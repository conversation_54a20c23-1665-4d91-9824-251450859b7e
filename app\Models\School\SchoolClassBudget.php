<?php

namespace App\Models\School;

use Illuminate\Database\Eloquent\Model;
use App\Models\v1\SubjectV1;
use App\User;


class SchoolClassBudget extends Model
{
    protected $fillable = [
        'school_id',
        'name',
        'requirement_type',
        'subject_id',
        'educator_profile',
        'special_education_certification',
        'delivery_mode',
        'years_of_experience',
        'instructional_days',
        'class_duration_hours',
        'non_instructional_hours',
        'language_of_instruction',
        'expected_class_size',
        'school_curriculum_provided',
        'calculated_budget',
        'tags',
        'qualification'
    ];

    protected $casts = [
        'special_education_certification' => 'boolean',
        'school_curriculum_provided' => 'boolean',
    ];

    // Handle tags as JSON, store in TEXT
    public function setTagsAttribute($value)
    {
        $this->attributes['tags'] = json_encode($value);
    }

    public function getTagsAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }
    
    public function subject()
    {
        return $this->belongsTo(SubjectV1::class, 'subject_id');
    }

    public function school()
    {
        return $this->belongsTo(User::class, 'school_id');
    }
}
