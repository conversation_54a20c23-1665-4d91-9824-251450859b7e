<style>
  body{
    font-family: sans-serif !important;
  }
  input.common__input.w-100.availability {
      padding: 15px;
      border: 2px solid black;
  }
  input.common__input.w-100.availability::placeholder {
      color: #000; /* true black */
      opacity: 1;
  }
  select.form-select.w-auto.contact_phone_country_code {
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      background-image: none !important;
      background: none;
      padding: 10px 20px;
      border: 2px solid black;
      border-radius: 50px;
      margin-right: 15px;
  }
  input.common__input.flex-grow-1.w-100.contact_phone_number {
      padding: 10px 20px;
      border: 2px solid black;
      border-radius: 50px;
  }
</style>
<?php echo csrf_field(); ?>

<?php
  $indexName = isset($index) ? "[{$index}]" : "";
?>

<div class="formGroupsContainer">
  <div class="position-relative">
    <?php $__currentLoopData = $fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <?php if($field['type'] === 'description'): ?>
        <div class="mb-3 fs-5 <?php if($field['options']['bold'] ?? false): ?> fw-bold <?php endif; ?>">
          <span <?php if(($field['options']['color'] ?? '') === 'primary'): ?> class="text-primary" <?php endif; ?>>
            <?php echo e($field['label']); ?>

          </span>
          <?php if($field['required']): ?>
            <span class="text-danger">*</span>
          <?php endif; ?>
        </div>
        <div class="mb-3 small"><?php echo $field['description']; ?></div>

      <?php elseif($field['type'] === 'separator'): ?>
        <hr class="my-4" />

      <?php elseif($field['type'] === 'view'): ?>
        <?php echo $__env->make($field['view'], $field['data'] ?? [], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

      <?php else: ?>
        <div class="mb-3">
          <?php if(!empty($field['label'])): ?>
            <label class="form-label"><?php echo e($field['label']); ?></label>
          <?php endif; ?>

          <?php switch($field['type']):

            case ('text'): ?>
              <input type="text"
                name="<?php echo e($field['name']); ?><?php echo e($indexName); ?>"
                value="<?php echo e(old($field['name'], $field['default'] ?? '')); ?>"
                class="common__input w-100 <?php echo e($field['name']); ?>" placeholder="<?php echo e($field['placeholder'] ?? ''); ?>"
                data-required="<?php echo e($field['required'] ? 'true' : 'false'); ?>"
                data-rules="<?php echo e(implode('|', $field['rules'] ?? [])); ?>">
              <?php break; ?>

            <?php case ('textarea'): ?>
              <textarea name="<?php echo e($field['name']); ?><?php echo e($indexName); ?>"
                class="common__input w-100"
                data-required="<?php echo e($field['required'] ? 'true' : 'false'); ?>"
                data-rules="<?php echo e(implode('|', $field['rules'] ?? [])); ?>"><?php echo e(old($field['name'], $field['default'] ?? '')); ?></textarea>
              <?php break; ?>

            <?php case ('date'): ?>
              <input type="text"
                name="<?php echo e($field['name']); ?><?php echo e($indexName); ?>"
                value="<?php echo e(old($field['name'], $field['default'] ?? '')); ?>"
                class="common__input datepicker w-100"
                data-required="<?php echo e($field['required'] ? 'true' : 'false'); ?>"
                data-rules="<?php echo e(implode('|', $field['rules'] ?? [])); ?>">
              <?php break; ?>

            <?php case ('time'): ?>
              <input type="text"
                name="<?php echo e($field['name']); ?><?php echo e($indexName); ?>"
                value="<?php echo e(old($field['name'], $field['default'] ?? '')); ?>"
                class="common__input timepicker w-100"
                data-required="<?php echo e($field['required'] ? 'true' : 'false'); ?>"
                data-rules="<?php echo e(implode('|', $field['rules'] ?? [])); ?>">
              <?php break; ?>

            <?php case ('select'): ?>
              <select name="<?php echo e($field['name']); ?><?php echo e($indexName); ?>"
                class="form-select w-100 select2"
                data-required="<?php echo e($field['required'] ? 'true' : 'false'); ?>"
                data-rules="<?php echo e(implode('|', $field['rules'] ?? [])); ?>">
                <?php $__currentLoopData = $field['options'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($key); ?>" <?php if(old($field['name'], $field['default']) == $key): ?> selected <?php endif; ?>>
                    <?php echo e($value); ?>

                  </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
              <?php break; ?>

            <?php case ('checkbox'): ?>
              <?php $__currentLoopData = $field['options'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="form-check">
                  <input type="checkbox"
                    name="<?php echo e($field['name']); ?>[<?php echo e($index ?? 0); ?>][]"
                    value="<?php echo e($key); ?>"
                    class="form-check-input"
                    data-required="<?php echo e($field['required'] ? 'true' : 'false'); ?>"
                    data-rules="<?php echo e(implode('|', $field['rules'] ?? [])); ?>">
                  <label class="form-check-label"><?php echo e($label); ?></label>
                </div>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              <?php break; ?>

            <?php case ('radio'): ?>
              <div class="d-flex gap-3 flex-wrap">
                <?php $__currentLoopData = $field['options'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <div class="form-check">
                    <input type="radio"
                      name="<?php echo e($field['name']); ?><?php if($index): ?>[<?php echo e($index ?? 0); ?>]<?php endif; ?>"
                      value="<?php echo e($key); ?>"
                      class="form-check-input"
                      <?php if(old($field['name'], $field['default'][0]) == $key): ?> checked <?php endif; ?>
                      data-required="<?php echo e($field['required'] ? 'true' : 'false'); ?>"
                      data-rules="<?php echo e(implode('|', $field['rules'] ?? [])); ?>">
                    <label class="form-check-label"><?php echo e($label); ?></label>
                  </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </div>
              <?php break; ?>

            <?php case ('phone'): ?>
              <?php
                $indexSuffix = isset($index) ? "[{$index}]" : "";
              ?>
              <div class="d-flex align-items-center">
                <select name="<?php echo e($field['name']); ?>_country_code<?php echo e($indexSuffix); ?>"
                  class="form-select w-auto <?php echo e($field['name']); ?>_country_code"
                  data-required="<?php echo e($field['required'] ? 'true' : 'false'); ?>"
                  data-rules="required">
                  <?php $__currentLoopData = $field['country_codes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($code); ?>" <?php if(old($field['name'].'.country_code', $field['default_country_code']) == $code): ?> selected <?php endif; ?>>
                      <?php echo e($label); ?> <?php echo e($code); ?>

                    </option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>

                <input type="tel"
                  name="<?php echo e($field['name']); ?>_number<?php echo e($indexSuffix); ?>"
                  value="<?php echo e(old($field['name'].'.number', $field['default'] ?? '')); ?>"
                  class="common__input flex-grow-1 w-100 <?php echo e($field['name']); ?>_number"
                  placeholder="Phone number"
                  data-required="<?php echo e($field['required'] ? 'true' : 'false'); ?>"
                  data-rules="<?php echo e(implode('|', $field['rules'] ?? [])); ?>">
              </div>
              <?php break; ?>

          <?php endswitch; ?>
        </div>
      <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
  </div>
</div>
<?php /**PATH D:\whizara\whizara\resources\views/components/dynamic-form-group.blade.php ENDPATH**/ ?>