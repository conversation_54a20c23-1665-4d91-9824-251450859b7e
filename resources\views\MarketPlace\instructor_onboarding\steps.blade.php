<section>
    <form id="availabilityScheduleForm" class="needs-validation d-none">
        <x-dynamic-form-group :fields="$config['availability']['fields']" />
    </form>
    
    <form id="addSubjectsForm" class="needs-validation d-none">
        <x-dynamic-form-group :fields="$config['subjects']['fields']" />
    </form>
    
    <form id="addSubstitutesForm" class="needs-validation d-none">
        <x-dynamic-form-group :fields="$config['substitutes']['fields']" />
    </form>
    
    <form id="addPhoneNumberForm" class="needs-validation d-none">
        <x-dynamic-form-group :fields="$config['phoneNumber']['fields']" />
    </form>
    
    <form id="notificationForm" class="needs-validation d-none">
        <x-dynamic-form-group :fields="$config['notifications']['fields']" />
    </form>
</section>


@section('scripts')
<script>
    window['onBoarding']['availability'] = @json($config['availability']['fields']);
    window['onBoarding']['subjects'] = @json($config['subjects']['fields']);
    window['onBoarding']['substitutes'] = @json($config['substitutes']['fields']);
    window['onBoarding']['phoneNumber'] = @json($config['phoneNumber']['fields']);
    window['onBoarding']['notifications'] = @json($config['notifications']['fields']);
</script>

<script>

  $(function () {
    const availabilityValidator = new DynamicFormValidator('#availabilityScheduleForm', window.onBoarding.availability);
    const subjectsValidator = new DynamicFormValidator('#addSubjectsForm', window.onBoarding.subjects);
    const substitutesValidator = new DynamicFormValidator('#addSubstitutesForm', window.onBoarding.substitutes);
    const phoneNumberValidator = new DynamicFormValidator('#addPhoneNumberForm', window.onBoarding.phoneNumber);
    const notificationsValidator = new DynamicFormValidator('#notificationForm', window.onBoarding.notifications);

    console.log(
        availabilityValidator,
        subjectsValidator,
        substitutesValidator,
        phoneNumberValidator,
        notificationsValidator
    )
  });
</script>

@endsection