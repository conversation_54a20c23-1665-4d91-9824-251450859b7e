<?php

namespace App;

use App\Models\InstructorBudgetApprovedModel;
use App\Models\OnboardingInstructorContract;
use App\Models\OnboardingInstructorMarketplaceContract;
use App\Models\V1\UserAdditionalSubject;
use App\Models\V1\UserAvailability;
use App\Models\V1\UserNotificationPreference;
use App\Models\V1\UserOnboardingFinalization;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;

class OnboardingInstructor extends Authenticatable
{
    use SoftDeletes;
    protected $table = 'new_onboarding_instructor';
    protected $fillable = [
        'user_status',
        'application_start_date',
        'onlinerate',
        'inpersonrate',
        'educator',
        'isDeclineContract',
        'seen_count',
        'is_background_check'
    ];

    public function googleMap()
    {
        return $this->hasOne(OnboardingIinstructorGoogleMapAddress::class, 'instructor_onboarding_id', 'id');
    }

    public function step1()
    {
        return $this->hasOne(InstructorFirstStepOnboardingModel::class, 'user_id', 'id');
    }

    public function step2()
    {
        return $this->hasOne(InstructorSecondStepOnboardingModel::class, 'user_id', 'id');
    }

    public function step3()
    {
        return $this->hasOne(InstructorThirdStepOnboardingModel::class, 'user_id', 'id');
    }

    public function step5()
    {
        return $this->hasOne(InstructorFifthStepOnboardingModel::class, 'user_id', 'id');
    }

    public function step6()
    {
        return $this->hasOne(InstructorSixthStepOnboardingModel::class, 'user_id', 'id');
    }

    public function step7()
    {
        return $this->hasOne(InstructorSeventhStepOnboardingModel::class, 'user_id', 'id');
    }

    public function rejectInstructor()
    {
        return $this->hasOne(RejectOnboardingInstructorModel::class, 'onboarding_instructor_id', 'id');
    }

    public function notifications()
    {
        return $this->hasMany(notification::class, 'user_id');
    }

    public function unreadNotifications()
    {
        return $this->notifications()->unread()->count();
    }

    public function shortList()
    {
        return $this->hasMany(ShortlistInstructorModel::class, 'user_id', 'id');
    }

    public function whizaraContract()
    {
        return $this->hasOne(OnboardingInstructorContract::class, 'user_id', 'id');
    }

    public function marketplaceContract()
    {
        return $this->hasOne(OnboardingInstructorMarketplaceContract::class, 'user_id', 'id');
    }


    public function category(){

        return $this->hasMany(PlatformInstructorCategory::class,"instructor_id","id");
    }

    public function approvedBudget()
    {
        return $this->hasMany(InstructorBudgetApprovedModel::class, 'user_id');
    }

    // public function availability()
    // {
    //     return $this->hasMany(UserAvailability::class, 'user_id');
    // }

    // public function additionalSubjects()
    // {
    //     return $this->hasMany(UserAdditionalSubject::class, 'user_id');
    // }

    // public function notificationPreference()
    // {
    //     return $this->hasMany(UserNotificationPreference::class, 'user_id');
    // }

    // public function onboardingFinalization()
    // {
    //     return $this->hasOne(UserOnboardingFinalization::class, 'user_id');
    // }
}
