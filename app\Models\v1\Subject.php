<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;

class Subject extends Model
{

    protected $table = 'subjects_v1';
    protected $fillable = ['subject_area_id', 'subject_code', 'title', 'description'];
    protected $primaryKey = 'id';

    // Define the relationship with subject_area_v1 table
    public function subjectArea()
    {
        return $this->belongsTo(SubjectArea::class, 'subject_area_id');
    }

    // Define the relationship with subject_budget_v1 table
    public function subjectBudget()
    {
        return $this->hasOne(SubjectBudget::class, 'subject_id');
    }
    public function budgets()
    {
        return $this->hasMany(SubjectBudget::class);
    }

    /**
     * Mutator to format title.
     * This will replace all '?' with '-' before saving.
     */
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = str_replace('?', '-', $value);
    }
}
