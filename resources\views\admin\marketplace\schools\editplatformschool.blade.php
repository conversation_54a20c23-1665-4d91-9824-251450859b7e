@extends('admin.layouts.master')

@section('title') Edit Platform School | Whizara @endsection

@section('content')
<main class="content">
    <div class="container-fluid p-0">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ url('admin-dashboard') }}" class="text-primary">{{ __('messages.dashboard') }}</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ url('admin/k12connections/manage-platform-schools') }}">{{ __('messages.list_platform_institute') }}</a>
                </li>
                <li class="breadcrumb-item active">Edit Platform School</li>
            </ol>
        </nav>

        <form method="post" enctype="multipart/form-data" id="edit_platform_institute_details">
            <div class="row justify-content-center">
                <div class="col-lg-2 col-md-3 col-6">
                    <div class="change_profile_img mx-auto mb-4">
                        <div class="avatar-edit">
                            <input type="file" name="profile_upload" class="profileImgUploadBtnd" id="imageUpload" accept=".png, .jpg, .jpeg" />
                            <label for="imageUpload"><span class="btn btn-primary btn-block dbdb">Upload Photo</span></label>
                        </div>
                        <div class="avatar-preview">
                            <div id="imagePreview" style="background-image: url('{{ $user_list[0]->profile ? asset($user_list[0]->profile) : default_user_placeholder() }}')"></div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-10 col-md-9">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header border-bottom"><h5 class="mb-0">{{ __('messages.institute_details') }}</h5></div>
                                <div class="card-body">

                                    {{-- Hidden Fields --}}
                                    <input type="hidden" name="latitude" class="form-control" id="latitude" value="{{ old('latitude', $user_list[0]->latitude ?? '') }}">
                                    <input type="hidden" name="longitude" class="form-control" id="longitude" value="{{ old('longitude', $user_list[0]->longitude ?? '') }}">
                                    <input type="hidden" name="user_id" value="{{$user_list[0]->id}}">

                                    <div class="row">
                                        {{-- School Name --}}
                                        <div class="col-md-4 form-group">
                                            <label class="form-label">School name</label>
                                            <select class="form-control select2" id="full_name" name="full_name">
                                                <option value="{{ $user_list[0]->full_name }}" selected>{{ $user_list[0]->full_name }}</option>
                                            </select>
                                            <span id="full_name_error" class="err"></span>
                                        </div>

                                        {{-- Other School Name --}}
                                        <div class="col-md-4 form-group {{ $user_list[0]->full_name == 'Other' ? '' : 'd-none' }}" id="other_full_name_section">
                                            <label class="form-label">School name</label>
                                            <input type="text" class="form-control" name="other_full_name" id="other_full_name" value="{{ old('other_full_name', $user_list[0]->other_full_name) }}">
                                            <span id="other_full_name_error" class="err"></span>
                                        </div>

                                        {{-- NCES Code --}}
                                        <div class="col-md-4 form-group">
                                            <label class="form-label">NCES School ID | NCES District ID</label>
                                            <input type="text" class="form-control" id="nces_cource_code" name="nces_cource_code" value="{{ old('nces_cource_code', $user_list[0]->nces_cource_code) }}">
                                            <span id="nces_cource_code_error" class="err"></span>
                                        </div>

                                        {{-- Organization Type --}}
                                        <div class="col-md-4 form-group">
                                            <label for="comment">Organization Type</label>
                                            <select class="form-control" id="organizationtype" name="organizationtype">
                                                <option value="">Select Organization Type</option>
                                                @foreach(['Public School', 'Charter school', 'Private school'] as $type)
                                                    <option value="{{ $type }}" {{ old('organizationtype', $user_list[0]->organizationtype) == $type ? 'selected' : '' }}>{{ $type }}</option>
                                                @endforeach
                                            </select>
                                            <span id="organizationtype_error" class="err"></span>
                                        </div>

                                        {{-- Address Fields --}}
                                        <div class="col-md-12 form-group">
                                            <label>Address</label>
                                            <textarea class="form-control" id="address" name="address">{{ old('address', $user_list[0]->address) }}</textarea>
                                            <span id="address_error" class="err"></span>
                                        </div>

                                        <div class="col-md-3 form-group">
                                            <input type="text" id="city" class="form-control" name="city" placeholder="City" value="{{ old('city', $user_list[0]->city) }}">
                                            <span id="p_city_error" class="err"></span>
                                        </div>

                                        <div class="col-md-3 form-group">
                                            <input type="text" id="state" class="form-control" name="state" placeholder="State" value="{{ old('state', $user_list[0]->state) }}">
                                            <span id="p_state_error" class="err"></span>
                                        </div>

                                        <div class="col-md-3 form-group">
                                            <input type="text" id="zipcode" class="form-control" name="zipcode" placeholder="Zipcode" value="{{ old('zipcode', $user_list[0]->zipcode) }}">
                                            <span id="p_zipcode_error" class="err"></span>
                                        </div>

                                        <div class="col-md-3 form-group">
                                            <input type="text" id="country" class="form-control" name="country" placeholder="County Name" value="{{ old('country', $user_list[0]->country) }}">
                                            <span id="p_country_error" class="err"></span>
                                        </div>

                                        {{-- Website --}}
                                        <div class="col-md-4 form-group">
                                            <label class="form-label">Website</label>
                                            <input type="text" class="form-control" name="website" placeholder="Website" id="website" value="{{ old('website', $user_list[0]->website_url) }}">
                                            <span id="website_error" class="err"></span>
                                        </div>

                                        {{-- Phone --}}
                                        <div class="col-md-4 form-group">
                                            <label class="form-label">Phone</label>
                                            <input type="text" class="form-control" name="school_phone" placeholder="Phone" id="school_phone" value="{{ old('phone_number', $user_list[0]->phone_number) }}">
                                            <span id="phone_error" class="err"></span>
                                        </div>

                                        {{-- Email --}}
                                        <div class="col-md-4 form-group">
                                            <label class="form-label">Email</label>
                                            <input type="text" class="form-control" name="email" placeholder="Email" id="email" value="{{ old('email', $user_list[0]->email) }}">
                                            <span id="email_error" class="err"></span>
                                        </div>

                                        {{-- Optional Fields --}}
                                        <div class="col-md-4 form-group">
                                            <label class="form-label">School Rating</label>
                                            <input type="text" class="form-control" name="school_rating" placeholder="School Rating" value="{{ old('school_rating', $user_list[0]->school_rating) }}">
                                        </div>

                                        <div class="col-md-4 form-group">
                                            <label class="form-label">Enrollment</label>
                                            <input type="number" class="form-control" name="enrollment" placeholder="Enrollment count" value="{{ old('enrollment', $user_list[0]->enrollment) }}">
                                        </div>

                                        <div class="col-md-4 form-group">
                                            <label for="comment">Grade levels</label>
                                            @php $selectedGrades = explode(',', $user_list[0]->grade); @endphp
                                            <select class="form-control select2" id="grade" name="grade[]" multiple data-placeholder="Select grade levels">
                                                @foreach($gradeLavel as $data)
                                                    <option value="{{ $data->id }}" {{ in_array($data->id, $selectedGrades) ? 'selected' : '' }}>{{ $data->class_name }}</option>
                                                @endforeach
                                            </select>
                                            <span id="grade_error" class="err"></span>
                                        </div>

                                        <div class="col-md-4 form-group">
                                            <label class="form-label">Locale</label>
                                            <input type="text" class="form-control" name="locale" placeholder="Locale" value="{{ old('locale', $user_list[0]->locale) }}">
                                        </div>

                                        <div class="col-md-4 form-group">
                                            <label class="form-label">Timezone</label>
                                            <input type="text" class="form-control" name="timezone" placeholder="Timezone" value="{{ old('timezone', $user_list[0]->timezone) }}">
                                        </div>

                                        {{-- Contact Info --}}
                                        <div class="col-lg-12"><label for="comment">Contact info</label></div>
                                        @foreach($school_contact_info as $index => $info)
                                            <div class="row after-add-more" style="margin-left:0 !important;">
                                                <div class="col-md-2 form-group">
                                                    <input type="text" class="form-control" name="job_title[]" placeholder="Job Title" value="{{ $info->job_title }}">
                                                </div>
                                                <div class="col-md-2 form-group">
                                                    <input type="text" class="form-control" name="first_name[]" placeholder="First name" value="{{ $info->first_name }}">
                                                </div>
                                                <div class="col-md-2 form-group">
                                                    <input type="text" class="form-control" name="last_name[]" placeholder="Last name" value="{{ $info->last_name }}">
                                                </div>
                                                <div class="col-md-3 form-group">
                                                    <input type="text" class="form-control" name="cemail[]" placeholder="Email" value="{{ $info->email }}">
                                                </div>
                                                <div class="col-md-2 form-group">
                                                    <input type="number" class="form-control" name="phone[]" placeholder="Phone" value="{{ $info->phone }}">
                                                </div>
                                                <div class="col-md-1 form-group change">
                                                    @if($index == 0)
                                                        <a class="btn btn-success add-more" onclick="add_more()">+</a>
                                                    @else
                                                        <a class="btn btn-danger remove">-</a>
                                                    @endif
                                                </div>
                                            </div>
                                        @endforeach

                                        {{-- Buttons --}}
                                        <div class="col-md-12 d-flex justify-content-md-end justify-content-between mt-3">
                                            <a href="{{ redirect()->getUrlGenerator()->previous() }}" class="btn btn-secondary mr-2">{{ __('messages.cancel') }}</a>
                                            <button type="submit" class="btn btn-primary">{{ __('messages.update') }}</button>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</main>
@endsection

@section('scripts')
<script>
    function add_more() {
        var html = `<div class="row after-add-more" style="margin-left:0 !important;">
                        <div class="col-md-2 form-group">
                            <input type="text" class="form-control" name="job_title[]" id="first_name" placeholder="Job Title">
                            <span id="name_error" class="err"></span>
                        </div>

                        <div class="col-md-2 form-group">
                            <input type="text" class="form-control" name="first_name[]" id="first_name" placeholder="First name">
                            <span id="name_error" class="err"></span>
                        </div>

                        <div class="col-md-2 form-group">
                            <input type="text" id="last_name" class="form-control" name="last_name[]" placeholder="{{ __('messages.last_name') }}">
                            <span id="p_last_name_error" class="err"></span>
                        </div>

                        <div class="col-md-3 form-group">
                            <input type="text" class="form-control" name="cemail[]"  placeholder="Email">
                            <span id="email_errors" class="err"></span>
                        </div>

                        <div class="col-md-2 form-group">
                            <input type="number" class="form-control" name="phone[]" id="phone" placeholder="Phone">
                            <span id="phone_error" class="err"></span>
                        </div>

                        <div class="col-md-1 form-group change">
                            <a class='btn btn-danger remove'>-</a>
                        </div>
                    </div>`;

        $(".after-add-more").last().after(html);
        $(html).find(".change").html("<a class='btn btn-danger remove'>-</a>");
    }
    $("body").on("click",".remove",function(){
        $(this).parents(".after-add-more").remove();
    });
</script>

<script>
    $(document).ready(function () {
        let schoolData = [];
        let debounceTimer = null;

        const $dropdown = $('#full_name');
        const $otherInput = $('#other_full_name');
        const $otherSection = $('#other_full_name_section');
        const $gradeSelect = $('#grade');

        const gradeMap = {
            G_PK_OFFERED: 'Tk',   // Mapping "Pre-K" to "Tk"
            G_KG_OFFERED: 'K',
            G_1_OFFERED: '1',
            G_2_OFFERED: '2',
            G_3_OFFERED: '3',
            G_4_OFFERED: '4',
            G_5_OFFERED: '5',
            G_6_OFFERED: '6',
            G_7_OFFERED: '7',
            G_8_OFFERED: '8',
            G_9_OFFERED: '9',
            G_10_OFFERED: '10',
            G_11_OFFERED: '11',
            G_12_OFFERED: '12'
        };

        function handleSchoolSelection() {
            const selectedName = $dropdown.val();
            if (selectedName.toLowerCase() === 'other') {
                $otherSection.removeClass('d-none');
                $('#nces_cource_code').val('');
                $gradeSelect.val(null).trigger('change');
            } else {
                $otherSection.addClass('d-none');
                const selectedSchool = schoolData.find(s => s.SCH_NAME === selectedName);
                if (selectedSchool) {
                    $('#website').val(selectedSchool.WEBSITE || '');
                    $('#phone').val(selectedSchool.PHONE || '');
                    $('#address').val(selectedSchool.LSTREET1 || selectedSchool.LSTREET2 || selectedSchool.LSTREET3 || '');
                    $('#city').val(selectedSchool.LCITY || '');
                    $('#state').val(selectedSchool.STATENAME || '');
                    $('#zipcode').val(selectedSchool.LZIP || '');
                    $('#nces_cource_code').val(selectedSchool.NCESSCH || '');

                    // ===== Grade Level Auto Selection =====
                    let selectedGrades = [];
                    for (const [apiKey, label] of Object.entries(gradeMap)) {
                        if (selectedSchool[apiKey] === 'Yes') {
                            const matchingOption = $gradeSelect.find('option').filter(function () {
                                return $(this).text().trim() === label;
                            }).val();
                            if (matchingOption) {
                                selectedGrades.push(matchingOption);
                            }
                        }
                    }
                    $gradeSelect.val(selectedGrades).trigger('change');
                }
            }
        }

        // === select2 for full_name with dynamic search ===
        $dropdown.select2({
            placeholder: 'Type at least 4 characters to search',
            minimumInputLength: 4,
            ajax: {
                delay: 500,
                transport: function (params, success, failure) {
                    const searchTerm = params.data.q || '';
                    if (searchTerm.length < 4) {
                        success({ results: [] });
                        return;
                    }
                    $.ajax({
                        url: `https://app.whizara.com/node/nces/schools?SCH_NAME=${encodeURIComponent(searchTerm)}`,
                        method: 'GET',
                        dataType: 'json',
                        success: function (response) {
                            schoolData = response;
                            let results = response.map(school => {
                                const schoolType = school.CHARTER_TEXT?.toLowerCase() === 'no' ? 'Public' : 'Independent';
                                const street = school.LSTREET1 || school.LSTREET2 || school.LSTREET3 || '';
                                const label = `${school.SCH_NAME} - ${schoolType}, ${street} (${school.LCITY}, ${school.LSTATE})`;
                                return { id: school.SCH_NAME, text: label };
                            });
                            results.push({ id: 'Other', text: 'Other' });
                            success({ results });
                        },
                        error: function (xhr, status, error) {
                            console.error("Request error:", status, error);
                            failure();
                        }
                    });
                },
                processResults: function (data) {
                    return data;
                }
            },
            language: {
                inputTooShort: function (args) {
                    const remaining = 4 - args.input.length;
                    return `Please enter ${remaining} more character${remaining > 1 ? 's' : ''}`;
                },
                noResults: function () {
                    return 'No results found';
                }
            },
            escapeMarkup: function (markup) {
                return markup;
            }
        });
        $dropdown.on('change', handleSchoolSelection);
        if ($.fn.select2) {
            $gradeSelect.select2();
        }
    });
</script>
@endsection