class DynamicFormValidator {
  constructor(formSelector, config = {}, apiData = {}) {
    this.$form = $(formSelector);
    this.config = config;
    this.apiData = apiData;
    this.rules = {};
    this.messages = {};

    this.applyConfigValues();
    this.assignApiDataValues();
    this.initPlugins();
    this.buildValidationRules();
    this.initValidator();
    this.setupDynamicHandlers();
  }

  applyConfigValues() {
    const self = this;
    Object.keys(this.config).forEach(fieldName => {
      const fieldConfig = self.config[fieldName];
      const $field = self.$form.find(`[name="${fieldName}[]"], [name="${fieldName}"]`);
      if (!$field.length) return;

      $field.each(function () {
        $(this).data('required', fieldConfig.required);
        $(this).data('rules', (fieldConfig.rules || []).join('|'));
      });
    });
  }

  assignApiDataValues() {
    const self = this;
    Object.keys(this.apiData).forEach(fieldName => {
      const $field = self.$form.find(`[name="${fieldName}[]"], [name="${fieldName}"]`);
      if (!$field.length) return;

      if ($field.is(':checkbox')) {
        const values = Array.isArray(self.apiData[fieldName]) ? self.apiData[fieldName] : [self.apiData[fieldName]];
        values.forEach(val => {
          $field.filter(`[value="${val}"]`).prop('checked', true);
        });
      } else if ($field.is(':radio')) {
        $field.filter(`[value="${self.apiData[fieldName]}"]`).prop('checked', true);
      } else {
        $field.each(function () {
          if ($(this).hasClass('select2')) {
            $(this).val(self.apiData[fieldName]).trigger('change');
          } else if ($(this).hasClass('datepicker')) {
            $(this).datepicker('update', self.apiData[fieldName]);
          } else if ($(this).hasClass('timepicker')) {
            $(this).timepicker('setTime', self.apiData[fieldName]);
          } else {
            $(this).val(self.apiData[fieldName]);
          }
        });
      }
    });
  }

  initPlugins() {
    this.$form.find('select.select2').each(function () {
      $(this).select2({
        theme: 'bootstrap4',
        width: 'style',
        placeholder: 'Select certificate/license type',
        allowClear: Boolean($(this).data('allow-clear')),
      });
    });
    this.$form.find('input.datepicker').each(function () {
      $(this).datepicker({ format: 'yyyy-mm-dd', autoclose: true, todayHighlight: true });
    });
    this.$form.find('input.timepicker').each(function () {
      $(this).timepicker({ timeFormat: 'HH:mm', interval: 30, dropdown: true, scrollbar: true });
    });
  }

  buildValidationRules() {
    const self = this;
    this.$form.find('input, select, textarea').each(function () {
      const $field = $(this);
      const name = $field.attr('name').replace('[]', '');
      if (!name) return;
      const required = $field.data('required') === true || $field.data('required') === 'true';
      const rulesString = $field.data('rules') || '';

      if (!self.rules[name]) {
        self.rules[name] = {};
      }
      if (required) {
        self.rules[name]['required'] = true;
      }

      if (rulesString) {
        const rulesArray = rulesString.split('|');
        rulesArray.forEach(rule => {
          if (rule.startsWith('min:')) {
            self.rules[name]['minlength'] = parseInt(rule.split(':')[1]);
          }
          if (rule.startsWith('max:')) {
            self.rules[name]['maxlength'] = parseInt(rule.split(':')[1]);
          }
          if (rule === 'email') {
            self.rules[name]['email'] = true;
          }
        });
      }

      self.messages[name] = {
        required: `${name} is required.`,
        email: `Please enter a valid email.`,
      };
    });
  }

  initValidator() {
    const self = this;
    this.validator = this.$form.validate({
      rules: self.rules,
      messages: self.messages,
      errorElement: 'div',
      errorClass: 'invalid-feedback',
      highlight: function (element) {
        $(element).addClass('is-invalid');
      },
      unhighlight: function (element) {
        $(element).removeClass('is-invalid');
      },
      errorPlacement: function (error, element) {
        if (element.is(':checkbox') || element.is(':radio')) {
          error.appendTo(element.closest('.form-group, .form-check'));
        } else {
          error.insertAfter(element);
        }
      },
      submitHandler: function (form) {
        console.log('Form is valid. Submitting...');
        form.submit();
      }
    });
  }

  setupDynamicHandlers() {
    const self = this;
    this.$form.find('select[name="country[]"]').on('change', function () {
      const countryVal = $(this).val();
      const $apptField = self.$form.find('[name="appointment_time[]"]');
      if (countryVal === 'US') {
        $apptField.rules('add', { required: true });
      } else {
        $apptField.rules('remove', 'required');
      }
    });
  }

  addFieldValidation($field) {
    const name = $field.attr('name').replace('[]', '');
    const required = $field.data('required') === true || $field.data('required') === 'true';
    const rulesString = $field.data('rules') || '';

    const newRules = {};
    if (required) {
      newRules['required'] = true;
    }

    if (rulesString) {
      const rulesArray = rulesString.split('|');
      rulesArray.forEach(rule => {
        if (rule.startsWith('min:')) {
          newRules['minlength'] = parseInt(rule.split(':')[1]);
        }
        if (rule.startsWith('max:')) {
          newRules['maxlength'] = parseInt(rule.split(':')[1]);
        }
        if (rule === 'email') {
          newRules['email'] = true;
        }
      });
    }

    $field.rules('add', newRules);
  }

  initPluginsForNewField($field) {
    if ($field.hasClass('select2')) {
      $field.select2({
        theme: 'bootstrap4',
        width: 'style',
        placeholder: 'Select certificate/license type',
        allowClear: Boolean($field.data('allow-clear')),
      });
    }
    if ($field.hasClass('datepicker')) {
      $field.datepicker({ format: 'yyyy-mm-dd', autoclose: true, todayHighlight: true });
    }
    if ($field.hasClass('timepicker')) {
      $field.timepicker({ timeFormat: 'HH:mm', interval: 30, dropdown: true, scrollbar: true });
    }
  }
}

window.DynamicFormValidator = DynamicFormValidator;