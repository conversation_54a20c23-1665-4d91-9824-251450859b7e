<?php

namespace App\Http\Controllers\WEB;
use App\Http\Controllers\Controller;
use DB;
use Validator;
use Session;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\User;
use App\UserFirstStepModel;
use App\UserSecondStepModel;
use App\StateModel;
use App\UserSubjectsModel;
use App\UserThirdStepModel;
use App\UserFourthStepModel;
use App\GradeLevelModel;
use App\Classes;
use App\Subject;
use App\AssessmentsModel;
use App\UserQuizModel;
use App\QuestionsModel;
use App\EducationListModel;
use App\user_interview_slots;
use App\user_references;
use App\SettingTermsModel;
use App\user_contract;
use App\ProfileStatusHistoryModel;
use App\SubsubjectModel;
use App\UserEducationModel;
use App\certifications;
use App\InvitationModel;
use App\invite_application_recruiter;
use App\LastChatModel;
use Barryvdh\DomPDF\Facade\Pdf;
use App\link;
use App\Users;
use Carbon\Carbon;
use Hash;
use Mail;
use Auth;
DB::enableQueryLog();
use Crypt;
use Exception;
use PhpParser\Node\Stmt\TryCatch;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Mail as FacadesMail;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\Hash as FacadesHash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session as FacadesSession;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function login()
    {
        if(Auth::user() && Auth::user()->type==5) {

            return redirect("/web-dashboard");
        }
        return view("web.auth.login");
    }

    public function signup()
    {
        if(Auth::user() && Auth::user()->type==5) {

            return redirect("/web-dashboard");
        }
        return view("web.auth.signup");
    }

    public function signinemail()
    {
        if(Auth::user() && Auth::user()->type==5) {

            return redirect("/web-dashboard");
        }
        return view("web.auth.signinemail");
    }
    public function reset_passwords($id)
    {
        $data["id"] = decrypt($id);
        $token = link::where('user_id','=',decrypt($id))
        ->where('created_at','>',Carbon::now()->subHours(24))
        ->where("link_type", "=", 'password')
        ->first();
        if($token){

            return view("web.auth.resetpassword")->with($data);
        }else{
            print_r('The password reset link has expired.');
        }


    }

    public function signupemail()
    {
        if(Auth::user() && Auth::user()->type==5) {

            return redirect("/web-dashboard");
        }
        return view("web.auth.signupemail");
    }

    public function submit()
    {
        return redirect("/web-dashboard");

    }

    public function logout()
    {
        Auth::logout();
        Session::forget('userewlogin');
        return redirect("/sign-in");
    }

    public function verify()
    {
        if (isset(Auth::user()->id) && Auth::user()->type==5) {
        } else {
            return redirect("/");
        }
        $user = User::where(["id" => Auth::user()->id])->first();
        if ($user) {
            if ($user["email_verify_status"] == "1") {
                $user_data = [
                    "email" => $user["email"],
                    "password" => $user["password"],
                    "type" => "5",
                ];
                if (Auth::attempt($user_data)) {
                    $request
                        ->session()
                        ->put("userewlogin", [
                            "id" => Auth::user()->id,
                            "name" => Auth::user()->first_name,
                            "email" => Auth::user()->email,
                            "type" => Auth::user()->type,
                        ]);
                }
                $data["id"] = $user["id"];
                return redirect("/onboarding-step/" . encrypt($user["id"]));
            } else {
                return view("web.auth.verify");
            }
        }
        return view("web.auth.verify");
    }

    public function pendingverify()
    {
        if (isset(Auth::user()->id) && Auth::user()->type==5) {
        } else {
            return redirect("/");
        }
        $user = User::where(["id" => Auth::user()->id])->first();
        if ($user) {
            if ($user["email_verify_status"] == "1") {
                $user_data = [
                    "email" => $user["email"],
                    "password" => $user["assword"],
                    "type" => "5",
                ];
                if (Auth::attempt($user_data)) {
                    $request
                        ->session()
                        ->put("userewlogin", [
                            "id" => Auth::user()->id,
                            "name" => Auth::user()->first_name,
                            "email" => Auth::user()->email,
                            "type" => Auth::user()->type,
                        ]);
                }
                $data["id"] = $user["id"];
                return redirect("/onboarding-step/" . encrypt($user["id"]));
            } else {
                return view("web.auth.pendingverify");
            }
        }
        return view("web.auth.pendingverify");
    }

    public function viewProfile()
    {
        if (isset(Auth::user()->id) && Auth::user()->type==5) {
        } else {
            return redirect("/");
        }
        $user = User::where(["id" => Auth::user()->id])->first();
        if ($user) {
            if (
                $user["email_verify_status"] == "1" &&
                $user["profile_status"] == 0
            ) {
                $user_data = [
                    "email" => $user["email"],
                    "password" => $user["password"],
                    "type" => "5",
                ];
                if (Auth::attempt($user_data)) {
                    $request
                        ->session()
                        ->put("userewlogin", [
                            "id" => Auth::user()->id,
                            "name" => Auth::user()->first_name,
                            "email" => Auth::user()->email,
                            "type" => Auth::user()->type,
                        ]);
                }
                $data["id"] = $user["id"];
                return redirect("/onboarding-step/" . encrypt($user["id"]));
            } elseif ($user["email_verify_status"] == "0") {
                return redirect("verify");
            } elseif ($user["profile_status"] == "1") {
                return redirect("/onboarding-step/" . encrypt($user["id"]));
            } elseif (
                $user["profile_status"] == 2 ||
                $user["profile_status"] == 10 ||
                $user["profile_status"] == 3 ||
                $user["profile_status"] == 5 ||
                $user["profile_status"] == 13
            ) {
                return redirect("submit");
            } elseif (
                $user["profile_status"] == 12 ||
                $user["profile_status"] == 4 ||
                $user["profile_status"] == 8 ||
                $user["profile_status"] == 8
            ) {
                return redirect("onboarding-details");
            }
        }
        return view("web.onboarding.submit");
    }

    public function forgotpassword()
    {
        return view("web.auth.forgotpassword");
    }

    public function subject_get(Request $request)
    {
        $id = $request->id;
        $data["subject"] = Subject::whereIn("grade_level", $id)->get();
        return view("web.onboarding.subject")->with($data);
    }

    public function onboarding(Request $request,$id)
    {
        if(empty(Auth::user()->id) && Auth::user()->type==5){
            return redirect("/");
        }

        if(Auth::user()->id != decrypt($id)){
            return redirect("/");
        }

        $data["id"] = decrypt($id);
        $id = decrypt($id);
        User::where("id", $id)->update(["email_verify_status" => 1]);
        $user = User::where("id", $id)->first();
        if(empty($user) ){
            return redirect("/");
        }
        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        $data["grade"] = GradeLevelModel::get();
        $data["subject"] = Subject::orderBy("subject_name", "asc")->get();
        $data["subjectdata"] = Subject::orderBy("subject_name", "asc")->get();
        $data["question"] = QuestionsModel::orderBy("question_id", "asc")->get();
        $data["certifications"] = certifications::where(["status" => 1])->orderBy("name", "asc")->get();
        $data["class"] = Classes::get();

        $data["first"] = UserFirstStepModel::where(["user_id" => $id])->first();
        $data["second"] = UserSecondStepModel::where([
            "user_id" => $id,
        ])->first();
        $data["third"] = UserThirdStepModel::where(["user_id" => $id])->first();
        $data["four"] = UserFourthStepModel::where(["user_id" => $id])->first();
        $data["intro"] = AssessmentsModel::where([
            "user_id" => $id,
            "type" => "introduction",
        ])->first();
        $data["teachings"] = AssessmentsModel::where([
            "user_id" => $id,
            "type" => "teaching",
        ])
            ->orderBy("id", "DESC")
            ->get();
        $data["classroom"] = AssessmentsModel::where([
            "user_id" => $id,
            "type" => "classroom",
        ])->get();

        $data["five"] = AssessmentsModel::where(["user_id" => $id])->first();
        $data["quiz"] = UserQuizModel::where(["user_id" => $id])->first();
        $data["education_list"] = EducationListModel::get();
        $data["user_education"] = UserEducationModel::where([
            "user_id" => $id,
        ])->get();
        $data["user_references"] = user_references::where([
            "user_id" => $id,
        ])->get();
        if ($user) { $user_data = [
                "email" => $user["email"]
            ];

            $newtimestamp = strtotime($user->email_verify_time . "+ 10 minute");
            $time = date("Y-m-d H:i:s", $newtimestamp);

        }
        if (
            $user["email_verify_status"] == "1" &&
            $user["profile_status"] == 0
        ) {
            return view("web.onboarding.onboarding-step")->with($data);
        } elseif ($user["profile_status"] == 1) {
            return view("web.onboarding.onboarding-step")->with($data);
        } elseif ($user["profile_status"] == 2) {
            return view("web.user.status.submit")->with($data);
        } elseif ($user["profile_status"] == 18) {
            return view("web.user.status.resubmitted")->with($data);
        } elseif ($user["profile_status"] == 19) {
            return view("web.user.status.contractsubmitted")->with($data);
        } elseif ($user["profile_status"] == 3) {
            return view("web.user.status.underreview")->with($data);
        } elseif ($user["profile_status"] == 4) {
            return redirect("/web-dashboard");
        } elseif ($user["profile_status"] == 5) {
            return view("web.user.status.preonboardedpending")->with($data);
        } elseif ($user["profile_status"] == 6) {
            $prvaluec=explode(",",$user->teach);

            if($user->is_approved){
                if($user->is_approved==20){
                $data["setting_terms"] = SettingTermsModel::where([
                    "status" => 1,"type"=>"hybrid contract"
                ])->first();
                }

                if($user->is_approved==16){
                    $data["setting_terms"] = SettingTermsModel::where([
                        "status" => 1,"type"=>"Online contract"
                    ])->first();
                    }

                    if($user->is_approved==17){
                        $data["setting_terms"] = SettingTermsModel::where([
                            "status" => 1,"type"=>"In person contract"
                        ])->first();
                        }
                    }else{
                    if(in_array("hybrid",$prvaluec)){
                        $data["setting_terms"] = SettingTermsModel::where([
                            "status" => 1,"type"=>"hybrid contract"
                        ])->first();
                    }else{

                        if(in_array("online",$prvaluec) && in_array("in-person",$prvaluec)){
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"hybrid contract"
                            ])->first();
                        }elseif(in_array("online",$prvaluec)){
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"Online contract"
                            ])->first();
                        }elseif(in_array("in-person",$prvaluec)){
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"In person contract"
                            ])->first();
                        }else{
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"hybrid contract"
                            ])->first();
                        }

                    }

                }

                    $body =  $data["setting_terms"]->description;

                    if($user["last_name"]){
                        $full_name=$user["first_name"].' '.$user["last_name"];
                    }else{
                        $full_name=$user["first_name"];
                    }

                    $body= str_replace('{{NAME}}', $full_name, $body);
                    $body= str_replace('{{DATE}}', date('F d, Y'), $body);
                    $body = str_replace('{{IN_PERSON_RATE}}', $user["inpersonrate"], $body);
                    $body = str_replace('{{ONLINE_RATE}}', $user["onlinerate"], $body);
                    $body = str_replace('{{CITY}}', $user["city"], $body);
                    $body = str_replace('{{STATE}}', $user["state"], $body);
                    $body = str_replace('{{ZIPCODE}}', $user["zipcode"], $body);
                       $data['contract']=$body;
            return view("web.user.status.contractpending")->with($data);
        } elseif ($user["profile_status"] == 7) {
            return view("web.user.status.onboardedpending")->with($data);
        } elseif ($user["profile_status"] == 8) {
            return view("web.dashboard.index")->with($data);
        } elseif ($user["profile_status"] == 9) {
            return redirect("/web-dashboard");
            // return view("web.user.status.interviewscheduled")->with($data);
        } elseif ($user["profile_status"] == 10) {
            $data["notes"] = ProfileStatusHistoryModel::where([
                "user_id" => $id,"status"=>10
            ])->orderBy("id", "DESC")->first();
            return view("web.user.status.resubmit")->with($data);
        } elseif ($user["profile_status"] == 11) {
            return redirect("/web-dashboard");

        } elseif ($user["profile_status"] == 12) {
            return redirect("/web-dashboard");
        } elseif ($user["profile_status"] == 13) {
            return view("web.user.status.reject")->with($data);
        } elseif (
            $user["profile_status"] == 14 ||
            $user["profile_status"] == 15 ||
            $user["profile_status"] == 17 ||
            $user["profile_status"] == 20 ||
            $user["profile_status"] == 16
        ) {
            return redirect("/web-dashboard");
        }elseif ($user["profile_status"] == 22) {
            return view("web.user.status.transcript_submitted")->with($data);
        } else {
            return redirect("/");
        }
    }

    public function reonboarding(Request $request,$id)
    {
        if(!session('userewlogin')) {
            return redirect("/");
        }
        User::where("id", $id)->update(["email_verify_status" => 1]);
        $data["id"] = decrypt($id);
        $data["state"] = StateModel::where(["country_id" => "239"])->get();
        $data["grade"] = GradeLevelModel::get();
        $data["subject"] = Subject::orderBy("subject_name", "asc")->get();
        $data["subjectdata"] = Subject::orderBy("subject_name", "asc")->get();
        $data["certifications"] = certifications::where(["status" => 1])->orderBy("name", "asc")->get();
        $data["question"] = QuestionsModel::orderBy("question_id", "asc")->get();
        $data["class"] = Classes::get();
        $id = decrypt($id);

        $user = User::where("id", $id)->first();
        if(empty($user)){
            return redirect("/");
        }
        $data["first"] = UserFirstStepModel::where(["user_id" => $id])->first();
        $data["second"] = UserSecondStepModel::where([
            "user_id" => $id,
        ])->first();
        $data["third"] = UserThirdStepModel::where(["user_id" => $id])->first();
        $data["four"] = UserFourthStepModel::where(["user_id" => $id])->first();
        $data["intro"] = AssessmentsModel::where([
            "user_id" => $id,
            "type" => "introduction",
        ])->first();
        $data["teachings"] = AssessmentsModel::where([
            "user_id" => $id,
            "type" => "teaching",
        ])
            ->orderBy("id", "DESC")
            ->get();
        $data["classroom"] = AssessmentsModel::where([
            "user_id" => $id,
            "type" => "classroom",
        ])->get();

        $data["five"] = AssessmentsModel::where(["user_id" => $id])->first();
        $data["quiz"] = UserQuizModel::where(["user_id" => $id])->first();
        $data["education_list"] = EducationListModel::get();
        $data["user_education"] = UserEducationModel::where([
            "user_id" => $id,
        ])->get();
        $data["user_references"] = user_references::where([
            "user_id" => $id,
        ])->get();



        if (
            $user["email_verify_status"] == "1" &&
            $user["profile_status"] == 0
        ) {
            return view("web.onboarding.onboarding-step")->with($data);
        } elseif ($user["profile_status"] == 1) {
            return view("web.onboarding.onboarding-step")->with($data);
        } elseif ($user["profile_status"] == 2) {
            return view("web.user.status.submit")->with($data);
        } elseif ($user["profile_status"] == 18) {
            return view("web.user.status.resubmitted")->with($data);
        } elseif ($user["profile_status"] == 19) {
            return view("web.user.status.contractsubmitted")->with($data);
        } elseif ($user["profile_status"] == 3) {
            return view("web.user.status.underreview")->with($data);
        } elseif ($user["profile_status"] == 4) {
            return redirect("/web-dashboard");
        } elseif ($user["profile_status"] == 5) {
            return view("web.onboarding.onboarding-step")->with($data);

        } elseif ($user["profile_status"] == 6) {
            $prvaluec=explode(",",$user->teach);
            if($user->is_approved){
                if($user->is_approved==20){
                $data["setting_terms"] = SettingTermsModel::where([
                    "status" => 1,"type"=>"hybrid contract"
                ])->first();
                }

                if($user->is_approved==16){
                    $data["setting_terms"] = SettingTermsModel::where([
                        "status" => 1,"type"=>"Online contract"
                    ])->first();
                    }

                    if($user->is_approved==17){
                        $data["setting_terms"] = SettingTermsModel::where([
                            "status" => 1,"type"=>"In person contract"
                        ])->first();
                        }
                    }else{
                    if(in_array("hybrid",$prvaluec)){
                        $data["setting_terms"] = SettingTermsModel::where([
                            "status" => 1,"type"=>"hybrid contract"
                        ])->first();
                    }else{

                        if(in_array("online",$prvaluec) && in_array("in-person",$prvaluec)){
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"hybrid contract"
                            ])->first();
                        }elseif(in_array("online",$prvaluec)){
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"Online contract"
                            ])->first();
                        }elseif(in_array("in-person",$prvaluec)){
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"In person contract"
                            ])->first();
                        }else{
                            $data["setting_terms"] = SettingTermsModel::where([
                                "status" => 1,"type"=>"hybrid contract"
                            ])->first();
                        }

                    }

                }

            $body =  $data["setting_terms"]->description;

            if($user["last_name"]){
                $full_name=$user["first_name"].' '.$user["last_name"];
            }else{
                $full_name=$user["first_name"];
            }

            $body= str_replace('{{NAME}}', $full_name, $body);

            $body= str_replace('{{DATE}}', date('F d, Y'), $body);
            $body = str_replace('{{IN_PERSON_RATE}}', $user["inpersonrate"], $body);
            $body = str_replace('{{ONLINE_RATE}}', $user["onlinerate"], $body);
            $body = str_replace('{{CITY}}', $user["city"], $body);
            $body = str_replace('{{STATE}}', $user["state"], $body);
            $body = str_replace('{{ZIPCODE}}', $user["zipcode"], $body);
               $data['contract']=$body;
            return view("web.user.status.contractpending")->with($data);
        } elseif ($user["profile_status"] == 7) {
            return view("web.user.status.onboardedpending")->with($data);
        } elseif ($user["profile_status"] == 8) {
            return view("web.user.status.onboardedready")->with($data);
        } elseif ($user["profile_status"] == 9) {
            return view("web.user.status.interviewscheduled")->with($data);
        } elseif ($user["profile_status"] == 10) {
            $data["notes"] = ProfileStatusHistoryModel::where([
                "user_id" => $id,"status"=>10
            ])->orderBy("id", "DESC")->first();
            return view("web.onboarding.onboarding-step")->with($data);
        } elseif ($user["profile_status"] == 11) {
            return view("web.user.status.pendinginterview")->with($data);
        } elseif ($user["profile_status"] == 12) {
            return redirect("/web-dashboard");
        } elseif ($user["profile_status"] == 13) {
            return view("web.user.status.reject")->with($data);
        } elseif (
            $user["profile_status"] == 14 ||
            $user["profile_status"] == 15 ||
            $user["profile_status"] == 17 ||
            $user["profile_status"] == 20 ||
            $user["profile_status"] == 16
        ) {
            return redirect("/web-dashboard");
        }elseif ($user["profile_status"] == 22) {
            return view("web.user.status.transcript_submitted")->with($data);
        } else {
            return redirect("/");
        }
    }

    public function loginwithemailold(Request $request)
    {
        $this->validate(
            $request,
            ["email" => "required", "password" => "required"],
            [
                "email" => "Email Field is required",
                "password" => "Password Field is required",
            ]
        );

        $email = $request->input("email");
        $password = $request->input("password");

        $user = new User();

        $login = User::where("email", "=", $email)->first();
        if (!$login) {
            return response()->json([
                "success" => false,
                "message" => "Login Failed, please check email id",
            ]);
        }

        $user_data = [
            "email" => $request->input("email"),
            "password" => $request->input("password"),
            "type" => "5",
        ];
        $remember_me = "";
        if (Auth::attempt($user_data, $remember_me)) {
            if (
                !Hash::check(
                    $request->input("password"),
                    Auth::user()->password
                )
            ) {
                return response()->json([
                    "success" => false,
                    "message" => "Login Failed, please check password",
                ]);
            } else {
                $request
                    ->session()
                    ->put("userewlogin", [
                        "id" => Auth::user()->id,
                        "name" => Auth::user()->first_name,
                        "email" => Auth::user()->email,
                        "type" => Auth::user()->type,
                    ]);
                if ($login["email_verify_status"] == 0) {
                    return response()->json([
                        "success" => "deactivate",
                        "message" => "Please verify your account",
                        "redirect" => url("verify"),
                    ]);
                }
                if ($login["profile_status"] == "3") {
                    return response()->json([
                        "success" => "deactivate",
                        "message" => "Please wait for admin approval",
                        "redirect" => url("submit"),
                    ]);
                } elseif ($login["profile_status"] == "1") {
                    return response()->json([
                        "success" => "deactivate",
                        "message" => "Login Successful",
                        "redirect" => url(
                            "/onboarding-step/" . encrypt($login["id"])
                        ),
                    ]);
                } elseif (
                    $login["profile_status"] == 2 ||
                    $login["profile_status"] == 10 ||
                    $login["profile_status"] == 3 ||
                    $login["profile_status"] == 5 ||
                    $login["profile_status"] == 13
                ) {
                    return response()->json([
                        "success" => "deactivate",
                        "message" => "Please wait for admin approval",
                        "redirect" => url("submit"),
                    ]);
                } elseif (
                    $login["profile_status"] == 12 ||
                    $login["profile_status"] == 4 ||
                    $login["profile_status"] == 8 ||
                    $login["profile_status"] == 6 ||
                    $login["profile_status"] == 8
                ) {
                    $data["success"] = true;
                    $data["message"] = "Login Successful";
                    $data["redirect"] = url("/web-dashboard");
                    return response()->json($data);
                }
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Login Fail, please check password",
            ]);
        }

    }

    public function loginwithemail(Request $request)
    {
        $this->validate(
            $request,
            ["email" => "required", "password" => "required"],
            [
                "email" => "Email Field is required",
                "password" => "Password Field is required",
            ]
        );

        $email = $request->input("email");
        $password = $request->input("password");

        $user = new User();

        $login = User::where("email", "=", $email)->first();
        if (!$login) {
            return response()->json([
                "success" => false,
                "message" => "Login Failed, please check email id",
            ]);
        }

        $user_data = [
            "email" => $request->input("email"),
            "password" => $request->input("password"),
            "type" => "5",
        ];
        $remember_me = "";
        if (Auth::attempt($user_data, $remember_me)) {
            if (
                !Hash::check(
                    $request->input("password"),
                    Auth::user()->password
                )
            ) {
                return response()->json([
                    "success" => false,
                    "message" => "Login Failed. Please check your password",
                ]);
            } else {
                Session::forget('schoolloginsession');
                $request
                    ->session()
                    ->put("userewlogin", [
                        "id" => Auth::user()->id,
                        "name" => Auth::user()->first_name,
                        "email" => Auth::user()->email,
                        "type" => Auth::user()->type,
                    ]);

                if ($login["email_verify_status"] == 0) {
                    return response()->json([
                        "success" => "deactivate",
                        "message" => "Please verify your account",
                        "redirect" => url("pendingverify"),
                    ]);
                }

                if ($login["status"] == 0) {
                    return response()->json([
                        "success" => false,
                        "message" => "Your account deactivated",
                    ]);
                }

                if ($login["status"] == 2) {
                    return response()->json([
                        "success" => false,
                        "message" => "Your account deleted",
                    ]);
                }


                if ($login["profile_status"] == "3") {
                    return response()->json([
                        "success" => "deactivate",
                        "message" => "Please wait for admin approval",
                        "redirect" => url("submit"),
                    ]);
                } elseif ($login["profile_status"] == "1") {
                    User::where("id", $login["id"])->update(["login_at" => date("Y-m-d H:i:s")]);
                    $userTimezone = $request->input('timezone');
                    session(['user_timezone' => $userTimezone]);

                    return response()->json([
                        "success" => "deactivate",
                        "message" => "Login Successful",
                        "redirect" => url(
                            "/onboarding-step/" . encrypt($login["id"])
                        ),
                    ]);
                } else {
                    User::where("id", $login["id"])->update(["login_at" => date("Y-m-d H:i:s")]);
                    $data["success"] = true;
                    $data['email']=$request->email;
                    $data['password']='123456';
                    $data["message"] = "Login Successful";
                    $data["redirect"] = url("/web-dashboard");
                    return response()->json($data);
                }
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Login Fail, please check password",
            ]);
        }
    }
    // forget password
    public function forgot_password(Request $request)
    {

        $this->validate(
            $request,
            ["email" => "required|email"],
            ["email" => "Email Field is required"]
        );
        $email = $request->input("email");
        $users = User::where(["email" => $email])->first();

        if (!empty($users)) {
            link::where("user_id", "=", $users["id"])->where("link_type", "=", 'password')->delete();
            $id = Crypt::encrypt($users["id"]);

            $message = "Send Reset Link";
            $to_name = $users["first_name"];
            $to_email = $users["email"];
            $url = url("reset-password/" . $id);
            $dataEmail = [
                "email" => $to_email,
                "mailbody" => "Forgot user password",
                "first_name" => $users["first_name"],
                "last_name" => $users["last_name"],
                "redirect" => url("/reset-password/" . encrypt($users["id"])),
            ];



            $template = DB::table("tbl_email_templates")->where("email_template_id", "4")->first();
            $body =  $template->description;
            if($users["last_name"]){
                $full_name=$users["first_name"].' '.$users["last_name"];
            }else{
                $full_name=$users["first_name"];
            }

            $body= str_replace('{{NAME}}', $full_name, $body);

            $body = str_replace('{{link}}', url("/reset-password/" . encrypt($users["id"])), $body);

            $subject=$template->subject;

            $data=array('template'=>$body);
        try{
           Mail::send('template', $data, function (
                $message
            ) use ($email,$subject) {
                $message->to($email)->subject($subject);
            });

        } catch (\Throwable $th) {
            //throw $th;
        }
            $datalink["link"] = url("/reset-password/" . encrypt($users["id"]));
            $datalink["link_type"] = 'password';
            $datalink["user_id"] = $users["id"];
            $saveLink = link::insertGetId($datalink);


            $data["success"] = true;
            $data["message"] =
                "Email sent successfully, Please check your Email";
            $data["link"] = $url;
        } else {
            $data["success"] = false;
            $data["message"] =
                "Sorry, no user exists on our system with that email";
        }
        return response()->json($data);
    }

    public function reset_password(Request $request)
    {

        $this->validate(
            $request,
            ["password" => "required", "cpassword" => "required"],
            [
                "password" => "Password Field is required",
                "cpassword" => "Confirm password field is required",
            ]
        );
        $id = $request->input("id");
        $datas = $request->except(["_token", "password", "cpassword"]);

        $password = Hash::make($request->input("password"));
        User::where(["id" => $id])->update(["password" => $password]);

        $datas["success"] = true;
        $datas["message"] = "Reset Password successfully";
        $datas["redirect"] = url("sign-in");
        return response()->json($datas);
    }

    public function createUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "email" => "required|string",
            "password" => "required|string",
            "first_name" => "required",
            "last_name" => "required",
            "about" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "password", "agree"]);
        $email = $request->email;

        if ($email != "") {
            $plannames = User::where("email", "=", $email)->get();
            if (count($plannames)) {
                return response()->json([
                    "success" => "already",
                    "message" =>
                        "An account with this email already exists. Please sign in to continue.",
                ]);
            }
        }
        $length = 6;
        $randpassword = substr(
            str_shuffle("**********ABCDEFGHIJKLMNOPQRSTUVWXYZ"),
            1,
            $length
        );
        $data["type"] = "5";
        $data["status"] = "1";
        $data["profile_status"] = "1";
        $data["email_verify_status"] = 0;
        $data["email_verify_time"] = date("Y-m-d H:i:s");
        $data["password"] = Hash::make($request->input("password"));
        $data["user_id"] = substr(str_shuffle("**********"), 1, $length);
        $data["app_notification"] = 1;
        $data["email_notification"] = 1;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $result = User::insert($data);
        if ($result) {
            Session::forget('schoolloginsession');
            $lastid = DB::getPdo()->lastInsertId();

            $email = $request->email;
            $invitation = InvitationModel::where("email", $email)->first();

            if($invitation){

            $datai["application_id"]=$lastid;
            $datai["user_id"]=$invitation->created_by;
            $datai["type"]='Recruiter';
            $datai["status"]='1';
            invite_application_recruiter::insertGetId($datai);

            }


            $dataEmail = [
                "email" => $email,
                "mailbody" => "New Staff",
                "first_name" => $request->first_name,
                "last_name" => $request->last_name,
                "redirect" => url("/onboarding-step/" . encrypt($lastid)),
            ];
            $template = DB::table("tbl_email_templates")->where("email_template_id", "3")->first();
            $body =  $template->description;

            if($request->last_name){
                $full_name=$request->first_name.' '.$request->last_name;
            }else{
                $full_name=$request->first_name;
            }

            $body= str_replace('{{NAME}}', $full_name, $body);
            $body = str_replace('{{redirect}}', url("/email-link/" . encrypt($lastid)), $body);

            $subject=$template->subject;

            $data=array('template'=>$body);

            try {
                //code...
            logger()->info(url("/email-link/" . encrypt($lastid)));
           Mail::send('template', $data, function (
                $message
            ) use ($email,$subject) {
                $message->to($email)->subject($subject);
            });
        } catch (\Throwable $th) {
            //throw $th;
        }
            $template1 = DB::table("tbl_email_templates")->where("email_template_id", "16")->first();
            $body1 =  $template1->description;
            $body1= str_replace('{{NAME}}', 'Whizara team', $body1);
            $body1= str_replace('{{UserName}}', $full_name, $body1);
            $body1= str_replace('{{userEmail}}', $email, $body1);


            $subject1=$template1->subject;
            $data1=array('template'=>$body1);
            $email1='<EMAIL>';
            try {

            Mail::send('template', $data1, function (
                $message
            ) use ($email1,$subject1) {
                $message->to($email1)->subject($subject1);
            });
        } catch (\Throwable $th) {
            //throw $th;
        }
            $user_data = [
                "email" => $email,
                "password" => $request->input("password"),
                "type" => "5",
            ];
            if (Auth::attempt($user_data)) {
                $request
                    ->session()
                    ->put("userewlogin", [
                        "id" => Auth::user()->id,
                        "name" => Auth::user()->first_name,
                        "email" => Auth::user()->email,
                        "type" => Auth::user()->type,
                    ]);
            }

            $datalink["link"] = url("/onboarding-step/" . encrypt($lastid));
            $datalink["link_type"] = 'onboarding';
            $datalink["user_id"] = $lastid;
            $saveLink = link::insertGetId($datalink);

            return response()->json([
                "success" => true,
                "message" => "Submitted successfully,Please check your email",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    public function verify_email(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required|string",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id"]);

        $user = User::where("id", $request->id)->first();
        if ($user) {
            $email = $user["email"];
            $dataEmail = [
                "email" => $user["email"],
                "mailbody" => "New Staff",
                "first_name" => $user["first_name"],
                "last_name" => $user["last_name"],
                "redirect" => url("/email-link/" . encrypt($request->id)),
            ];
            logger()->info(url("/email-link/" . encrypt($request->id)));
            $template = DB::table("tbl_email_templates")->where("email_template_id", "3")->first();
            $body =  $template->description;
            if($user["last_name"]){
                $full_name=$user["first_name"].' '.$user["last_name"];
            }else{
                $full_name=$user["first_name"];
            }

            $body= str_replace('{{NAME}}', $full_name, $body);
            $body = str_replace('{{redirect}}', url("/email-link/" . encrypt($request->id)), $body);

            $subject=$template->subject;

            $data=array('template'=>$body);


            try {

           Mail::send('template', $data, function (
                $message
            ) use ($email,$subject) {
                $message->to($email)->subject($subject);
            });

            } catch (\Throwable $th) {

            }

            $dataa["email_verify_status"] = 0;
            $dataa["email_verify_time"] = date("Y-m-d H:i:s");
            User::where("id", $request->id)->update($dataa);

            link::where("user_id", "=", $request->id)->where("link_type", "=", 'onboarding')->delete();
            $datalink["link"] = url("/onboarding-step/" . encrypt($request->id));
            $datalink["link_type"] = 'onboarding';
            $datalink["user_id"] = $request->id;
            $saveLink = link::insertGetId($datalink);
            return response()->json([
                "success" => true,
                "message" =>
                    "Verification email sent",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Something went wrong",
            ]);
        }
    }

    function submitFirstStep(Request $request)
    {
        $reside_united_states = $request->reside_united_states;
        if ($reside_united_states == "yes") {
            $validator = Validator::make($request->all(), [
                "reside_united_states" => "required",
                "state" => "required",
                "zip_code" => "required",
                "city" => "required",
                "i_am_authorized" => "required",
                "id" => "required",
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                "reside_united_states" => "required",
                "id" => "required",
            ]);
        }

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id"]);
        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $first = UserFirstStepModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($first)) {
            UserFirstStepModel::where(["user_id" => $request->id])->delete();
        }
        $result = UserFirstStepModel::insert($data);
        if ($result) {
            $datas["country"] = "United States";
            $datas["state"] = $request->state;
            $datas["city"] = $request->city;
            $datas["zipcode"] = $request->zip_code;
            $datas["start_date"] = date("Y-m-d H:i:s");

            User::where("id", $request->id)->update($datas);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    function submitSecondStep(Request $request)
    {
        if ($request->certification == "no") {
            $validator = Validator::make($request->all(), [
                "certification" => "required",
                "teaching_since" => "required",
                "experience_teaching_ages" => "required",
                "highest_level_of_education" => "required",
                "month_and_year_graduation" => "required",
                "GPA" => "required",
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                "certification" => "required",
                "teaching_certification_year" => "required",
                "teaching_certification_states" => "required",
                "teaching_since" => "required",
                "experience_teaching_ages" => "required",
                "highest_level_of_education" => "required",
                "month_and_year_graduation" => "required",
                "GPA" => "required",
            ]);
        }

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except([
            "_token",
            "id",
            "resume",
            "filename",
            "other_specify",
            "specify",
            "resume_value",
            "file",
            "award_value",
            "certificatelastid",
            "awardlastid",
            "certificate_value",
            "distinction_value",
            "distinctionsid",
            "highest_level_of_education",
            "month_and_year_graduation",
            "GPA",
            "educationid",
            "experience_teaching_ages",
            "certified_special_education",
            "teaching_certification_states",
            "certifications_other"
        ]);
        if ($request->hasFile("resume")) {
            $image = $request->file("resume");
            $name = time() . "." . $image->getClientOriginalExtension();
            $filename = 'uploads/resume/'. $name;
            uploads3image($filename,$image);
            $data["resume"] = $name;
        } else {
            $data["resume"] = $request->resume_value;
        }

        if (isset($request->certified_special_education)) {
            if (count($request->certified_special_education) > 0) {
                $data["certified_special_education"] = implode(
                    ",",
                    $request->certified_special_education
                );
            }
        }
        if ($request->certification == "yes") {
            $data["specify"] = $request->specify;
        } else {
            $data["specify"] = $request->other_specify;
        }
        $data["user_id"] = $request->id;
        if ($request->certification == "yes") {
            $profiletype = "Certified Teacher";
        } else {
            $profiletype = $request->profile_type;
        }
        if (isset($request->experience_teaching_ages)) {
            if (count($request->experience_teaching_ages) > 0) {
            $data["experience_teaching_ages"] = implode(
                ",",
                $request->experience_teaching_ages
            );
       }
       }
       if (isset($request->teaching_certification_states)) {
        if (count($request->teaching_certification_states) > 0) {
        $data["teaching_certification_states"] = implode(
            ",",
            $request->teaching_certification_states
        );
      }
    }
        $data['certified_other']=$request->certifications_other;
        $data["profile_type"] = $profiletype;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $first = UserSecondStepModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($first)) {
            UserSecondStepModel::where(["user_id" => $request->id])->delete();
        }

        $result = UserSecondStepModel::insert($data);
        if ($result) {
            if ($request->certification == "yes") {
                $cert = "yes";
            } else {
                $cert = $request->profile_type;
            }
            $highest_level_of_education = $request->highest_level_of_education;
            $month_and_year_graduation = $request->month_and_year_graduation;
            $GPA = $request->GPA;
            $firstedu = UserEducationModel::where([
                "user_id" => $request->id,
            ])->get();
            if (!empty($firstedu)) {
                UserEducationModel::where([
                    "user_id" => $request->id,
                ])->delete();
            }

            $educationarray = [];
            if (!empty($highest_level_of_education)) {
                foreach ($highest_level_of_education as $key => $rowseduc) {
                    if ($rowseduc != "") {
                        $educationarray[] = [
                            "user_id" => $request->id,
                            "highest_level_of_education" => $rowseduc,
                            "month_and_year_graduation" =>
                                $month_and_year_graduation[$key],
                            "GPA" => $GPA[$key],
                        ];
                    }
                }
            }


            UserEducationModel::insert($educationarray);

            $datas["teacher_type"] = $cert;
            User::where("id", $request->id)->update($datas);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    function submitSecondStepOnBoarding(Request $request)
    {

        $first = UserSecondStepModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($first)) {
            $data = $request->except([
                "_token",
               "id",
               "resume",
               "filename",
               "other_specify",
               "specify",
               "resume_value",
               "file",
               "award_value",
               "certificatelastid",
               "awardlastid",
               "certificate_value",
               "distinction_value",
               "distinctionsid",
               "highest_level_of_education",
               "month_and_year_graduation",
               "GPA",
               "educationid",
               "experience_teaching_ages",
               "certified_special_education",
               "teaching_certification_states",
               "award_desc",
               "certificate_desc",
               "notable_desc",
               'certifications_other',
               'certification',
               'teaching_certification_year',
               'teaching_since',
               'profile_type'
           ]);
            $data['profile_type']=$first->profile_type;
            $data['certification']=$first->certification;
            $data['certified_other']=$first->certified_other;
            $data["specify"] = $first->specify;
            $data["teaching_certification_year"] = $first->teaching_certification_year;

            $data["certified_special_education"] = $first->certified_special_education;
            $data["teaching_certification_states"] = $first->teaching_certification_states;
            $data["experience_teaching_ages"] =  $first->experience_teaching_ages;
            $data["teaching_since"] =  $first->teaching_since;
        }else{
            if ($request->certification == "no") {
                $validator = Validator::make($request->all(), [
                    "certification" => "required",
                    "teaching_since" => "required",
                    "experience_teaching_ages" => "required",
                    "highest_level_of_education" => "required",
                    "month_and_year_graduation" => "required",
                    "GPA" => "required",
                ]);
            } else {
                $validator = Validator::make($request->all(), [
                    "certification" => "required",
                    "teaching_certification_year" => "required",
                    "teaching_certification_states" => "required",

                    "teaching_since" => "required",
                    "experience_teaching_ages" => "required",
                    "highest_level_of_education" => "required",
                    "month_and_year_graduation" => "required",
                    "GPA" => "required",
                ]);
            }

            if ($validator->fails()) {
                return response()->json(
                    [
                        "success" => false,
                        "error" => "Please enter valid details",
                        "message" => $validator->errors(),
                    ],
                    400
                );
            }
            $data = $request->except([
                 "_token",
                "id",
                "resume",
                "filename",
                "other_specify",
                "specify",
                "resume_value",
                "file",
                "award_value",
                "certificatelastid",
                "awardlastid",
                "certificate_value",
                "distinction_value",
                "distinctionsid",
                "highest_level_of_education",
                "month_and_year_graduation",
                "GPA",
                "educationid",
                "experience_teaching_ages",
                "certified_special_education",
                "teaching_certification_states",
                "award_desc",
                "certificate_desc",
                "notable_desc",
                'certifications_other'
            ]);

            if ($request->certification == "yes") {
                $data["specify"] = $request->specify;
            } else {
                $data["specify"] = $request->other_specify;
            }


            if (isset($request->certified_special_education)) {
                if (count($request->certified_special_education) > 0) {
                    $data["certified_special_education"] = implode(
                        ",",
                        $request->certified_special_education
                    );
                }
            }
            if (isset($request->teaching_certification_states)) {
            if (count($request->teaching_certification_states) > 0) {
            $data["teaching_certification_states"] = implode(
                ",",
                $request->teaching_certification_states
            );
                }
            }

            if (isset($request->experience_teaching_ages)) {
                if (count($request->experience_teaching_ages) > 0) {
            $data["experience_teaching_ages"] = implode(
                ",",
                $request->experience_teaching_ages
            );
           }
          }

            $data['certified_other']=$request->certifications_other;

        }





        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");



        if ($request->hasFile("resume")) {
            $image = $request->file("resume");
            $name = time() . "." . $image->getClientOriginalExtension();

            $filename = 'uploads/resume/'. $name;
            uploads3image($filename,$image);
            $data["resume"] = $name;
        } else {
            $data["resume"] = $request->resume_value;
        }

        if (!empty($first)) {
            UserSecondStepModel::where(["user_id" => $request->id])->delete();
        }

        $result = UserSecondStepModel::insert($data);
        if ($result) {
            if ($request->certification == "yes") {
                $cert = "yes";
            } else {
                $cert = $request->profile_type;
            }
            $awardvalue = $request->award_value;
            $certificate_value = $request->certificate_value;
            $distinction_value = $request->distinction_value;

            $award_desc = $request->award_desc;
            $certificate_desc = $request->certificate_desc;
            $notable_desc = $request->notable_desc;

            $awardArray = [];
            if ($files = $request->file("file")) {

                    if (isset($files["awards"])) {
                    foreach ($files["awards"] as $key => $file) {
                        $name =
                            time() . "." . $file->getClientOriginalExtension();


                        $filename = 'uploads/award/'. $name;
                        uploads3image($filename,$file);
                        $awardArray[] = [
                            "user_id" => $request->id,
                            "type" => "award",
                            "file" => $name,
                            "subject" => $award_desc[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }

            $awardArrayy = [];
            if (!empty($awardvalue)) {
                foreach ($awardvalue as $key => $rows) {
                    if ($rows != "") {
                        $name = $awardvalue[$key];
                        $awardArrayy[] = [
                            "user_id" => $request->id,
                            "type" => "award",
                            "file" => $name,
                            "subject" => $award_desc[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }
            $first = AssessmentsModel::where([
                "user_id" => $request->id,
                "type" => "award",
            ])->first();
            if (!empty($first)) {
                AssessmentsModel::where([
                    "user_id" => $request->id,
                    "type" => "award",
                ])->delete();
            }
            if (count($awardArray) > 0) {
                AssessmentsModel::insert($awardArray);
            }
            if (count($awardArrayy) > 0) {
                AssessmentsModel::insert($awardArrayy);
            }

            $certificateArray = [];
            if ($files = $request->file("file")) {
                if (isset($files["certificate"])) {
                    foreach ($files["certificate"] as $key => $file) {
                        $name =
                            time() . "." . $file->getClientOriginalExtension();

                        $filename = 'uploads/certificate/'. $name;
                        uploads3image($filename,$file);
                        $certificateArray[] = [
                            "user_id" => $request->id,
                            "type" => "certificate",
                            "file" => $name,
                            "subject" => $certificate_desc[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }

            $certificateArrayy = [];
            if (!empty($certificate_value)) {
                foreach ($certificate_value as $key => $rowsc) {
                    if ($rowsc != "") {
                        $name = $certificate_value[$key];
                        $certificateArrayy[] = [
                            "user_id" => $request->id,
                            "type" => "certificate",
                            "file" => $name,
                            "subject" => $certificate_desc[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }
            $first = AssessmentsModel::where([
                "user_id" => $request->id,
                "type" => "certificate",
            ])->first();
            if (!empty($first)) {
                AssessmentsModel::where([
                    "user_id" => $request->id,
                    "type" => "certificate",
                ])->delete();
            }
            if (count($certificateArray) > 0) {
                AssessmentsModel::insert($certificateArray);
            }
            if (count($certificateArrayy) > 0) {
                AssessmentsModel::insert($certificateArrayy);
            }

            $distinctionsArray = [];
            if ($files = $request->file("file")) {
                if (isset($files["distinctions"])) {
                    foreach ($files["distinctions"] as $key => $file) {
                        $name =
                            time() . "." . $file->getClientOriginalExtension();
                            $filename = 'uploads/distinctions/'. $name;
                            uploads3image($filename,$file);

                        $distinctionsArray[] = [
                            "user_id" => $request->id,
                            "type" => "distinctions",
                            "file" => $name,
                            "subject" => $notable_desc[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }

            $distinctionsArrayy = [];
            if (!empty($distinction_value)) {
                foreach ($distinction_value as $key => $rowsc) {
                    if ($rowsc != "") {
                        $name = $distinction_value[$key];
                        $distinctionsArrayy[] = [
                            "user_id" => $request->id,
                            "type" => "distinctions",
                            "file" => $name,
                            "subject" => $notable_desc[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }
            $first = AssessmentsModel::where([
                "user_id" => $request->id,
                "type" => "distinctions",
            ])->first();
            if (!empty($first)) {
                AssessmentsModel::where([
                    "user_id" => $request->id,
                    "type" => "distinctions",
                ])->delete();
            }
            if (count($distinctionsArray) > 0) {
                AssessmentsModel::insert($distinctionsArray);
            }
            if (count($distinctionsArrayy) > 0) {
                AssessmentsModel::insert($distinctionsArrayy);
            }
            $highest_level_of_education = $request->highest_level_of_education;
            $month_and_year_graduation = $request->month_and_year_graduation;
            $GPA = $request->GPA;
            $firstedu = UserEducationModel::where([
                "user_id" => $request->id,
            ])->get();
            if (!empty($firstedu)) {
                UserEducationModel::where([
                    "user_id" => $request->id,
                ])->delete();
            }

            $educationarray = [];
            if (!empty($highest_level_of_education)) {
                foreach ($highest_level_of_education as $key => $rowseduc) {
                    if ($rowseduc != "") {
                        $educationarray[] = [
                            "user_id" => $request->id,
                            "highest_level_of_education" => $rowseduc,
                            "month_and_year_graduation" =>
                                $month_and_year_graduation[$key],
                            "GPA" => $GPA[$key],
                        ];
                    }
                }
            }

            UserEducationModel::insert($educationarray);

            $datas["teacher_type"] = $cert;
            User::where("id", $request->id)->update($datas);
            return response()->json([
                "success" => true,
                "message" => "Submitted Successfully",
            ]);

        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function submitThirdStep(Request $request)
    {



        $validator = Validator::make($request->all(), [
            "i_prefer_to_teach" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except([
            "_token",
            "id",
            "format",
            "schedule",
            "subject",
            "range",
            "subject_other",
            "i_prefer_to_teach",
            "sub_subject"
        ]);
        $data["i_prefer_to_teach"] = implode(",", $request->i_prefer_to_teach);
        $data["format"] = implode(",", $request->format);
        $data["schedule"] = implode(",", $request->schedule);
        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $first = UserThirdStepModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($first)) {
            UserThirdStepModel::where(["user_id" => $request->id])->delete();
        }
        $result = UserThirdStepModel::insert($data);
        if ($result) {
            $lastid = DB::getPdo()->lastInsertId();
            UserSubjectsModel::where(["step_id" => $lastid])->delete();


            $min = $request->range;
            $subject_other = $request->subject_other;
            $sub_subject = $request->sub_subject;
            $datasubject = [];

            if (count($request->subject) > 0) {
                $i=0;
                foreach (array_filter($request->subject) as $key => $rows) {
                 $i++;

                    if ($rows) {
                        $sub='';
                        if(isset($sub_subject[$i])){
                         $sub=   implode(',',$sub_subject[$i]);
                        }
                        $datasubject[] = [
                            "user_id" =>$request->id,
                            "step_id" =>$lastid,
                            "subject" => $rows,
                            'sub_subject'=>$sub,
                            "proficiency" => $min[$key],
                            "other" => $subject_other[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }

                }

            }

            UserSubjectsModel::insert($datasubject);
            if (count($request->format) > 0) {
                $datas["teach"] = implode(",", $request->format);
                User::where("id", $request->id)->update($datas);
            }
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function submitThirdStepTec(Request $request)
    {



        $validator = Validator::make($request->all(), [
            "i_prefer_to_teach" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except([
            "_token",
            "id",
            "format",
            "schedule",
            "subject",
            "range",
            "subject_other",
            "i_prefer_to_teach",
            "sub_subject"
        ]);
        $data["i_prefer_to_teach"] = implode(",", $request->i_prefer_to_teach);
        $data["format"] = implode(",", $request->format);
        $data["schedule"] = implode(",", $request->schedule);
        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $first = UserThirdStepModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($first)) {
            UserThirdStepModel::where(["user_id" => $request->id])->delete();
        }
        $result = UserThirdStepModel::insert($data);
        if ($result) {
            $lastid = DB::getPdo()->lastInsertId();
            UserSubjectsModel::where(["user_id" => $request->id])->delete();


            $min = $request->range;
            $subject_other = $request->subject_other;
            $sub_subject = $request->sub_subject;
            $datasubject = [];

            if (count($request->subject) > 0) {

                foreach (array_filter($request->subject) as $key => $rows) {
                    if ($rows) {

                        $datasubject[] = [
                            "user_id" =>$request->id,
                            "step_id" =>$lastid,
                            "subject" => $rows,
                            'sub_subject'=>@$sub_subject[($key+1)][0],
                            "proficiency" => $min[$key],
                            "other" => $subject_other[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }

                }

            }

            UserSubjectsModel::insert($datasubject);
            if (count($request->format) > 0) {
                $datas["teach"] = implode(",", $request->format);
                User::where("id", $request->id)->update($datas);
            }
            return response()->json([
                "success" => true,
                "message" => "Teaching Preference Saved Successfully",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function submitFourthStep(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id", "formate_value"]);

        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $first = UserFourthStepModel::where([
            "user_id" => $request->id,
        ])->first();
        if (!empty($first)) {
            UserFourthStepModel::where(["user_id" => $request->id])->delete();
        }
        $result = UserFourthStepModel::insert($data);
        if ($result) {
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function addAssessments(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except([
            "_token",
            "id",
            "ass_subject",
            "file",
            "total_question",
            "topic"
        ]);

        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $ass_subject = $request->ass_subject;
        $topic=$request->topic;
        $classroomarray = [];
        $teachingarray = [];
        $indorductionarray = [];
        if ($files = $request->file("file")) {
            if (count($files["introduction"]) > 0) {
                foreach ($files["introduction"] as $file) {
                    $name = time() . "." . $file->getClientOriginalExtension();

                    $filename = 'uploads/introduction/'. $name;
                    uploads3image($filename,$file);
                    $indorductionarray[] = [
                        "user_id" => $request->id,
                        "type" => "introduction",
                        "subject" => null,
                        "file" => $name,
                        "created_at" => date("Y-m-d H:i:s"),
                        "updated_at" => date("Y-m-d H:i:s"),
                    ];
                }
            }

            if (count($files["teaching"]) > 0) {
                foreach ($files["teaching"] as $key => $file) {
                    $name = time() . "." . $file->getClientOriginalExtension();


                    $filename = 'uploads/teaching/'. $name;
                    uploads3image($filename,$file);
                    $teachingarray[] = [
                        "user_id" => $request->id,
                        "type" => "teaching",
                        "file" => $name,
                        "subject" => $ass_subject[$key],
                        "topic"=>$topic[$key],
                        "created_at" => date("Y-m-d H:i:s"),
                        "updated_at" => date("Y-m-d H:i:s"),
                    ];
                }
            }

            if (count($files["classroom"]) > 0) {
                foreach ($files["classroom"] as $key => $file) {
                    $name = time() . "." . $file->getClientOriginalExtension();

                    $filename = 'uploads/classroom/'. $name;
                    uploads3image($filename,$file);

                    $classroomarray[] = [
                        "user_id" => $request->id,
                        "type" => "classroom",
                        "file" => $name,
                        "subject" => null,
                        "created_at" => date("Y-m-d H:i:s"),
                        "updated_at" => date("Y-m-d H:i:s"),
                    ];
                }
            }
        }
        $newArray = array_merge(
            $indorductionarray,
            $teachingarray,
            $classroomarray
        );


        $first = AssessmentsModel::where(["user_id" => $request->id])->first();
        if (!empty($first)) {
            AssessmentsModel::where(["user_id" => $request->id])->delete();
        }
        $result = AssessmentsModel::insert($newArray);
        if ($result) {
            $total = $request->total_question;
            if (!empty($total)) {
                $quizarray = [];
                for ($i = 0; $i < $total; $i++) {
                    $qu = "question" . $i;
                    $question = $request->{$qu};
                    $op = "option" . $i;
                    $option = $request->{$op};
                    $quizarray[$question] = $option;
                }
                $first = UserQuizModel::where([
                    "user_id" => $request->id,
                ])->first();
                if (!empty($first)) {
                    UserQuizModel::where(["user_id" => $request->id])->delete();
                }
                UserQuizModel::insert([
                    "user_id" => $request->id,
                    "quiz_question_answer" => json_encode($quizarray),
                    "created_at" => date("Y-m-d H:i:s"),
                    "updated_at" => date("Y-m-d H:i:s"),
                ]);
            }

            $datas["profile_status"] = "2";
            User::where("id", $request->id)->update($datas);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }
    public function submit_intro(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id", "ifile"]);

        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $indorductionarray = [];
        if ($files = $request->file("file")) {
            foreach ($files as $file) {
                $name = time() . "." . $file->getClientOriginalExtension();
                $filename = 'uploads/introduction/'. $name;
                uploads3image($filename,$file);
                $indorductionarray[] = [
                    "user_id" => $request->id,
                    "type" => "introduction",
                    "subject" => null,
                    "file" => $name,
                    "created_at" => date("Y-m-d H:i:s"),
                    "updated_at" => date("Y-m-d H:i:s"),
                ];
            }
        } else {
            $indorductionarray[] = [
                "user_id" => $request->id,
                "type" => "introduction",
                "subject" => null,
                "file" => $request->ifile,
                "created_at" => date("Y-m-d H:i:s"),
                "updated_at" => date("Y-m-d H:i:s"),
            ];
        }

        $first = AssessmentsModel::where([
            "user_id" => $request->id,
            "type" => "introduction",
        ])->first();
        if (!empty($first)) {
            AssessmentsModel::where([
                "user_id" => $request->id,
                "type" => "introduction",
            ])->delete();
        }

        $result = AssessmentsModel::insert($indorductionarray);
        if ($result) {
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);

        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function submit_teaching(Request $request)
    {

        $validator = Validator::make($request->all(), [
            "id" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id", "tfile","topic"]);

        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $ass_subject = $request->ass_subject;
        $teach_other_specify = $request->teach_other_specify;
        $tfile = $request->tfile;
        $topic = $request->topic;
        $sub_subject_teaching = $request->sub_subject_teaching;
        $teachingarray = [];
        $teachingarrayy = [];
        if ($files = $request->file("file")) {
            foreach ($files as $key => $file) {
                $name = time() . "." . $file->getClientOriginalExtension();

                $filename = 'uploads/teaching/'. $name;
                uploads3image($filename,$file);

                $teachingarray[] = [
                    "user_id" => $request->id,
                    "type" => "teaching",
                    "file" => $name,
                    "topic" => $topic[$key],
                    "subject" => $ass_subject[$key],
                    "created_at" => date("Y-m-d H:i:s"),
                    "updated_at" => date("Y-m-d H:i:s"),
                ];
            }
        }else{
            if (!empty($tfile)) {
                foreach ($tfile as $key => $rows) {
                    if ($rows != "") {
                        $name = $tfile[$key];
                        $teachingarray[] = [
                            "user_id" => $request->id,
                            "type" => "teaching",
                            "file" => $name,
                            "topic" => $topic[$key],

                            "subject" => $ass_subject[$key],
                            "created_at" => date("Y-m-d H:i:s"),
                            "updated_at" => date("Y-m-d H:i:s"),
                        ];
                    }
                }
            }

        }




        $first = AssessmentsModel::where([
            "user_id" => $request->id,
            "type" => "teaching",
        ])->get();
        if (count($first) > 0) {
            AssessmentsModel::where([
                "user_id" => $request->id,
                "type" => "teaching",
            ])->delete();
        }
        if (count($teachingarray) > 0) {
            AssessmentsModel::where([
                "user_id" => $request->id,
                "type" => "teaching",
            ])->delete();
            $result = AssessmentsModel::insert($teachingarray);
        }
        $result = true;

        if ($result) {

            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);

        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function submit_classrorm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id"]);

        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");
        $class_value = $request->class_value;
        $classroomarray = [];
        if ($files = $request->file("file")) {
            if (count($files["classroom"]) > 0) {
                foreach ($files["classroom"] as $key => $file) {
                    $name = time() . "." . $file->getClientOriginalExtension();

                    $filename = 'uploads/classroom/'. $name;
                    uploads3image($filename,$file);
                    $classroomarray[] = [
                        "user_id" => $request->id,
                        "type" => "classroom",
                        "file" => $name,
                        "subject" => null,
                        "created_at" => date("Y-m-d H:i:s"),
                        "updated_at" => date("Y-m-d H:i:s"),
                    ];
                }
            }
        }else{

        if (!empty($class_value)) {
            foreach ($class_value as $key => $rows) {
                if ($rows != "") {
                    $name = $class_value[$key];
                    $classroomarray[] = [
                        "user_id" => $request->id,
                        "type" => "classroom",
                        "file" => $name,
                        "subject" => null,
                        "created_at" => date("Y-m-d H:i:s"),
                        "updated_at" => date("Y-m-d H:i:s"),
                    ];
                }
            }
        }
    }
        $first = AssessmentsModel::where([
            "user_id" => $request->id,
            "type" => "classroom",
        ])->first();
        if (!empty($first)) {
            AssessmentsModel::where([
                "user_id" => $request->id,
                "type" => "classroom",
            ])->delete();
        }
        if (count($classroomarray) > 0) {
            $result = AssessmentsModel::insert($classroomarray);
        }
        $result = true;

        if ($result) {

            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
            return response()->json([
                "success" => true,
                "message" => "Application Saved",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function submit_quiz(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id", "total_question"]);

        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $total = $request->total_question;

        $quizarray = [];
        for ($i = 0; $i < $total; $i++) {
            $qu = "question" . $i;
            $question = $request->{$qu};
            $op = "option" . $i;
            $option = $request->{$op};
            $quizarray[$question] = $option;
        }
        $first = UserQuizModel::where(["user_id" => $request->id])->first();
        $usersdetails = User::where(["id" => $request->id])->first();
        $profilestatus = "2";
        if (!empty($first)) {
            UserQuizModel::where(["user_id" => $request->id])->delete();
            if($usersdetails->profile_status==10){
            $profilestatus = "18";
            $datas["is_resubmit"] = "1";
            }
        }
        $result = UserQuizModel::insert([
            "user_id" => $request->id,
            "quiz_question_answer" => json_encode($quizarray),
            "created_at" => date("Y-m-d H:i:s"),
            "updated_at" => date("Y-m-d H:i:s"),
        ]);
        if ($result) {
            $date = date("Y-m-d H:i:s");
            $datas["profile_status"] = $profilestatus;
            $datas["review_deadline"] = date(
                "Y-m-d H:i:s",
                strtotime($date . " + 4 days")
            );
            $datas["submission_date"] = $date;

            $userUp=User::where("id", $request->id)->update($datas);

            if($usersdetails->profile_status==10 && $usersdetails->app_notification==1){
                createCommanNotification($usersdetails,'2','Reviewer','Admin','user');
            }else{
                if($usersdetails->app_notification==1){
                createCommanNotification($usersdetails,'1','Reviewer','Admin','user');
                }
            }


            $template = DB::table("tbl_email_templates")->where("email_template_id", "5")->first();
            $body =  $template->description;

            if($usersdetails->last_name){
                $full_name=$usersdetails->first_name.' '.$usersdetails->last_name;
            }else{
                $full_name=$usersdetails->first_name;
            }

            $body= str_replace('{{NAME}}', $full_name, $body);
            $subject=$template->subject;
            $email= $usersdetails->email;
            $data=array('template'=>$body);

            try {


           Mail::send('template', $data, function (
                $message
            ) use ($email,$subject) {
                $message->to($email)->subject($subject);
            });
        } catch (\Throwable $th) {
            //throw $th;
        }





            $template1 = DB::table("tbl_email_templates")->where("email_template_id", "17")->first();
            $body1 =  $template1->description;
            $body1= str_replace('{{NAME}}', 'Whizara team', $body1);
            $body1= str_replace('{{UserName}}', $full_name, $body1);
            $body1= str_replace('{{userEmail}}', $email, $body1);
            $subject1=$template1->subject;
            $data1=array('template'=>$body1);
            $email1='<EMAIL>';


            try {


            Mail::send('template', $data1, function (
                $message
            ) use ($email1,$subject1) {
                $message->to($email1)->subject($subject1);
            });
        } catch (\Throwable $th) {
            //throw $th;
        }

            return response()->json([
                "success" => true,
                "message" => "Application Submited",
                "redirect" => url("submit"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function save_quiz(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id" => "required",
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except(["_token", "id", "total_question"]);

        $data["user_id"] = $request->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");

        $total = $request->total_question;

        $quizarray = [];
        for ($i = 0; $i < $total; $i++) {
            $qu = "question" . $i;
            $question = $request->{$qu};
            $op = "option" . $i;
            $option = $request->{$op};
            $quizarray[$question] = $option;
        }
        $first = UserQuizModel::where(["user_id" => $request->id])->first();
        $usersdetails = User::where(["id" => $request->id])->first();
        $profilestatus = "2";
        if (!empty($first)) {
            UserQuizModel::where(["user_id" => $request->id])->delete();
            if($usersdetails->profile_status==10){
            $profilestatus = "18";
            $datas["is_resubmit"] = "1";
            }
        }
        $result = UserQuizModel::insert([
            "user_id" => $request->id,
            "quiz_question_answer" => json_encode($quizarray),
            "created_at" => date("Y-m-d H:i:s"),
            "updated_at" => date("Y-m-d H:i:s"),
        ]);
        if ($result) {
            $date = date("Y-m-d H:i:s");
            $datas["profile_status"] = $profilestatus;


            return response()->json([
                "success" => true,
                "message" => "Application Saved",

            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }


    public function submitContract(Request $request)
    {

        $data["profile_status"] = 19;
        $data["is_contract"] = 1;
        $data["address"] = $request->addres;

        $id = Auth::user()->id;
        $record = User::where("id", $id)->first();
        if ($record) {
            $res = User::where("id", $id)->update($data);
            if ($res) {
                $result = user_contract::insert([
                    "user_id" =>$id,
                    "signature"=> $request->signature,
                    "contract" => $request->contracthtml,
                    "address" => $request->address,
                    "created_at" => date("Y-m-d H:i:s"),
                    "updated_at" => date("Y-m-d H:i:s"),
                ]);

                if($record->app_notification==1){

                createCommanNotification($record,'5','Reviewer','Admin','user');
                }

                $template = DB::table("tbl_email_templates")->where("email_template_id", "11")->first();
                $body =  $template->description;
                if($record->last_name){
                    $full_name=$record->first_name.' '.$record->last_name;
                }else{
                    $full_name=$record->first_name;
                }

                $body= str_replace('{{NAME}}', $full_name, $body);
                $subject=$template->subject;
                $email=$record->email;
                $data=array('template'=>$body);
                $agreementContent = user_contract::where('user_id',$id)->select('contract')->first();
                $pdf = Pdf::loadView('web.pdf.agreement_template', compact('agreementContent'));
                $pdfPath = storage_path('app/public/Learn2Code.LiveContract.pdf');
                $pdf->save($pdfPath);
                try{
                    $this->sendEmailWithAttachment($email, $subject, $pdfPath, $data);
            } catch (\Throwable $th) {
                //throw $th;
            }
                return response()->json([
                    "success" => true,
                    "message" => "Successfully Contract Submitted",
                    "redirect" => url("web-dashboard"),
                ]);
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Something went worng",
                ]);
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Record not found",
            ]);
        }
    }

    public function sendEmailWithAttachment($email, $subject, $pdfPath, $data)
    {
        Mail::send('template', $data, function ($message) use ($email, $subject, $pdfPath) {
            $message->to($email)->subject($subject);
            $message->attach($pdfPath, [
                'as' => 'agreement.pdf',
                'mime' => 'application/pdf',
            ]);
        });
    }

    public function interviewslot()
    {
        return view("web.user.status.interviewslot");
    }

    public function savetimeslot(Request $request)
    {
        $data["profile_status"] = 9;
        $id = Auth::user()->id;
        $record = User::where("id", $id)->first();
        if ($record) {
            $res = User::where("id", $id)->update($data);
            if ($res) {
                      $data1["slot_id"] = $_POST["time"];
                    $data1["user_id"] = $id;
                    user_interview_slots::insertGetId($data1);

                $tslot = DB::table("tbl_schedule_interviews")->where("id", $_POST["time"])->first();
               if($tslot){
                if($record->app_notification==1){
                createCommanNotification($record,'1','Reviewer','Admin','user');
                }
                $template = DB::table("tbl_email_templates")->where("email_template_id", "8")->first();
                            $body =  $template->description;
                            if($record->last_name){
                                $full_name=$record->first_name.' '.$record->last_name;
                            }else{
                                $full_name=$record->first_name;
                            }

                            $body= str_replace('{{NAME}}', $full_name, $body);
                            $body = str_replace('{{date}}', date('m-d-Y', strtotime($tslot->date)), $body);
                            $body = str_replace('{{Time}}', date('h:i a', strtotime($tslot->time)).'-'.date('h:i a', strtotime($tslot->end_time)).', '.$tslot->timezone, $body);
                            $body = str_replace('{{link}}', $tslot->link, $body);

                            $subject=$template->subject;
                            $email=$record->email;
                            $data=array('template'=>$body);
                            try{
                           Mail::send('template', $data, function (
                                $message
                            ) use ($email,$subject) {
                                $message->to($email)->subject($subject);
                            });
                        } catch (\Throwable $th) {
                            //throw $th;
                        }
                    }

                return response()->json([
                    "success" => true,
                    "message" => "Successfully Schedule Submitted",
                    "redirect" => url("web-dashboard"),
                ]);
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Something went worng",
                ]);
            }
        } else {
            return response()->json([
                "success" => false,
                "message" => "Record not found",
            ]);
        }
    }

    public function referencedetails(Request $request)
    {
        $id = Auth::user()->id;
        $data['user'] = User::where("id", $id)->first();
        return view("web.user.status.reference")->with($data);
    }

    function savereference(Request $request)
    {
        $validator = Validator::make($request->all(), [

            "transcript" => "required",

        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    "success" => false,
                    "error" => "Please enter valid details",
                    "message" => $validator->errors(),
                ],
                400
            );
        }
        $data = $request->except([
            "_token",


            "certifications",
            "transcript"

        ]);

        $data["user_id"] = Auth::user()->id;
        $data["created_at"] = date("Y-m-d H:i:s");
        $data["updated_at"] = date("Y-m-d H:i:s");



        $data1["user_id"] = Auth::user()->id;
        $data1["created_at"] = date("Y-m-d H:i:s");
        $data1["updated_at"] = date("Y-m-d H:i:s");

        if ($request->hasFile("transcript")) {
            $image = $request->file("transcript");
            $name = "TRN-" . time() . "-" . rand(10000, 99999) . "." . $image->getClientOriginalExtension();

            $filename = 'uploads/certificates/'. $name;
            uploads3image($filename,$image);

            $data["transcript"] = $name;
        }

        if ($request->hasFile("certifications")) {
            $image = $request->file("certifications");
            $name1 = "CRT-" . time() . "-" . rand(10000, 99999) . "." . $image->getClientOriginalExtension();

            $filename = 'uploads/certificates/'. $name1;
            uploads3image($filename,$image);
            $data["certifications"] = $name1;
        }



        $first = user_references::where([
            "user_id" => Auth::user()->id,
        ])->first();
        if (!empty($first)) {
            user_references::where(["user_id" => Auth::user()->id])->delete();
        }
        $result = user_references::insert($data);
        if ($result) {

            $dataps["profile_status"] = 22;
            $id = Auth::user()->id;
            $recordps = User::where("id", $id)->first();
            if ($recordps) {
                $res = User::where("id", $id)->update($dataps);
            }


            return response()->json([
                "success" => true,
                "message" => "Submitted successfully",
                "redirect" => url("/web-dashboard"),
            ]);
        } else {
            return response()->json([
                "success" => false,
                "error" => "Something went wrong",
            ]);
        }
    }

    public function get_subsubject(Request $request){
        $data['subsubject']=subsubject($request->id);
        $data['step']=$request->step;
        return view('web.onboarding.subsubject')->with($data);

    }

    public function get_subsubject_teaching(Request $request){
        $data['subsubject']=subsubject($request->id);

        return view('web.onboarding.subsubjectteaching')->with($data);
    }

    public function get_subject_details(Request $request){
    $id=$request->id;
    $subject=SubsubjectModel::where(['id'=>$id])->first();
    if(!empty($subject)){
         if($subject->topic){


            return response()->json([
                "success" => true,
                "data" => $subject->topic
            ]);
        }else{  return response()->json([
            "success" => false
        ]);
     };

    }else{
        return response()->json([
            "success" => false
        ]);

    }



    }

    public function filter_subject(Request $request){
        $subject = array_map("unserialize", array_unique(array_map("serialize", $request->subj)));
        $html='<option value="" >Select Subject</option>';
        if(!empty($subject)){
        foreach($subject as $rows){

         $html.='<option value="'.$rows['value'].'" data-sub-id="'.$rows['data-id'].'">'.$rows['text'].'</option>';
        }
        }
        return $html;
    }



    public function schoollogin()
    {

        if(Auth::user() && Auth::user()->type==6 && session('schoolloginsession')) {

            return redirect("/school-dashboard");
        }

        return view("school.login");
    }

    public function schoolforgotpassword()
    {
        return view("school.forgotpassword");
    }

     // forget password
     public function forgot_password_school(Request $request)
     {

         $this->validate(
             $request,
             ["email" => "required|email"],
             ["email" => "Email Field is required"]
         );
         $email = $request->input("email");
         $users = User::where(["email" => $email])->first();

         if (!empty($users)) {
             $id = Crypt::encrypt($users["id"]);
             link::where("user_id", "=", $users["id"])->where("link_type", "=", 'password')->delete();
             $message = "Send Reset Link";
             $to_name = $users["first_name"];
             $to_email = $users["email"];
             $url = url("reset-password-school/" . $id);
             $dataEmail = [
                 "email" => $to_email,
                 "mailbody" => "Forgot user password",
                 "first_name" => $users["first_name"],
                 "last_name" => $users["last_name"],
                 "redirect" => url("/reset-password-school/" . encrypt($users["id"])),
             ];



             $template = DB::table("tbl_email_templates")->where("email_template_id", "4")->first();
             $body =  $template->description;

             if($users["last_name"]){
                $full_name=$users["first_name"].' '.$users["last_name"];
            }else{
                $full_name=$users["first_name"];
            }

            $body= str_replace('{{NAME}}', $full_name, $body);
             $body = str_replace('{{link}}', url("/reset-password-school/" . encrypt($users["id"])), $body);

             $subject=$template->subject;

             $data=array('template'=>$body);
        try{
            Mail::send('template', $data, function (
                 $message
             ) use ($email,$subject) {
                 $message->to($email)->subject($subject);
             });
            } catch (\Throwable $th) {
                //throw $th;
            }

             $datalink["link"] = url("/reset-password-school/" . encrypt($users["id"]));
             $datalink["link_type"] = 'password';
             $datalink["user_id"] = $users["id"];
             $saveLink = link::insertGetId($datalink);

             $data["success"] = true;
             $data["message"] =
                 "Email sent successfully, Please check your Email";
             $data["link"] = $url;
         } else {
             $data["success"] = false;
             $data["message"] =
                 "Sorry, no user exists on our system with that email";
         }
         return response()->json($data);
     }

     public function reset_password_school(Request $request)
     {

         $this->validate(
             $request,
             ["password" => "required", "cpassword" => "required"],
             [
                 "password" => "Password Field is required",
                 "cpassword" => "Confirm password field is required",
             ]
         );
         $id = $request->input("id");
         $datas = $request->except(["_token", "password", "cpassword"]);

         $password = Hash::make($request->input("password"));
         User::where(["id" => $id])->update(["password" => $password]);

         $datas["success"] = true;
         $datas["message"] = "Reset Password successfully";
         $datas["redirect"] = url("schools");
         return response()->json($datas);
     }

     public function reset_passwords_school($id)
     {

         $data["id"] = decrypt($id);

         $token = link::where('user_id','=',decrypt($id))
         ->where('created_at','>',Carbon::now()->subHours(24))
         ->where("link_type", "=", 'password')
         ->first();
         if($token){

            return view("school.resetpassword")->with($data);
         }else{
             print_r('The password reset link has expired.');
         }

     }

    public function loginWithToken(Request $request) {
        $school = User::where('id',decrypt($request->id))->first();
        Auth::login($school);
        $request
            ->session()
            ->put("schoolloginsession", [
                "id" => $school->id,
                "name" => $school->first_name,
                "email" => $school->email,
                "type" => '6',
            ]);
        return redirect(url("/school-dashboard"));
    }

    public function loginwithemailschool(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required'
        ], [
            'email.required' => 'Email Field is required',
            'password.required' => 'Password Field is required',
        ]);

        $email = $request->email;
        $password = $request->password;

        $user = User::where('email', $email)->first();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Login Failed, please check email id']);
        }

        // Check user type, status, and credentials
        if ($user->type != 6 || !FacadesHash::check($password, $user->password)) {
            return response()->json(['success' => false, 'message' => 'Login Failed. Please check your password']);
        }
        if ($user->status == 0) {
            return response()->json(['success' => false, 'message' => 'Your account is deactivated']);
        }
        if ($user->status == 2) {
            return response()->json(['success' => false, 'message' => 'Your account is deleted']);
        }

        FacadesAuth::login($user);
        FacadesSession::forget('userewlogin');
        session(['schoolloginsession' => ['id' => $user->id, 'name' => $user->first_name, 'email' => $user->email, 'type' => $user->type]]);

        // try {
        //     $baseUrl = config('services.node.url', 'http://localhost:8000');
        //     $apiUrl = "$baseUrl/user/token?userId={$user->id}&userType=user";

        //     $client = new \GuzzleHttp\Client();
        //     $response = $client->get($apiUrl, ['headers' => ['Accept' => 'application/json']]);

        //     $chatToken = $response->getBody()->getContents();
        //     if (!empty($chatToken)) {
        //         session(['chatToken' => $chatToken]);
        //     }
        // } catch (Exception $e) {
        //     return response()->json(['success' => false, 'message' => 'Error fetching token: ' . $e->getMessage()]);
        // }

        session(['user_timezone' => $request->input('timezone')]);
        return response()->json(['success' => true, 'message' => 'Login Successful', 'redirect' => url('/school-dashboard')]);
    }

    public function emaillink(Request $request) {
        try {
            // Attempt to decrypt the ID from the request
            $id = decrypt($request->id);
        } catch (DecryptException $e) {
            // Handle decryption failure
            return redirect()->route('error.page')->with('error', 'The link is invalid or has expired.');
        }

        // Find the token for the given user ID and ensure it's within the valid timeframe
        $token = link::where('user_id', $id)
            ->where('created_at', '>', Carbon::now()->subHours(24))
            ->where("link_type", "=", 'onboarding')
            ->first();

        if ($token) {
            // Find the user by the decrypted ID
            $user = User::find($id);
            if ($user) {
                // Log in the user
                Auth::login($user);

                // Store user details in session
                $request->session()->put("userewlogin", [
                    "id" => $user->id,
                    "name" => $user->first_name,
                    "email" => $user->email,
                    "type" => $user->type,
                ]);

                // Redirect to the onboarding step with the encrypted ID
                return redirect("/onboarding-step/" . $request->id);
            } else {
                // User not found (possibly deleted)
                return redirect()->route('error.page')->with('error', 'The link is invalid or has expired.');
            }
        } else {
            // Token not found or expired
            return redirect()->route('error.page')->with('error', 'The link is invalid or has expired.');
        }
    }

    public function sendMsg(Request $request)
    {

        $to_id= Auth::user()->id;
        $from_id=$_POST['from_id'];
        $lastmsg = LastChatModel::where(function ($query) use ($from_id,$to_id) {
            $query->where(['from_id' => $from_id, 'to_id' => $to_id]);
        })->orWhere(function ($query) use ($from_id,$to_id) {
            $query->where(['from_id' => $to_id, 'to_id' => $from_id]);
        })->orderBy('id', 'asc')->first();



        if (!empty($lastmsg)) {
            return response()->json([
                "success" => false
            ]);
        } else {
            LastChatModel::insert(['from_id' => $from_id, 'to_id' => $to_id, 'message' =>'', 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')]);
            return response()->json([
                "success" => true
            ]);
        }
    }

    // ********Reset-Platform-School-Password********
    public function resetPlatformSchoolPassword($user_id)
    {
        $marketplaceExists = Users::where("id", $user_id)->where("cust_type", "Platform")->first();
        if (!$marketplaceExists) {
            return redirect()->back();
        }
        return view("school.resetplatformschoolpassword", compact("user_id", "marketplaceExists"));
    }
    // ********Reset-Platform-School-Password********

    // ********Reset-Platform-School-Password-Functionality********
    public function resetPlatformSchoolPasswordFrm(Request $request)
    {
        try {
            $request->validate([
                'password' => 'required|string|min:8',
                'confirm_password' => 'required|string|same:password',
                'temp_password' => 'required|string',
                'user_id' => 'required|exists:users,id',
            ]);

            $user = User::find($request->user_id);
            if (!FacadesHash::check($request->temp_password, $user->password)) {
                return response()->json(['success' => false, 'message' => 'Invalid temporary password.'], 401);
            }

            $user->password = FacadesHash::make($request->password);
            $user->temp_password_changed = 1;
            $user->temp_password = null;
            $user->save();

            FacadesAuth::login($user);
            session(['schoolloginsession' => ['id' => $user->id, 'name' => $user->first_name, 'email' => $user->email, 'type' => $user->type, 'cust_type' => $user->cust_type]]);

            $template = FacadesDB::table("tbl_email_templates")->where("email_template_id", 48)->first();
            if ($template) {
                $userName = $user->first_name;
                $body = str_replace('{{NAME}}', $userName, $template->description);
                try {
                    FacadesMail::send('template', ['template' => $body], function ($message) use ($user, $template) {
                        $message->to($user->email)->subject($template->subject);
                    });
                } catch (Exception $e) {
                    Log::error('Email sending error: ' . $e->getMessage());
                }
            }
            return response()->json(['success' => true, 'message' => 'Password changed successfully.', 'redirect' => route('new-school.dashboard')]);
        } catch (ValidationException $ex) {
            return response()->json(['success' => false, 'errors' => $ex->errors(), 'message' => 'Validation failed.'], 422);
        } catch (Exception $e) {
            Log::error('Password reset error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Something went wrong. Please try again later.'], 500);
        }
    }
    // ********Reset-Platform-School-Password-Functionality********
}


