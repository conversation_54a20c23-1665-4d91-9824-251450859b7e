<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests;

// Helpers

use App\Helpers\NotificationHelper;
use App\Helpers\DataTableHelper;
use App\EmailTemplate;

// utils

use DB;
use Hash;
use Mail;
use Auth;
use Session;
use Exception;
use Validator;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Hash as FacadesHash;
use Illuminate\Support\Facades\Mail as FacadesMail;
use Illuminate\Support\Facades\Validator as FacadesValidator;

// Models
use App\OnboardingInstructor;
use App\InstructorThirdStepOnboardingModel;
use App\InstructorSubjectsThirdStepOnboardingModel;
use App\Models\v1\UserAvailability;
use App\Models\v1\UserAdditionalSubject;
use App\Models\v1\UserNotificationPreference;
use App\Models\v1\UserOnboardingFinalization;

class ManageNewEducatorController extends Controller {
    public function index(Request $request) {
        
        if ($request->ajax()) {
            $query = UserOnboardingFinalization::query();
            $query->addSelect([
                    'name' => OnboardingInstructor::selectRaw('new_onboarding_instructor.first_name')
                            ->whereColumn('user_onboarding_finalization_v1.user_id', 'new_onboarding_instructor.id')
                            ->limit(1),
                    'email' => OnboardingInstructor::selectRaw('new_onboarding_instructor.email')
                            ->whereColumn('user_onboarding_finalization_v1.user_id', 'new_onboarding_instructor.id')
                            ->limit(1),
                    'state' => OnboardingInstructor::selectRaw('new_onboarding_instructor.state')
                            ->whereColumn('user_onboarding_finalization_v1.user_id', 'new_onboarding_instructor.id')
                            ->limit(1),
                    'city' => OnboardingInstructor::selectRaw('new_onboarding_instructor.city')
                            ->whereColumn('user_onboarding_finalization_v1.user_id', 'new_onboarding_instructor.id')
                            ->limit(1),
                    'add_more_subjects' => UserOnboardingFinalization::selectRaw(
                                "CASE 
                                    WHEN user_onboarding_finalization_v1.add_more_subjects = 1 THEN 'Yes'
                                    ELSE 'No'
                                END as add_more_subjects"
                            )
                            ->whereColumn('user_onboarding_finalization_v1.user_id', 'user_onboarding_finalization_v1.user_id')
                            ->limit(1),
                    'availability_time' => UserAvailability::selectRaw(
                                "GROUP_CONCAT(
                                    CONCAT(
                                        day_of_week, ' ',
                                        DATE_FORMAT(start_time, '%h:%i %p'), '-', DATE_FORMAT(end_time, '%h:%i %p')
                                    ) SEPARATOR ',\n'
                                )"
                            )
                            ->whereColumn('user_availability_v1.user_id', 'user_onboarding_finalization_v1.user_id')
                            ->groupBy('user_availability_v1.user_id'),
                    'subjects' => InstructorSubjectsThirdStepOnboardingModel::selectRaw('onboarding_instructor_subjects.sub_subject')
                            ->whereColumn('onboarding_instructor_subjects.user_id', 'user_onboarding_finalization_v1.user_id')
                            ->groupBy('onboarding_instructor_subjects.user_id'),
                    'teaching_preference' => InstructorThirdStepOnboardingModel::selectRaw('onboarding_instructor_teaching_preferences.program_type')
                            ->whereColumn('onboarding_instructor_teaching_preferences.user_id', 'user_onboarding_finalization_v1.user_id')
                            ->limit(1),
                    'format' => InstructorThirdStepOnboardingModel::selectRaw('onboarding_instructor_teaching_preferences.format')
                            ->whereColumn('onboarding_instructor_teaching_preferences.user_id', 'user_onboarding_finalization_v1.user_id')
                            ->limit(1),
                ]);
            $params = DataTableHelper::getParams($request);
            // $query->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');
            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);
            return DataTableHelper::generateResponse($params['draw'], $count, $result);
        }

        return view('admin.marketplace.educator.index');
    }

    public function show($id) {
        $educator = UserOnboardingFinalization::with(['instructor', 'additionalSubjects', 'availability'])
            ->findOrFail($id);

        return view('admin.marketplace.educator.show', compact('educator'));
    }

    public function edit($id) {
        $educator = UserOnboardingFinalization::with(['instructor', 'additionalSubjects', 'availability'])
            ->findOrFail($id);

        return view('admin.marketplace.educator.edit', compact('educator'));
    }

    public function update(Request $request, $id) {
        $educator = UserOnboardingFinalization::findOrFail($id);
        $data = $request->validate([
            'user_status' => 'required|string',
            'application_start_date' => 'required|date',
            'application_end_date' => 'required|date',
            'instructor_id' => 'required|exists:users,id',
            'notification_preferences' => 'array',
            'additional_subjects' => 'array',
            'availability' => 'array',
        ]);

        $educator->update($data);

        return redirect()->route('admin.marketplace.educator.index')
            ->with('success', 'Educator updated successfully.');
    }

    public function destroy($id) {
        $educator = UserOnboardingFinalization::findOrFail($id);
        $educator->delete();
        return redirect()->route('admin.marketplace.educator.index')
            ->with('success', 'Educator deleted successfully.');
    }

    public function export(Request $request) {
        $educators = UserOnboardingFinalization::with(['instructor', 'additionalSubjects', 'availability'])
            ->get();
        $filename = 'educators_' . Carbon::now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=' . $filename,
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];
        $handle = fopen('php://output', 'w');
        fputcsv($handle, [
            'ID', 'Name', 'Email', 'Status', 'Application Start Date', 'Application End Date',
            'Notification Preferences', 'Additional Subjects', 'Availability'
        ]);
        foreach ($educators as $educator) {
            fputcsv($handle, [
                $educator->id,
                $educator->instructor->full_name,
                $educator->instructor->email,
                $educator->user_status,
                $educator->application_start_date,
                $educator->application_end_date,
                json_encode($educator->notificationPreferences->pluck('notification_type')),
                json_encode($educator->additionalSubjects->pluck('subject_name')),
                json_encode($educator->availability->pluck('availability_time')),
            ]);
        }
        fclose($handle);
        return response()->stream(function () use ($handle) {
            fclose($handle);
        }, 200, $headers);
    }
}