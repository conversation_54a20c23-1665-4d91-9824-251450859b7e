<?php if($useModal): ?>
  <!-- Modal Trigger -->
  <button class="btn btn-primary my-4" data-bs-toggle="modal" data-bs-target="#<?php echo e($modalId); ?>">
    <?php echo e($triggerButtonLabel); ?>

  </button>

  <!-- Modal -->
  <div class="modal fade" id="<?php echo e($modalId); ?>" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content border-0 rounded-3 shadow-lg">
        <div class="modal-header">
          <h5 class="modal-title"><?php echo e($modalTitle); ?></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <?php echo $__env->make('components.marketplace.educator._progress-stepper', ['steps' => $steps, 'useModal' => true, 'progress' => $progress, 'requiredCompletedCount' => $requiredCompletedCount, 'requiredCount' => $requiredCount], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
      </div>
    </div>
  </div>

  <?php if (! $__env->hasRenderedOnce('63b757b6-fc01-44c5-ac54-324f95073955')): $__env->markAsRenderedOnce('63b757b6-fc01-44c5-ac54-324f95073955'); ?>
  <?php $__env->startPush('scripts'); ?>
  <script>
  $(function() {
    $('#<?php echo e($modalId); ?>').on('shown.bs.modal', function() {
      $('#<?php echo e($modalId); ?> .btn-outline-primary').first().focus();
    })
  });
  </script>
  <?php $__env->stopPush(); ?>
  <?php endif; ?>

<?php else: ?>
  <!-- Inline -->
  <div class="shadow rounded-xl p-4" id="<?php echo e($modalId); ?>">
    <h5 class="mb-3"><?php echo e(isset($modalTitle) ? $modalTitle : ''); ?></h5>
    <?php echo $__env->make('components.marketplace.educator._progress-stepper', ['steps' => $steps, 'useModal' => false, 'progress' => $progress, 'requiredCompletedCount' => $requiredCompletedCount, 'requiredCount' => $requiredCount], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  </div>
  
  <?php if (! $__env->hasRenderedOnce('10d58242-1dd2-447b-a8a2-71b4a50a4ca8')): $__env->markAsRenderedOnce('10d58242-1dd2-447b-a8a2-71b4a50a4ca8'); ?>
  <?php $__env->startPush('scripts'); ?>
  <script>
  $(function() {
    $('#<?php echo e($modalId); ?>').on('shown.bs.modal', function() {
      $('#<?php echo e($modalId); ?> .btn-outline-primary').first().focus();
    })
  });
  </script>
  <?php $__env->stopPush(); ?>
  <?php endif; ?>
<?php endif; ?>
<?php /**PATH D:\whizara\whizara\resources\views/components/marketplace/educator/progress-stepper.blade.php ENDPATH**/ ?>