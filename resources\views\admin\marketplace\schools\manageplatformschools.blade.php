@extends('admin.layouts.master')

@section('title')Platform School List | Whizara @endsection

@section('content')
<?php
    $res = get_permission(session('Adminnewlogin')['type']);
    $marketplacePermissions = isset($res['managemarketplace']) ? json_decode($res['managemarketplace'], true) : [];
?>
<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                @if (isset($res['dashboard']))
                    @if (array_key_exists('dashboard', $res))
                        @if (in_array('add', json_decode($res['dashboard'], true)))
                            <li class="breadcrumb-item"><a href="{{ url('admin-dashboard') }}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
                        @endif
                    @endif
                @endif
                @if (in_array('platform schools', $marketplacePermissions))
                    <li class="breadcrumb-item active" aria-current="page">{{ __('messages.list_platform_institute') }}</li>
                    <li class="breadcrumb-item active float-right" aria-current="page"><a href="{{ url('admin/k12connections/add-platform-schools') }}">{{ __('messages.add_platform_institute') }}</a></li>
                @endif
            </ol>
        </nav>
        <!-- BREADCRUMB END -->

        <div class="table-responsive">
            <table id="dataTable" class="table table-striped dtlist" style="width:100%">
                <thead class="thead-dark">
                    <tr>
                        <!-- <th>#</th> -->
                        <th>School id</th>
                        <th>Instituts Name</th>
                        <th>{{ __('messages.email') }}</th>
                        <th>Organization Type</th>
                        {{-- <th>Organization Name</th> --}}
                        {{-- <th>{{ __('messages.status') }}</th> --}}
                        <th>{{ __('messages.registration_date') }}</th>
                        <th class="text-center">{{ __('messages.action') }}</th>
                    </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        </div>
        <!-- END -->
        <!-- EDIT PROFILE SECTION END -->
    </div>
</main>
<!-- MAIN SECTION END -->
@endsection

@section('scripts')
<link rel="stylesheet" href="{{ asset('css/datatables.min.css') }}">
<script src="{{ asset('js/datatables.min.js') }}"></script>
<script>
    $(function() {
        if (typeof dataTable !== 'undefined' && dataTable instanceof $.fn.dataTable.Api) {
            dataTable.destroy();
        }
        window.dataTable = initializeAdminDataTable("#dataTable", "{{ url('/admin/k12connections/manage-platform-schools') }}", [
            {data: 'id',},
            {data: 'full_name'},
            {data: 'email'},
            {data: 'organizationtype'},
            // {data: 'organizationname'},
            // {data: 'status', searchable: false, orderable: false},
            {data: 'created_at'},
            {data: 'action', searchable: false, orderable: false},
        ]);
    });

    function status_update(id) {
        var url = base_url + 'change-status-institute';
        var status = $('.changestatuscls-' + id).data('data');
        if (status == 0) {
            confirm_message = 'Are you sure you want to Deactivate ?';
        } else {
            confirm_message = 'Are you sure you want to Deactivate ?';
        }
          update_status(id, url, confirm_message);
    }
</script>
@endsection
