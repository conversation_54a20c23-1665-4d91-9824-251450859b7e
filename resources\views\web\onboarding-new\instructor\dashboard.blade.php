@extends('web.onboarding-new.layouts.master')

@section('title') Home | Whizara @endsection

@section('content')
@php
    // $stepsToRemove = ["step-1:us-work-authorization","step-3:certification","step-3:education","step-3:teaching-experience","step-3:resume","step-4:grade-levels","step-4:subject","step-4:proficiency","step-4:format","step-4:schedule","step-5:profile","step-6:assessment","step-7:agreement"];
    $pending = [
        'step-1' => 'Us Work Authorization',
        'step-3' => 'Education & Experience',
        'step-4' => 'Your Preferences',
        'step-5' => 'Profile',
        'step-6' => 'Assessment:Quiz',
    ];

    $pendingCount = 0;
    $totalCount = 5;
    $id = encrypt(auth()->guard('instructor')->user()->id);
@endphp
<div class="m-5 d-flex justify-content-center align-items-center alert-incomplete-profile">
    @if (!empty($pendingData))
        <!-- Pending Data (Incomplete Profile) -->
        <h5 class="py-3 pb-3 px-5 d-flex gap-2" style="background-color: #ffff005c; border-radius: 10px;">
            <svg width="18px" height="18px" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img"
                class="iconify iconify--emojione" preserveAspectRatio="xMidYMid meet">
                <path
                    d="M5.9 62c-3.3 0-4.8-2.4-3.3-5.3L29.3 4.2c1.5-2.9 3.9-2.9 5.4 0l26.7 52.5c1.5 2.9 0 5.3-3.3 5.3H5.9z"
                    fill="#ffce31">
                </path>
                <g fill="#231f20">
                    <path d="M27.8 23.6l2.8 18.5c.3 1.8 2.6 1.8 2.9 0l2.7-18.5c.5-7.2-8.9-7.2-8.4 0">
                    </path>
                    <circle cx="32" cy="49.6" r="4.2">
                    </circle>
                </g>
            </svg>
            Note:  Your application is incomplete.<a style="text-decoration-line: underline;" href="{{url('k12connections/application')}}">Please complete your application and submit!</a>
            <!-- Note: Your profile is Missing Qualifications, Complete Your Profile To Become A Vetted Teacher And Start Earning! <a style="text-decoration-line: underline;" href="{{url('k12connections/application')}}">Complete Profile Now</a> -->
        </h5>
    @elseif(empty($pendingData) && $instructor->user_status == 'InProgress')
        <!-- InProgress Status -->
        <h5 class="py-3 pb-3 px-5 d-flex gap-2" style="background-color: #ffff005c; border-radius: 10px;">
            <svg width="18px" height="18px" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img"
                class="iconify iconify--emojione" preserveAspectRatio="xMidYMid meet">
                <path
                    d="M5.9 62c-3.3 0-4.8-2.4-3.3-5.3L29.3 4.2c1.5-2.9 3.9-2.9 5.4 0l26.7 52.5c1.5 2.9 0 5.3-3.3 5.3H5.9z"
                    fill="#ffce31">
                </path>
                <g fill="#231f20">
                    <path d="M27.8 23.6l2.8 18.5c.3 1.8 2.6 1.8 2.9 0l2.7-18.5c.5-7.2-8.9-7.2-8.4 0">
                    </path>
                    <circle cx="32" cy="49.6" r="4.2">
                    </circle>
                </g>
            </svg>
            Your application is ready for submission. <a style="text-decoration-line: underline;" href="{{url('k12connections/application')}}">Submit for Review</a>
        </h5>
    @elseif(empty($pendingData) && ($instructor->user_status == 'UnderReview' || $instructor->user_status == 'ChangeRequested'))
        <!-- UnderReview or ChangeRequested Status -->
        <h5 class="py-3 pb-3 px-5 d-flex gap-2" style="background-color:#ffff005c; border-radius: 10px;">
            <svg fill="#0dae0a" width="25px" height="25px" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" stroke="#0dae0a">
                <path d="M400,48H112a64.07,64.07,0,0,0-64,64V400a64.07,64.07,0,0,0,64,64H400a64.07,64.07,0,0,0,64-64V112A64.07,64.07,0,0,0,400,48ZM364.25,186.29l-134.4,160a16,16,0,0,1-12,5.71h-.27a16,16,0,0,1-11.89-5.3l-57.6-64a16,16,0,1,1,23.78-21.4l45.29,50.32L339.75,165.71a16,16,0,0,1,24.5,20.58Z"/>
            </svg>
            Your application has been submitted. <span style="font-weight: 300;">You will hear from us shortly.</span>
        </h5>
    @elseif(empty($pendingData) && ($instructor->user_status == 'Active'))
        <!-- Active Status -->
        <h5 class="py-3 pb-3 px-5 d-flex gap-2" style="background-color:rgb(75 186 15 / 98%); border-radius: 10px; color: white;">
            <svg width="25px" height="25px" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--emojione" preserveAspectRatio="xMidYMid meet" fill="#000000">
                <path fill="#f7b600" d="M2 61l8.6-3l-6.5-3z"></path>
                <path fill="#ffdd7d" d="M26.9 36.4L14.8 24.2l-2 5.6z"></path>
                <path fill="#f7b600" d="M12.8 29.8l-2.2 6.3l26.8 12.5l1.3-.4l-11.8-11.8z"></path>
                <path fill="#ffdd7d" d="M8.5 42.4l20 9.3l8.9-3.1l-26.8-12.5z"></path>
                <path fill="#f7b600" d="M6.3 48.7l13.2 6.2l9-3.2l-20-9.3z"></path>
                <path fill="#ffdd7d" d="M6.3 48.7L4.1 55l6.5 3l8.9-3.1z"></path>
            </svg>
            Your Whizara K-12 Connections account is now active!
        </h5>
    @endif
</div>
<div class="container mt-5">
    <div class="pt-5">
        <div class="row">
            @php
                $steps = [
                    [
                        'title' => 'Add Availability',
                        'required' => true,
                        'completed' => false,
                        'icon' => 'fas fa-calendar',
                        'action_label' => 'Add Now',
                        'hidePrevButton' => true,
                        'btn_click' => 'handleOnboardingStepClick(this)',
                    ],
                    [
                        'title' => 'Add More Subjects',
                        'required' => true,
                        'completed' => false,
                        'icon' => 'fas fa-list',
                        'action_label' => 'Add Now',
                        'btn_click' => 'handleOnboardingStepClick(this)',
                    ],
                    [
                        'title' => 'Available for substitute requirements',
                        'required' => true,
                        'completed' => false,
                        'icon' => 'fas fa-book',
                        'action_label' => 'Add Now',
                        'btn_click' => 'handleOnboardingStepClick(this)',
                    ],
                    [
                        'title' => 'Add Phone Number',
                        'required' => true,
                        'completed' => false,
                        'icon' => 'fas fa-phone',
                        'action_label' => 'Add Now',
                        'btn_click' => 'handleOnboardingStepClick(this)',
                    ],
                    [
                        'title' => 'Allow Notifications',
                        'required' => true,
                        'completed' => false,
                        'icon' => 'fas fa-bell',
                        'action_label' => 'Update Now',
                        'nextButtonText' => 'Save & Finish',
                        'btn_click' => 'handleOnboardingStepClick(this)',
                    ],
                ];
            @endphp
            @if (empty($pendingData) && ($instructor->user_status == 'Active'))
            <div class="col-lg-6">
                <x-marketplace.educator.progress-stepper :modalId="'onboardingProgressStepper'" :modalTitle="'Setup Steps Overview'" :useModal='false' :steps='$steps' />
            </div>
            @else
            <div class="col-lg-6">
                <div class="card_padding w-100">
                    <div class="row">
                        <div class="col-lg-6">
                            <p class="color_primary fs-18 fw-600">Application Completion</p>
                            @foreach ($pending as $stepKey => $stepValue)
                                @php
                                    // Check if the current step exists in pendingData (partial match)
                                    $isStepPending = false;

                                    if (!empty($pendingData)) {
                                        foreach ($pendingData as $pendingStep) {
                                            if (strpos($pendingStep, $stepKey) !== false) {
                                                $isStepPending = true;
                                                break;
                                            }
                                        }
                                    }
                                @endphp
                                @if (!$isStepPending)
                                    @php
                                        $pendingCount++;
                                    @endphp
                                    <p class="mt-4">
                                        <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <g id="SVGRepo_bgCarrier" stroke-width="0" />
                                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />
                                            <g id="SVGRepo_iconCarrier">
                                                <path d="M4.89163 13.2687L9.16582 17.5427L18.7085 8" stroke="#0EA300" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" />
                                            </g>
                                        </svg>
                                        {{ $stepValue }}
                                    </p>
                                @else
                                    <p class="mt-4">
                                        <svg width="20px" height="20px" viewBox="2 3 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">

                                            <g id="SVGRepo_bgCarrier" stroke-width="0" />

                                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" />

                                            <g id="SVGRepo_iconCarrier">
                                                <path d="M16 8L8 16M8.00001 8L16 16" stroke="#c70000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                            </g>

                                        </svg>
                                        {{ $stepValue }}
                                    </p>
                                @endif
                            @endforeach
                            <input type="hidden" name="pendingCount" id="pendingCount" value="{{ $pendingCount }}">
                            <input type="hidden" name="totalCount" id="totalCount" value="{{ $totalCount }}">
                        </div>
                        <div class="col-lg-6 mm-20">
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <svg id="completionCircle" width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                                    <!-- Background Circle (remaining part) -->
                                    <circle cx="100" cy="100" r="90" stroke="lightgrey" stroke-width="10" fill="none" />

                                    <!-- Completion Circle (filled part) -->
                                    <circle id="completion" cx="100" cy="100" r="90" stroke="green" stroke-width="10" fill="none" stroke-dasharray="565.48" stroke-dashoffset="0" transform="rotate(-90, 100, 100)" />

                                    <!-- Text -->
                                    <text id="completionText" x="50%" y="50%" text-anchor="middle" dy="7" fill="#004cbd" font-size="36px" font-weight="bold">
                                        0%
                                    </text>
                                    <text id="completionText" x="50%" y="65%" text-anchor="middle" dy="7" fill="#004cbd" font-size="16px" font-weight="600">
                                        Completed
                                    </text>
                                </svg>

                            </div>
                        </div>
                        </row>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

<!-- Bootstrap 5 Modal -->
<div 
  class="modal fade" 
  id="onboardingStepModal"
  tabindex="-1" 
  aria-labelledby="onboardingStepModalLabel" 
>
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content custom_form">
      <div class="modal-body pt-5 text-gray-700">
        <div id="onboardingStepModalContent"></div>
        <div>
        <div class="d-flex justify-content-between align-items-center mt-4">
            <button 
              type="button" 
              class="btn btn-secondary" 
              onclick="openPreviousOnboardingModal()"
            >
              Previous
            </button>
            <div class="d-flex gap-2">
              <button 
                type="button" 
                class="btn btn-secondary" 
                data-bs-dismiss="modal"
              >
                Cancel
              </button>
              <button 
                type="button" 
                class="btn btn-primary" 
                onclick="openNextOnboardingModal()"
              >
                Save & Next
              </button>
            </div>
        </div>
      </div>
    </div>
  </div>
</div>


@endsection

@section('scripts')
<script>
    function handleOnboardingStepClick(button) {
        const stepTitle = button.getAttribute('data-step') || 'Onboarding Step';
        closeOnboardingModal();
        console.log('Step clicked:', stepTitle);

        // Set modal content
        $('#onboardingStepModal').data().current_step = stepTitle;
        updateOnboardingModalContent(stepTitle);

        // Show modal
        $('#onboardingStepModal').modal('show');
    }

    function closeOnboardingModal() {
        $('#onboardingStepModal').modal('hide');
        hideOnboardingSteps();
    }
    function hideOnboardingSteps() {
        $('#onboardingStepModalContent')
            .find('form')
            .each(function () {
                $(this).addClass('d-none');
                $(this).removeClass('d-block');
            });
    }

    function openNextOnboardingModal() {
        const currentStep = $('#onboardingStepModal').data().current_step;

        const $currentStepElement = $('#onboardingProgressStepper').find(
            'ul li[data-step="' + currentStep + '"]'
        );
        const form = $('#onboardingStepModalContent form.d-block');
        
        $.ajax({
            url: "{{ url('k12connections/onboarding') }}?step=" + currentStep,
            type: 'POST',
            data: form.serialize(),
            success: function (response) {
                alertify.success('Saved successfully');
                const $nextStepElement = $currentStepElement.next('li');
                if ($nextStepElement.length === 0) {
                    closeOnboardingModal();
                    console.log('No next step found for:', currentStep);
                    return;
                }
                const nextStepTitle = $nextStepElement.data('step');
                const nextStepButton = $nextStepElement.find('button, a');
                if (nextStepButton.length !== 0) {
                    hideOnboardingSteps();
                    $('#onboardingStepModal').data().current_step = nextStepButton.data().step;
                    updateOnboardingModalContentVisibility(nextStepButton.data().step);
                } else {
                    closeOnboardingModal();
                }
        
                console.log(
                    'Current Step Element:',
                    $currentStepElement,
                    'Current Step:',
                    currentStep,
                    'Next Step Element:',
                    $nextStepElement,
                    'Next Step Title:',
                    nextStepTitle,
                    'Next Step Button:',
                    nextStepButton
                );
            },
            error: function (xhr, status, error) {
                console.error('Error fetching step content:', error, status);
                alertify.error(xhr?.responseJSON?.message || 'Error occurred while saving. Please try again later.');
            },
        });
    }

    function openPreviousOnboardingModal() {
        const currentStep = $('#onboardingStepModal').data().current_step;

        const $currentStepElement = $('#onboardingProgressStepper').find(
            'ul li[data-step="' + currentStep + '"]'
        );
        const $prevStepElement = $currentStepElement.prev('li');

        if ($prevStepElement.length === 0) {
            console.log('No previous step found for:', currentStep);
            closeOnboardingModal();
            return;
        }

        const prevStepTitle = $prevStepElement.data('step');
        const prevStepButton = $prevStepElement.find('button, a');

        if (prevStepButton.length !== 0) {
            hideOnboardingSteps();
            $('#onboardingStepModal').data().current_step = prevStepButton.data().step;
            updateOnboardingModalContentVisibility(prevStepButton.data().step);
        } else {
            closeOnboardingModal();
        }

        console.log(
            'Current Step Element:',
            $currentStepElement,
            'Current Step:',
            currentStep,
            'Previous Step Element:',
            $prevStepElement,
            'Previous Step Title:',
            prevStepTitle,
            'Previous Step Button:',
            prevStepButton
        );
    }

    function updateOnboardingModalContentVisibility(title) {
        switch (title) {
            case 'Add Availability':
                $('#availabilityScheduleForm').removeClass('d-none');
                $('#availabilityScheduleForm').addClass('d-block');
                break;
            case 'Add More Subjects':
                $('#addSubjectsForm').removeClass('d-none');
                $('#addSubjectsForm').addClass('d-block');
                break;
            case 'Available for substitute requirements':
                $('#addSubstitutesForm').removeClass('d-none');
                $('#addSubstitutesForm').addClass('d-block');
                break;
            case 'Add Phone Number':
                $('#addPhoneNumberForm').removeClass('d-none');
                $('#addPhoneNumberForm').addClass('d-block');
                break;
            case 'Allow Notifications':
                $('#notificationForm').removeClass('d-none');
                $('#notificationForm').addClass('d-block');
                break;
            default:
                $('#onboardingStepModalContent').html(
                    '<p>No content available for this step.</p>'
                );
        }
    }

    function initializeSelect2Dashboard() {
        $('.form-select.select2').each(function () {
            $(this).select2({
                placeholder: this.placeholder,
                allowClear: true,
                width: '100%',
                dropdownParent: $('#onboardingStepModal'),
            });
        });
    }

    function updateOnboardingModalContent(title) {
        if ($('#onboardingStepModalContent').html().trim() !== '') {
            updateOnboardingModalContentVisibility(title);
            return;
        }
        $.ajax({
            url: "{{ url('k12connections/onboarding') }}",
            type: 'GET',
            data: {
                step_title: title,
                _token: '{{ csrf_token() }}',
            },
            success: function (response) {
                $('#onboardingStepModalContent').html(response);
                updateOnboardingModalContentVisibility(title);
                updateAllSubjectAreaValues();
                initializeSelect2Dashboard();
            },
            error: function (xhr, status, error) {
                console.error('Error fetching step content:', error);
                $('#onboardingStepModalContent').html(
                    '<p>Error loading content. Please try again later.</p>'
                );
            },
        });
    }
    document.addEventListener('DOMContentLoaded', function () {
        fetchSubjectAreas();

        $('body').on('click', '#addMoreSubjects', function () {
            var $nextRow = $('.subject-row.d-none').first();
            if ($nextRow.length) {
                $nextRow.removeClass('d-none');
            }
        });

        $('body').on('change', 'input[name="day_available[]"]', function() {
            console.log('Checkbox changed:', this);
            const $checkbox = $(this);
            const isChecked = $checkbox.is(':checked');

            // Find the parent <li class="day_container">
            const $dayContainer = $checkbox.closest('.day_container');

            // Find the related .details_container and .add_button_availability inside this li
            const $detailsContainer = $dayContainer.find('.details_container');
            const $addButton = $dayContainer.find('.add_button_availability');

            if (isChecked) {
            // Show both elements
            $detailsContainer.removeClass('d-none').show();
            $addButton.removeClass('d-none').show();
            $addButton.trigger('click');
            } else {
            // Hide and clear details, hide add button
            $detailsContainer.empty().hide();
            $addButton.hide();
            }
        });

        $('body').on('click', '.add_button_availability', function () {
            const $button = $(this);
            const day = $button.find('#days_add_more_button').data('container');
            const $dayContainer = $button.closest('.day_container');
            const $detailsContainer = $dayContainer.find('.details_container');

            // ✅ Count existing entries for this day:
            const dayIndex = $detailsContainer.children().length;

            // Clone the template
            const $clone = $('#time_entries_clone').clone().removeClass('d-none').addClass('d-flex').removeAttr('id');

            // Replace [0] with dayIndex
            $clone.find('input[data-name^="availability[0]"]').each(function() {
                const $input = $(this);
                const name = ($input.data().name || '').replace('[0]', `[${dayIndex}]`);
                $input.attr('name', name);

                if ($input.attr('name').includes('[day_of_week]')) {
                $input.val(day);
                }
            });

            $clone.find('select[data-name^="availability[0]"]').each(function() {
                const $select = $(this);
                const name = ($select.data().name || '').replace('[0]', `[${dayIndex}]`);
                $select.attr('name', name);
            });

            $detailsContainer.append($clone);
        });

        $('body').on('click', '.remove_time_entry', function() {
            const $entry = $(this).parent(); // adjust selector to match your structure
            $entry.remove();
        });
        $('body').on('click', '.remove-subject', function () {
            $(this).closest('.subject-row').addClass('d-none');
        });
    });

</script>

@endsection
