<!-- Progress Bar -->
<div class="mb-3">
  <div class="progress" style="height: 8px;">
    <div class="progress-bar bg-primary" style="width: <?php echo e(isset($progress) ? $progress : 0); ?>%;"></div>
  </div>
  <small class="text-muted d-block mt-1">
    <?php echo e($requiredCompletedCount); ?> of <?php echo e($requiredCount); ?> required steps completed
  </small>
</div>

<!-- Steps List -->
<ul class="list-group list-group-flush">
  <?php $__currentLoopData = $steps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
  <li class="list-group-item d-flex align-items-center" data-step="<?php echo e($step['title']); ?>" data-next-button="<?php echo e($step['nextButtonText'] ?? ''); ?>" data-prev-button="<?php echo e(isset($step['hidePrevButton']) ? $step['hidePrevButton'] : 'false'); ?>">
    <?php if(isset($step['icon'])): ?>
    <i class="<?php echo e($step['icon']); ?> fs-4 me-3 text-primary"></i>
    <?php endif; ?>
    <div class="flex-grow-1">
      <div class="fw-medium" data-required="<?php echo e($step['required']); ?>"><?php echo e($step['title']); ?></div>
    </div>
    <?php if(isset($step['action_link']) || isset($step['btn_click'])): ?>
      <?php if($step['completed']): ?>
        <span class="badge bg-success"> right </span> 
      <?php else: ?> 
        <?php if(isset($step['action_link'])): ?>
        <a href="<?php echo e($step['action_link']); ?>" class="btn btn-sm btn-outline-primary ms-3 hover:text-white">
          <?php echo e($step['action_label']); ?>

        </a>
        <?php elseif(isset($step['btn_click'])): ?>
        <button class="btn btn-outline-primary ms-3 hover:text-white" data-step="<?php echo e($step['title']); ?>" onclick="<?php echo e($step['btn_click']); ?>">
          <?php echo e($step['action_label']); ?>

        </button>
        <?php endif; ?>
      <?php endif; ?>
    <?php endif; ?>
  </li>
  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH D:\whizara\whizara\resources\views/components/marketplace/educator/_progress-stepper.blade.php ENDPATH**/ ?>