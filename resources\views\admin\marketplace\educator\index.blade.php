@extends('admin.layouts.master')

@section('title')
Manage Educator | Whi<PERSON>a
@endsection

@section('content')
<div class="table-responsive">
    <table class="table table-striped" style="width:100%" id="dataTable">
        <thead class="thead-dark">
            <tr>
                <th>User Id</th>
                <th>Name</th>
                <th>Email</th>
                <th>State</th>
                <th>City</th>
                <th>Subjects</th>
                <th>Substitute</th>
                <th>Availability</th>
                <th>Teaching Preference</th>
                <th>Format</th>
                <th>Onboarding</th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>
</div>
@endsection

@section('scripts')
<link rel="stylesheet" href="{{ asset('css/datatables.min.css') }}">
<script src="{{ asset('js/datatables.min.js') }}"></script>
<script>
    $(function() {
        if (typeof dataTable !== 'undefined' && dataTable instanceof $.fn.dataTable.Api) {
            dataTable.destroy();
        }
        window.dataTable = initializeAdminDataTable("#dataTable",
            "{{ route('admin.manage-educator.index') }}", [{
                    data: 'user_id',
                    searchable: false,
                    visible: false,
                },
                {
                    data: 'name',
                },
                {
                    data: 'email',
                },
                {
                    data: 'state',
                },
                {
                    data: 'city',
                },
                {
                    data: 'subjects',
                },
                {
                    data: 'add_more_subjects',
                },
                {
                    data: 'availability_time',
                },
                {
                    data: 'teaching_preference',
                },
                {
                    data: 'format',
                },
                {
                    data: 'status',
                },
                // {
                //     data: 'action',
                //     orderable: false,
                //     searchable: false,
                // },
            ],
        );
    });
</script>
@endsection