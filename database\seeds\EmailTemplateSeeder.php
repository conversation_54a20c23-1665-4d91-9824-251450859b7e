<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EmailTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('tbl_email_templates')->insert([
            'title' => 'Change Platform School Password',
            'subject' => 'Your Password has been changed successfully',
            'description' => <<<EOT
                                <center>
                                    <div style="width:100%;  background:#ffffff; padding:30px 20px; text-align:left; font-family: 'Arial', sans-serif; margin-top: 22px;">
                                        <a href="http://**************:8080/" style="width: 180px; display: flex; align-items: center; justify-content: center;">
                                            <img src="https://static.wixstatic.com/media/730ae3_c149b7b9e7bc488e8a1d7f6fc090ef08~mv2.png/v1/fill/w_284,h_79,al_c,q_85,usm_0.66_1.00_0.01,enc_auto/image.png" width="150" height="50" style="display:block; margin-bottom:40px;">
                                        </a>

                                        <h1 style="font-size:16px; line-height:22px; font-weight:normal; color:#333333;">Hi {{NAME}}, <span style="font-weight: 500;"> , </span></h1>

                                        <p style="font-size:16px; line-height:24px; color:#505050; margin-bottom:25px;">Welcome to Whizara! Your account has been successfully created. Start by posting a class requirement or exploring our educator  nnetwork.</p>

                                        <p style="font-size:16px; line-height:24px; color:#505050; margin-bottom:15px;">CTA / Link: <a href="http://**************:8080/school-dashboard" style="text-decoration:none;">School Login</a></p>

                                        <div style="background-color: #004CBC; padding: 0 23px; color: white; display: flex; align-items: center; gap: 25px; margin-top: 40px;">
                                            <div style="max-width: 200px;">
                                                <a href="http://**************:8080">
                                                    <img src="https://s3-alpha-sig.figma.com/img/916c/de76/2dee4441490a51b210966a9477a6be9f?Expires=**********&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=J6kyGqL6he0U-vCp59NOIkJ7gCB1h0MZo1ZbX8bPBNQt~uZs7HiDmr-oaMXg5j4NR~tHlhNwb7C06ZV4lwrjh26TU6-lw8SsG5HJLu7-T5tufMp1QXx1cSmOWJONuvqmE9XEt5VOeNv9GO1P1Ev9XgVXkXzOUlOu~KUjB2CleaE9H5FPLENsPwsgNK~1CCAQHlwjp3g7oYtujTiDqMjdeYDrvYriMQHBoFk--Mt~xd74SMOwdxtg7w~M0ZxlHkadAM4DS6-yYNJsEaEliLRVmbOtM9pPCpgyZ8eBA2UJPdbBca2GmcXP3kM-61Stwg3psmuc2YajfETvqkb5AlotjQ__" width="150" height="50" style="display: block;max-width: 168px;">
                                                </a>
                                                <p style="margin: 5px 0; font-size: 13px; margin-top: -11px; transform: translate(2px, -20px);">Created by Learn2Code.Live </p>
                                            </div>
                                            <div class="fotter-text" style="max-width: 400px;">
                                                <p style="margin: 5px 0; line-height: 22px; font-weight: 500; font-size: 14px;">Connecting schools nationwide with high-quality educators </p>
                                                <p style="margin: 5px 0; line-height: 22px; font-weight: 500; font-size: 14px;">5826 New Territory Blvd #323, Sugar Land TX 77479 </p>
                                            </div>
                                        </div>
                                        <div style="text-align: center; padding-top: 50px; text-decoration: underline; text-underline-offset: 2px; color: #004CBD;">
                                            <a href="https://www.whizara.com/notificationpreferences" target="_blank">unsubscribe from notifications</a>
                                        </div>
                                    </div>
                                </center>
                                EOT,
            'status' => 1,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }
}



