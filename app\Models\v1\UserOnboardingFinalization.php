<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;
use App\OnboardingInstructor;

class UserOnboardingFinalization extends Model
{
    protected $table = 'user_onboarding_finalization_v1';

    protected $fillable = [
        'user_id',
        'hours_per_week',
        'add_more_subjects',
        'open_to_substitute_opportunity',
        'phone_number',
        'country_code',
        'progress',
        'status',
    ];

    public function instructor()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'user_id');
    }

    public function additionalSubjects()
    {
        return $this->hasMany(UserAdditionalSubject::class, 'user_id', 'user_id');
    }

    public function availability()
    {
        return $this->hasMany(UserAvailability::class, 'user_id', 'user_id');
    }

}
