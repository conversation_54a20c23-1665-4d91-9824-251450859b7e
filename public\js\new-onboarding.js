var key = 0;
$("body").on("click", "#forgot", function () {
    console.log("clicked span checkbox");
    // Toggle the checkbox state
    const checkbox = $("#agree");
    const isChecked = checkbox.prop("checked");

    // Update the checkbox state
    checkbox.prop("checked", !isChecked);

    // Toggle the styling for the span
    $(this).toggleClass("checked", !isChecked);
});

$("body").on("change", "#profile_type", function () {
    if (this.selectedOptions[0] && this.selectedOptions[0].value == "Other") {
        $(this).closest(".secondno").next()[0].style.display = "block";
    } else {
        $(this).closest(".secondno").next()[0].style.display = "none";
    }
});

$("body").on("click", "#singup-instructor", function () {
    var loading = $(this).attr("data-loading-text");
    var email = document.getElementById("email").value;
    var password = document.getElementById("password").value;
    var first_name = document.getElementById("first_name").value;
    var last_name = document.getElementById("last_name").value;
    var forgot = $("#agree").prop("checked");

    if (email == "") {
        $("#email").css("border-color", "red");
        $("#email_error").html("Please enter valid email address");
    }
    if (password == "") {
        $("#password").css("border-color", "red");
        $("#password_error").html("Please enter your password");
    }
    if (first_name == "") {
        $("#first_name").css("border-color", "red");
        $("#first_name_error").html("Please enter your first name");
    }
    if (last_name == "") {
        $("#last_name").css("border-color", "red");
        $("#last_name_error").html("Please enter your last name");
    }
    if (password != "") {
        const messages = checkPasswordValidity(password);
        if (!messages) {
        } else {
            $("#password_error").html(
                '<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>'
            );
            $("#password").css("border-color", "red");
        }
    }
    if (forgot === false) {
        // $("#forgot").css({
        //     "border": "1px solid #ff0000",
        //     "background-color": "#ffdede"
        // });
    } else {
        $("#forgot").css({
            border: "1px solid #ccc",
            "background-color": "#fff",
        });
    }
    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i;
    var regemailid = emailfilter.test($("#email").val());
    regemailid;
    if (regemailid == false) {
        // $('#email_error').html('Please enter valid email address');
        $("#email").css("border-color", "red");
        $("#email").focus();

        return false;
    } else {
        $("#email_error").html("");
        $("#email").css("border-color", "#d8dadc");
    }

    var password = document.getElementById("password").value;
    if (password.length == "") {
        // $('#password_error').html('Please Enter Your Password');
        $("#password").css("border-color", "red");
        document.getElementById("password").value = "";
        document.getElementById("password").focus();
        return false;
    } else {
        $("#password_error").html("");

        $("#password").css("border-color", "#d8dadc");
    }

    const message = checkPasswordValidity(password);
    if (!message) {
    } else {
        $("#password").css("border-color", "red");
        // Must contain at least one number one symbol one uppercase and one lowercase letter, and at least 10 or more characters
        $("#password_error").html(
            '<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>'
        );
        // document.getElementById('password').value = "";
        document.getElementById("password").focus();
        return false;
    }
    var first_name = document.getElementById("first_name").value;
    if (first_name.length == "") {
        // $('#first_name_error').html('Please Enter Your First Name');
        $("#first_name").css("border-color", "red");
        document.getElementById("first_name").value = "";
        document.getElementById("first_name").focus();
        return false;
    } else {
        $("#first_name_error").html("");
        $("#first_name").css("border-color", "#d8dadc");
    }
    var last_name = document.getElementById("last_name").value;
    if (last_name.length == "") {
        // $('#last_name_error').html('Please Enter Your Last Name');
        $("#last_name").css("border-color", "red");
        document.getElementById("last_name").value = "";
        document.getElementById("last_name").focus();
        return false;
    } else {
        $("#last_name_error").html("");
        $("#last_name").css("border-color", "#d8dadc");
    }
    if (forgot == false) {
        $("#forgot1").css({
            border: "2px solid red",
            outline: "none",
        });
        $("#agree_error").html(
            "You must agree to the privacy policy and terms of use to proceed."
        );
        $("#agree_error").css({
            color: "red",
            "font-weight": "bold",
        });
        return false;
    } else {
        $("#agree_error").html("");
        $("#agree_error").css("border-color", "#d8dadc");
    }
    var $this = $(this);
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var url = APP_URL + "/k12connections/create_instructor";
    $.ajax({
        type: "POST",
        url: url,
        data: $("#signupid").serialize(),
        dataType: "json",
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {
            if (data.success == true) {
                $("#signupid")[0].reset();
                window.location.href =
                    APP_URL + "/k12connections/verify-instructor";
                $this.html("Sign Up");
                $this.prop("disabled", false);
            } else if (data.success == "already") {
                $("#email").css("border-color", "red");
                document.getElementById("email").focus();
                $("#email_error").html(data.message);
                $this.html("Sign Up");
                $this.prop("disabled", false);
            } else {
                $this.html("Sign Up");
                $this.prop("disabled", false);
                alertify.error(data.message);
            }
        },
    }).done(function () {
        $this.html("Sign Up");
        $this.prop("disabled", false);
    });
});

function checkPasswordValidity(value) {
    if (!hasNumber.test(value)) {
        return "Password must contain at least one number.";
    }

    const isNonWhiteSpace = /^\S*$/;
    if (!isNonWhiteSpace.test(value)) {
        return "Password must not contain Whitespaces.";
    }

    const isContainsUppercase = /^(?=.*[A-Z]).*$/;
    if (!isContainsUppercase.test(value)) {
        return "Password must have at least one Uppercase Character.";
    }

    const isContainsLowercase = /^(?=.*[a-z]).*$/;
    if (!isContainsLowercase.test(value)) {
        return "Password must have at least one Lowercase Character.";
    }

    const isContainsNumber = /^(?=.*[0-9]).*$/;
    if (!isContainsNumber.test(value)) {
        return "Password must contain at least one Digit.";
    }

    const isContainsSymbol =
        /^(?=.*[~`!@#$%^&*()--+={}\[\]|\\:;"'<>,.?/_₹]).*$/;
    if (!isContainsSymbol.test(value)) {
        return "Password must contain at least one Special Symbol.";
    }

    const isValidLength = /^.{10,20}$/;
    if (!isValidLength.test(value)) {
        return "Password must be 10-20 Characters Long.";
    }

    return null;
}

$("body").on("click", "#logininstructorbtn", function () {
    var loading = $(this).attr("data-loading-text");

    var email = document.getElementById("email").value;
    var password = document.getElementById("password").value;

    if (email == "") {
        $("#email").css("border-color", "red");
    }
    if (password == "") {
        $("#password").css("border-color", "red");
    }

    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i;
    var regemailid = emailfilter.test($("#email").val());
    regemailid;

    if (regemailid == false) {
        $("#email").css("border-color", "red");
        $("#email").val("");
        // $('.email_error').html('Please enter a valid Email');
        $("#email").focus();
        return false;
    } else {
        $(".email_error").html("");
        $("#email").css("border-color", "#d8dadc");
    }

    var password = $("#password").val();
    if (password == "") {
        $("#password").css("border-color", "red");
        // $('.password_error').html('Please enter your password');
        $("#password").val("");
        $("#password").focus();
        return false;
    } else {
        $(".password_error").html("");
        $("#password").css("border-color", "#d8dadc");
    }
    var $this = $(this);
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var url = APP_URL + "/k12connections/loginwithinstructoremail";
    var formData =
        $("#loginid").serialize() +
        "&timezone=" +
        encodeURIComponent(getUserTimezone());

    $.ajax({
        type: "POST",
        url: url,
        dataType: "json",
        data: formData,
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {
            $(".loader").addClass("d-none");
            if (data.success == true) {
                firebaseauth(data.email, data.password);
                firebasesignin(data.email, data.password);
                alertify.success(data.message);
                $this.html("Login");
                $this.prop("disabled", false);
                window.location.href = data.redirect;
            } else if (data.success == "deactivate") {
                alertify.success(data.message);
                $this.html("Login");
                $this.prop("disabled", false);
                window.location.href = data.redirect;
            } else {
                $this.html("Login");
                $this.prop("disabled", false);
                alertify.error(data.message);
            }
        },
    }).done(function () {
        setTimeout(function () {
            $this.html("Login");
            $this.prop("disabled", false);
        }, 500);
    });
});

$("body").on("click", "#forgotinstructorbtn", function () {
    var loading = $(this).attr("data-loading-text");

    var email = document.getElementById("email").value;

    if (email == "") {
        $("#email").css("border-color", "red");
    }

    var emailfilter = /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i;
    var regemailid = emailfilter.test($("#email").val());
    regemailid;

    if (regemailid == false) {
        $("#email").css("border-color", "red");
        $("#email").focus();

        return false;
    } else {
        $(".email_error").html("");
        $("#email").css("border-color", "#d8dadc");
    }

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var $this = $(this);

    var url = APP_URL + "/k12connections/forgot-instructor-password-frm";
    $.ajax({
        type: "POST",
        url: url,
        dataType: "json",
        data: $("#forgotid").serialize(),
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {
            $("#forgotid")[0].reset();
            if (data.success == true) {
                $this.html("Send");
                $this.prop("disabled", false);
                // alertify.success(data.message);
                $("#danger").hide();
                $("#success")
                    .show()
                    .html("<strong>Success!</strong> " + data.message);

                // .delay(5000).queue(function(n) {
                //     $(this).hide(); n();
                //   });
                //  window.location.href = data.redirect;
            } else {
                $("#success").hide();
                $("#danger")
                    .show()
                    .html("<strong>Danger!</strong> " + data.message);

                // .delay(5000).queue(function(n) {
                //     $(this).hide(); n();
                //   });

                // alertify.error('Incorrect Email');
                $this.html("Send");
                $this.prop("disabled", false);
            }
        },
    });
});

$("body").on("click", "#resetinstructorbtn", function () {
    var loading = $(this).attr("data-loading-text");
    var password = document.getElementById("password").value;

    if (password == "") {
        $("#password").css("border-color", "red");
    }
    var cpassword = document.getElementById("cpassword").value;

    if (cpassword == "") {
        $("#cpassword").css("border-color", "red");
    }
    if (password != "") {
        const message12 = checkPasswordValidity(new_password);
        if (!message12) {
        } else {
            $(".password_error").html(
                '<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>'
            );
            $("#password").css("border-color", "red");
        }
    }
    if (cpassword != "") {
        const message11 = checkPasswordValidity(confirm_password);
        if (!message11) {
        } else {
            $(".confirm_password_error").html(
                '<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>'
            );
            $("#cpassword").css("border-color", "red");
        }
    }

    var new_password = $("#password").val();

    if (new_password == "") {
        $("#password").css("border-color", "red");
        $("#password").focus();
        return false;
    } else {
        $("#password").css("border-color", "");
        $(".password_error").html("");
    }

    const message = checkPasswordValidity(new_password);
    if (!message) {
    } else {
        $(".password_error").html(
            '<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>'
        );

        $("#password").css("border-color", "red");
        // document.getElementById('password').value = "";
        document.getElementById("password").focus();
        return false;
    }

    var confirm_password = $("#cpassword").val();
    if (confirm_password == "") {
        $("#cpassword").css("border-color", "red");
        $("#cpassword").focus();
        return false;
    } else {
        $("#cpassword").css("border-color", "");
        $(".confirm_password_error").html("");
    }
    const message1 = checkPasswordValidity(confirm_password);
    if (!message1) {
    } else {
        $(".confirm_password_error").html(
            '<div class="pmsg">Please enter valid password</div><div class="tooltip custom"><i class="fa fa-info-circle circle" aria-hidden="true"></i><span class="tooltiptext">Please Include:<br>-A lower case letter<br>-An upper case letter<br>-A number<br>-A Symbol: !@#$%^&*<br>-At least 10 characters<br></span></div>'
        );

        $("#cpassword").css("border-color", "red");
        // document.getElementById('password').value = "";
        document.getElementById("cpassword").focus();
        return false;
    }
    if (new_password != confirm_password) {
        $("#cpassword").css("border-color", "red");
        $(".confirm_password_error").html(
            "The password and confirmation password do not match."
        );
        $("#cpassword").focus();
        return false;
    } else {
        $("#cpassword").css("border-color", "");
        $(".confirm_password_error").html("");
    }
    var $this = $(this);
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var url = APP_URL + "/k12connections/reset-instructor-password-frm";
    $.ajax({
        type: "POST",
        url: url,
        dataType: "json",
        data: $("#resetid").serialize(),
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {
            if (data.success == true) {
                $("#resetid")[0].reset();
                $this.html("Submit");
                $this.prop("disabled", false);
                alertify.success(data.message);
                window.location.href = data.redirect;
            } else {
                $this.html("Submit");
                $this.prop("disabled", false);
                alertify.error("Incorrect Email");
            }
        },
    });
});

$("body").on("click", "#submitverifyinstructor", function () {
    var loading = $(this).attr("data-loading-text");
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var $this = $(this);
    var url = APP_URL + "/k12connections/verify_instructor_email";
    $.ajax({
        type: "POST",
        url: url,
        data: $("#verifyemail").serialize(),
        dataType: "json",
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {
            if (data.success == true) {
                $this.html("Resend Email");
                $this.prop("disabled", false);

                $("#verifyemail")[0].reset();
                alertify.success(data.message);
            } else {
                $this.html("Resend Email");
                $this.prop("disabled", false);
                alertify.error(data.message);
            }
        },
    }).done(function () {
        $this.html("Resend Email");
        $this.prop("disabled", false);
    });
});

let lastUploadedResumeFile = "";
$("body").on("change", "#myFile", function () {
    const $this = $(this);
    const file = $this[0]?.files?.item(0);
    if (!file) {
        return;
    }
    const text = $(this).closest(".upload").find(".uploaded-documents");
    const resume_value = $("#resume_value").val();
    const resume_name = $("#resume_name").val();

    if (!file) {
        if (resume_name) {
            text.removeClass("d-none")
                .html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${resume_name}</span>
                <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 " title="Remove" id="resume_remove">
                    <svg style="cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`);
        } else {
            text.addClass("d-none").html("");
        }
        lastUploadedResumeFile = "";
        return;
    }

    if (file && text) {
        var ext = $(this).val().split(".").pop().toLowerCase();
        var fileSizeInBytes = file.size;
        const fileSizeInKB = fileSizeInBytes / 1024;

        if ($.inArray(ext, ["doc", "docx", "ppt", "pdf"]) == -1) {
            if (!text.hasClass("d-none")) {
                text.addClass("d-none");
            }
            $("#resume_error").text(
                "The file format is invalid. Please upload a file in one of the supported formats"
            );
            $(this).addClass("brederror");
            return;
        } else if (Math.round(fileSizeInKB) >= 2048) {
            if (!text.hasClass("d-none")) {
                text.addClass("d-none");
            }
            $("#resume_error").text("File size exceeds the maximum limit.");
            $(this).addClass("brederror");
            return;
        } else {
            // $(".text-icon-2").empty();
            // $(".text-icon-2").html(`
            //     <span class="ms-2 " title="Remove">
            //         <svg style="cursor:pointer" id="resume_remove" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
            //             <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
            //         </svg>
            //     </span>`);

            $("#resume_error").text("");
            $(this).removeClass("brederror");
        }
        text.removeClass("d-none");
        lastUploadedResumeFile = file.name;
        text.html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${file.name}</span>
                <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 " title="Remove">
                    <svg style="cursor:pointer" id="resume_remove" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`);
        $(this).closest(".upload").find(".selectfile").attr("title", file.name);
    }
    // else if (resume_value && text) {
    //     text.innerText = resume_value;
    //     $('#resume_error').text('');
    //     $(this).removeClass('brederror');
    // }
    else {
        if (resume_name != "" && lastUploadedResumeFile == "") {
            text.html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${resume_name}</span>
                <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 " title="Remove" id="resume_remove">
                    <svg style="cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`);
            $(this).closest(".login__form").next(".certificate_err").text("");
            $(this).next("span").removeClass("brederror");
            $(this)
                .closest(".upload")
                .find(".selectfile")
                .attr("title", resume_name);
        } else if (lastUploadedResumeFile != "") {
            text.html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${lastUploadedResumeFile}</span>
                <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 " title="Remove" id="resume_remove">
                    <svg style="cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`);

            $(this)
                .closest(".upload")
                .find(".selectfile")
                .attr("title", lastUploadedResumeFile);
        }
        // else {
        //     text.innerText = 'Upload your resume*';
        //     $('.text-icon-2').html(`<span class="icon selectfile ">
        //                         <svg width="17" height="17" viewBox="0 0 17 17" fill="none"
        //                             xmlns="http://www.w3.org/2000/svg">
        //                             <path
        //                                 d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11"
        //                                 stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        //                             </path>
        //                             <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5"
        //                                 stroke-linecap="round" stroke-linejoin="round"></path>
        //                             <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round"
        //                                 stroke-linejoin="round"></path>
        //                         </svg>
        //                     </span>`);
        //     $('#resume_error').text('');
        //     $(this).removeClass('brederror');
        // }
    }
});

$(document).on("click", "#resume_remove", function () {
    $("#myFile").val("");
    $("#resume_name").val("");
    $("#resume_value").val("");
    $(".resumefiles").text("Upload your resume*");
    $(".text-icon-2").empty();
    text = $(this).closest(".upload").find(".uploaded-documents");
    text.addClass("d-none");

    $(".text-icon-2").html(
        '\
    <span class="icon selectfile"> \
        <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"> \
            <path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" \
                stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"> \
            </path> \
            <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" \
                stroke-linecap="round" stroke-linejoin="round"></path> \
            <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" \
                stroke-linejoin="round"></path> \
        </svg> \
    </span>'
    );
});

let lastUploadedCertificateFile = "";
$("body").on("change", ".certificatefile", function () {
    const $this = $(this);
    const file = $this[0]?.files?.item(0);
    const cert_name = $(this)
        .closest(".login__form")
        .find(".certification_name");
    const text = $(this)
        .closest(".certification-list")
        .find(".uploaded-documents");

    if (!file) return;
    if (!file) {
        if (cert_name.val()) {
            text.removeClass("d-none")
                .html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${cert_name.val()}</span>
                <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 remove-certificate remove_certificate_new" title="Remove">
                    <svg style="position:relative;z-index:5;cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`);
        } else {
            text.addClass("d-none").html("");
        }
        lastUploadedCertificateFile = "";
        return;
    }

    if (file && text) {
        var ext = $(this).val().split(".").pop().toLowerCase();
        if (
            $.inArray(ext, ["doc", "docx", "pdf", "png", "jpg", "jpeg"]) == -1
        ) {
            if (!text.hasClass("d-none")) {
                text.addClass("d-none");
            }
            $(this)
                .closest(".login__form")
                .next(".certificate_err")
                .text(
                    "The file format is invalid. Please upload a file in one of the supported formats."
                );
            $(this).next("span").addClass("brederror");
            return;
        } else if (file && file.size > 1048576) {
            if (!text.hasClass("d-none")) {
                text.addClass("d-none");
            }
            $(this)
                .closest(".login__form")
                .next(".certificate_err")
                .text("File size exceeds the maximum limit.");
            $(this).next("span").addClass("brederror");
            return;
        } else {
            $(this).closest(".login__form").next(".certificate_err").text("");
            $(this).next("span").removeClass("brederror");
        }
        text.removeClass("d-none");
        lastUploadedCertificateFile = file.name;
        text.html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${file.name}</span>
            <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 remove-certificate remove_certificate_new"  title="Remove">
                <svg style="position:relative;z-index:5;cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                    <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                </svg>
            </span>`);
    } else {
        if (cert_name.val() != "" && lastUploadedCertificateFile == "") {
            text.removeClass("d-none");
            text.html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${cert_name.val()}</span>
                <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 remove-certificate remove_certificate_new" title="Remove">
                    <svg style="position:relative;z-index:5;cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`);
            $(this).closest(".login__form").next(".certificate_err").text("");
            $(this).next("span").removeClass("brederror");
        } else if (lastUploadedCertificateFile != "") {
            text.removeClass("d-none");
            text.html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${lastUploadedCertificateFile}</span>
                <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 remove-certificate remove_certificate_new"  title="Remove">
                    <svg style="position:relative;z-index:5;cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`);
        }
        // else {
        //     text.innerHTML = `Upload Certificate/License*
        //                     <svg width="17" height="17" viewBox="0 0 17 17" fill="none"
        //                         xmlns="http://www.w3.org/2000/svg">
        //                         <path
        //                             d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11"
        //                             stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        //                         </path>
        //                         <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5"
        //                             stroke-linecap="round" stroke-linejoin="round"></path>
        //                         <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round"
        //                             stroke-linejoin="round"></path>
        //                     </svg>`;

        //     $(this).closest('.login__form').next('.certificate_err').text('');
        //     $(this).next("span").removeClass('brederror');
        // }
    }
});

$("body").on("click", ".remove_certificate_new", function () {
    // Find the parent container of the clicked button (which is .certificate_text in this case)
    var parentElement = $(this).closest(".certification-list"); // Now the parent is .certificate_text

    // Clear the certificate file and certification name inputs within the same parent
    parentElement.find(".certificatefile").val("");
    parentElement.find(".certificate_value").val("");
    parentElement.find(".certification_name").val("");
    text = parentElement.find(".uploaded-documents");
    text.addClass("d-none");

    // Empty and reset the .certificate_text content within the same parent
    parentElement.find(".certificate_text").html(`
        Upload Certificate/License*
        <svg width="17" height="17"  viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11"
                stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            </path>
            <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5"
                stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round"
                stroke-linejoin="round"></path>
        </svg>
    `);
});

$("body").on(
    "change",
    "#marketplace_first_name, #marketplace_last_name, #marketplace_address",
    function () {
        if (!contractDoc) {
            contractDoc = $(".marketplaceContract").html();
        }
        let contractHtml = contractDoc;
        if (
            $("#marketplace_first_name").val() != "" &&
            $("#marketplace_last_name").val() != ""
        ) {
            const legal_user = `${$("#marketplace_first_name").val()} ${$(
                "#marketplace_last_name"
            ).val()}`;
            contractHtml = contractHtml.replace(/{{LEGAL_NAME}}/g, legal_user);
        } else {
            const legal_user = $("#userName").val();
            contractHtml = contractHtml.replace(/{{LEGAL_NAME}}/g, " ");
        }

        if ($("#marketplace_address").val()) {
            const address = $("#marketplace_address").val();
            contractHtml = contractHtml.replace(/{{ADDRESS}}/g, address);
        } else {
            contractHtml = contractHtml.replace(/{{ADDRESS}}/g, " ");
        }
        $(".marketplaceContract").html(contractHtml);
    }
);

function updateContractDocumentDetails(name) {
    if (!contractDoc) {
        contractDoc = $(".marketplaceContract").html();
    }
    let contractHtml = contractDoc;
    if (name != "") {
        let occurrences = 0;
        contractHtml = contractHtml.replace(
            /{{LEGAL_NAME}}/g,
            function (match) {
                occurrences++;
                if (occurrences === 1) {
                    return name; // Replace the first occurrence with the name
                } else if (occurrences === 2) {
                    return ""; // Replace the second occurrence with an empty string
                }
                return match; // Return the match as is for the first occurrence
            }
        );
    }

    const address = $("#marketplace_address").val();
    contractHtml = contractHtml.replace(/{{ADDRESS}}/g, address);

    $(".marketplaceContract").html(contractHtml);
}

let lastUploadedTranscriptFile = "";
$("body").on("change", ".transcriptfile", function () {
    const $this = $(this);
    const file = $this[0]?.files?.item(0);
    const transcript_name = $(this)
        .closest(".login__form")
        .find(".transcript_name");

    const text = $(this)
        .closest(".removeeducation1")
        .find(".uploaded-documents");

    if (!file) {
        if (transcript_name.val()) {
            text.removeClass("d-none")
                .html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${transcript_name.val()}</span>
                <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 remove-certificate remove_certificate_new" title="Remove">
                    <svg style="position:relative;z-index:5;cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`);
        } else {
            text.addClass("d-none").html("");
        }
        lastUploadedTranscriptFile = "";
        return;
    }

    if (file && text) {
        var ext = $(this).val().split(".").pop().toLowerCase();
        if (
            $.inArray(ext, ["doc", "docx", "pdf", "png", "jpg", "jpeg"]) == -1
        ) {
            if (!text.hasClass("d-none")) {
                text.addClass("d-none");
            }
            $(this)
                .closest(".login__form")
                .next(".transcript_err")
                .text(
                    "The file format is invalid. Please upload a file in one of the supported formats."
                );
            $(this).next("span").addClass("brederror");
            return;
        } else if (file && file.size > 1048576) {
            if (!text.hasClass("d-none")) {
                text.addClass("d-none");
            }
            $(this)
                .closest(".login__form")
                .next(".transcript_err")
                .text("File size exceeds the maximum limit.");
            $(this).next("span").addClass("brederror");
            return;
        } else {
            $(this).closest(".login__form").next(".transcript_err").text("");
            $(this).next("span").removeClass("brederror");
        }
        text.removeClass("d-none");
        lastUploadedTranscriptFile = file.name;
        text.html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${file.name}</span>
            <span style="position:relative;z-index:5;cursor:pointer" id="remove_transscript" class="ms-2 remove-transcript" title="Remove">
                <svg style="position:relative;z-index:5;cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                    <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                </svg>
            </span>`);
    } else {
        if (transcript_name.val() != "" && lastUploadedTranscriptFile == "") {
            text.removeClass("d-none");
            text.innerHTML = `<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${transcript_name.val()}</span>
                <span style="position:relative;z-index:5;cursor:pointer" class="remove-transcript" title="Remove">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`;
            $(this).closest(".login__form").next(".transcript_err").text("");
            $(this).next("span").removeClass("brederror");
        } else if (lastUploadedTranscriptFile != "") {
            text.removeClass("d-none");
            text.html(`<span class="text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">${lastUploadedTranscriptFile}</span>
                <span style="position:relative;z-index:5;cursor:pointer" class="ms-2 remove-certificate remove_certificate_new"  title="Remove">
                    <svg style="position:relative;z-index:5;cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`);
        }
        // else {
        //     text.innerHTML = `Upload Transcript*
        //                 <svg width="17" height="17" viewBox="0 0 17 17" fill="none"
        //                     xmlns="http://www.w3.org/2000/svg">
        //                     <path
        //                         d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11"
        //                         stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        //                     </path>
        //                     <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5"
        //                         stroke-linecap="round" stroke-linejoin="round"></path>
        //                     <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round"
        //                         stroke-linejoin="round"></path>
        //                 </svg>`;
        //     $(this).closest('.login__form').next('.transcript_err').text('');
        //     $(this).next("span").removeClass('brederror');
        // }
    }
});

$(document).on("click", "#remove_transscript", function () {
    var parentElement = $(this).closest(".removeeducation1");
    parentElement.find(".transcriptfile").val("");
    parentElement.find(".transcript_value").val("");
    parentElement.find(".transcript_name").val("");
    text = parentElement.find(".uploaded-documents");
    text.addClass("d-none");

    parentElement.find(".transcript_text").html(`Upload Transcript*
        <svg width="17" height="17" viewBox="0 0 17 17"
            fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11"
                stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round"
                stroke-linejoin="round">
            </path>
            <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7"
                stroke-width="1.5" stroke-linecap="round"
                stroke-linejoin="round">
            </path>
            <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5"
                stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>`);
});

$("body").on("change", "#total_experience", function () {
    const $this = $(this);
    if ($this.val() > 0) {
        $(".apendExperience").removeClass("hide");
        $("#add-experience").removeClass("hide");
        $(".appendReferences").removeClass("hide");
        if ($(".apendExperience > .row").length == 0) {
            $("#add-experience").click();
            if ($(".appendReferences > .row").length == 0) {
                $("#add-reference-experience").click();
            }
        }
    } else if ($this.val() == "") {
        $("#add-experience").removeClass("hide");
        $(".apendExperience").empty();
        $(".appendReferences").empty();
    } else {
        $("#add-experience").addClass("hide");
        $(".apendExperience").empty();
        $(".appendReferences").empty();
    }
});

let contractDoc = "";

$("body").on("change", "#legal_first_name,#legal_last_name", function () {
    if ($("#legal_first_name").val() && $("#legal_last_name").val()) {
        const legal_user = `${$("#legal_first_name").val()} ${$(
            "#legal_last_name"
        ).val()}`;
        const contract = $("#contracthtml");
        if (!contractDoc) contractDoc = contract.html();
        contract.html(contractDoc.replace(/{{LEGAL_NAME}}/g, legal_user));
        $("#agreement_text").val(contract.html());
    }
});

$("body").on("change", "#formatwo,#formathree", function () {
    const selected = $("#formatwo").is(":checked");
    // $('.maps').addClass(selected ? "show": "hide");
    // $('.maps').removeClass(selected ? "hide": "show");

    // if(!window['inperson-map'] && selected){
    //     initMap();
    // }
});

let lastUploadedFileName = "";
let lastUploadedFile = "";
$("body").on("change", "#video_source", function () {
    const $this = $(this);
    const file = $this[0]?.files?.item(0);
    const text = $this[0]?.parentElement.querySelector(".video_uplod");
    const video_name = $(this).closest(".upload_video").find("#video_name");
    if (file && text) {
        var videoExt = $this.val().split(".").pop().toLowerCase();
        var fileSizeInBytes = file.size;
        const fileSizeInMB = fileSizeInBytes / (1024 * 1024);
        if ($.inArray(videoExt, ["mp4", "avi", "mkv", "webm", "mov"]) == -1) {
            $(".video_uplod").css("border-color", "red");
            $("#video_error").text(
                " The file format is invalid. Please upload a file in one of the supported formats."
            );
            document.getElementById("video_source").value = "";
        } else if (Math.round(fileSizeInMB) > 20) {
            // File size exceeds 50 KB

            $(".video_uplod").css("border-color", "red");
            $("#video_error").text("File size exceeds the maximum limit.");
        } else {
            $("#video_error").text("");
            $(".video_uplod").css("border-color", "#ced4da");
        }

        lastUploadedFileName = file.name;
        lastUploadedFile = file;
        text.innerHTML = `${file.name}
            <span class="ms-2" title="Remove" id="remove-video">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                    <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                </svg>
            </span>`;
    } else {
        if (video_name.length > 0 && video_name.val() != "") {
            text.innerHTML = `${video_name.val()}
                <span class="ms-2" title="Remove" id="remove-video">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                        <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                    </svg>
                </span>`;
            $("#video_error").text("");
            $(".video_uplod").css("border-color", "#ced4da");
        } else {
            if (lastUploadedFileName) {
                let dataTransfer = new DataTransfer();
                dataTransfer.items.add(lastUploadedFile);
                document.getElementById("video_source").files =
                    dataTransfer.files;
                text.innerHTML = `${lastUploadedFileName}
                    <span class="ms-2" title="Remove" id="remove-video">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                            <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                        </svg>
                    </span>`;
            } else {
                $("#video_error").text("");
                $(".video_uplod").css("border-color", "#ced4da");
                text.innerHTML = `Upload Video*
                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>`;
            }
        }
    }
});

let loading = false;
let activeType = "step1";
let activeTab = 1;
let requiredFieldArray = [
    "step-3:certification",
    "step-3:education",
    "step-3:teaching-experience",
    "step-3:other-experience",
    "step-3:resume",
    "step-4:grade-levels",
    "step-4:subject",
    "step-4:proficiency",
    "step-4:format",
    "step-4:compensation",
    "step-4:curriculum",
    "step-4:program-type",
    "step-4:languages",
    "step-5:profile",
    "step-6:assessment",
];

function profile_tags() {
    const profileTagsInput = $("#profile_tags");
    const tagsContainer = $(".tags-container");
    const tagsHiddenInput = $("#tags_hidden");
    const tagsError = $(".tags_err");
    let tags = [];

    if (tagsHiddenInput.val()) {
        tags = tagsHiddenInput
            .val()
            .split(",")
            .map((tag) => tag.trim());
        renderTags();
    }

    function processTags(inputValue) {
        // Split input by commas, trim each tag, and filter out duplicates and empty values
        let newTags = inputValue
            .split(",")
            .map((tag) => tag.trim())
            .filter((tag) => tag && !tags.includes(tag));

        if (newTags.length > 0) {
            if (tags.length + newTags.length <= 7) {
                tags.push(...newTags); // Add valid new tags
                renderTags();
            } else {
                profileTagsInput.css("border-color", "red");
                tagsError.text("You can only add up to 7 tags");
            }
        }
    }

    profileTagsInput.on("input", function () {
        let inputValue = profileTagsInput.val().trim();

        if (inputValue.includes(",")) {
            processTags(inputValue);
            profileTagsInput.val(""); // Clear the input after processing
        }
    });

    profileTagsInput.on("keydown", function (e) {
        if (e.key === "Enter") {
            e.preventDefault();
            let inputValue = profileTagsInput.val().trim();

            if (inputValue) {
                processTags(inputValue);
                profileTagsInput.val(""); // Clear the input after processing
            }
        }
    });

    profileTagsInput.on("blur", function () {
        let inputValue = profileTagsInput.val().trim();

        if (inputValue) {
            processTags(inputValue);
            profileTagsInput.val(""); // Clear the input after processing
        }
    });

    function renderTags() {
        tagsContainer.empty();

        $.each(tags, function (index, tag) {
            let tagElement = $(`

        <p class="tags-field m-1 py-2" style="border: none; border-radius: 20px; display: inline-flex; align-items: center; padding: 0.5rem 1rem; background-color: #D5EAFF; border: 1px solid #ddd; white-space: nowrap;">

                    ${tag}
                    <button type="button" class="btn btn-sm btn-danger ms-2 remove-tag" style="background: transparent; padding: 0px;" data-index="${index}">
                    <svg width="25px" height="25px" viewBox="0 -0.5 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="SVGRepo_bgCarrier" stroke-width="0"/>
<g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
<g id="SVGRepo_iconCarrier"> <path d="M6.96967 16.4697C6.67678 16.7626 6.67678 17.2374 6.96967 17.5303C7.26256 17.8232 7.73744 17.8232 8.03033 17.5303L6.96967 16.4697ZM13.0303 12.5303C13.3232 12.2374 13.3232 11.7626 13.0303 11.4697C12.7374 11.1768 12.2626 11.1768 11.9697 11.4697L13.0303 12.5303ZM11.9697 11.4697C11.6768 11.7626 11.6768 12.2374 11.9697 12.5303C12.2626 12.8232 12.7374 12.8232 13.0303 12.5303L11.9697 11.4697ZM18.0303 7.53033C18.3232 7.23744 18.3232 6.76256 18.0303 6.46967C17.7374 6.17678 17.2626 6.17678 16.9697 6.46967L18.0303 7.53033ZM13.0303 11.4697C12.7374 11.1768 12.2626 11.1768 11.9697 11.4697C11.6768 11.7626 11.6768 12.2374 11.9697 12.5303L13.0303 11.4697ZM16.9697 17.5303C17.2626 17.8232 17.7374 17.8232 18.0303 17.5303C18.3232 17.2374 18.3232 16.7626 18.0303 16.4697L16.9697 17.5303ZM11.9697 12.5303C12.2626 12.8232 12.7374 12.8232 13.0303 12.5303C13.3232 12.2374 13.3232 11.7626 13.0303 11.4697L11.9697 12.5303ZM8.03033 6.46967C7.73744 6.17678 7.26256 6.17678 6.96967 6.46967C6.67678 6.76256 6.67678 7.23744 6.96967 7.53033L8.03033 6.46967ZM8.03033 17.5303L13.0303 12.5303L11.9697 11.4697L6.96967 16.4697L8.03033 17.5303ZM13.0303 12.5303L18.0303 7.53033L16.9697 6.46967L11.9697 11.4697L13.0303 12.5303ZM11.9697 12.5303L16.9697 17.5303L18.0303 16.4697L13.0303 11.4697L11.9697 12.5303ZM13.0303 11.4697L8.03033 6.46967L6.96967 7.53033L11.9697 12.5303L13.0303 11.4697Z" fill="#eb0000"/> </g>
</svg>
                    </button>
                </p>

            `);

            tagsContainer.append(tagElement);
        });

        // Update the hidden input with the current tags
        tagsHiddenInput.val(tags.join(","));
    }

    tagsContainer.on("click", ".remove-tag", function () {
        let index = $(this).data("index");
        tags.splice(index, 1);
        if (tags.length < 7) {
            profileTagsInput.css("border-color", "#ced4da");
            tagsError.text("");
        }
        renderTags();
    });
}

function additional_tools() {
    const additionalToolsInput = $("#additional_tools");
    const toolsContainer = $(".tools-container");
    const toolsHiddenInput = $("#tools_hidden");
    const toolsError = $(".tools_err");
    let tools = [];

    if (toolsHiddenInput.val()) {
        tools = toolsHiddenInput
            .val()
            .split(",")
            .map((tool) => tool.trim());
        renderTools();
    }

    function processTools(inputValue) {
        // Split input by commas, trim each tool, and filter out duplicates and empty values
        let newTools = inputValue
            .split(",")
            .map((tool) => tool.trim())
            .filter((tool) => tool && !tools.includes(tool));

        if (newTools.length > 0) {
            if (tools.length + newTools.length <= 7) {
                tools.push(...newTools); // Add valid new tools
                renderTools();
            } else {
                additionalToolsInput.css("border-color", "red");
                toolsError.text("You can only add up to 7 tools");
            }
        }
    }

    additionalToolsInput.on("input", function () {
        let inputValue = additionalToolsInput.val().trim();

        if (inputValue.includes(",")) {
            processTools(inputValue);
            additionalToolsInput.val(""); // Clear the input after processing
        }
    });

    additionalToolsInput.on("keydown", function (e) {
        if (e.key === "Enter") {
            e.preventDefault();
            let inputValue = additionalToolsInput.val().trim();

            if (inputValue) {
                processTools(inputValue);
                additionalToolsInput.val(""); // Clear the input after processing
            }
        }
    });

    additionalToolsInput.on("blur", function () {
        let inputValue = additionalToolsInput.val().trim();

        if (inputValue) {
            processTools(inputValue);
            additionalToolsInput.val(""); // Clear the input after processing
        }
    });

    function renderTools() {
        toolsContainer.empty();

        $.each(tools, function (index, tool) {
            let tagElement = $(`

        <p class="tools-field m-1 py-2 text-dark" style="border: none; border-radius: 20px; display: inline-flex; align-items: center; padding: 0.5rem 1rem; background-color: #F6F6F6; border: 1px solid #F6F6F6; white-space: nowrap; font-weight: 500;">

                    ${tool}
                    <button type="button" class="btn btn-sm btn-danger ms-2 remove-tool" style="background: transparent; padding: 0px;" data-index="${index}">
                    <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.00371 5.06081L6.65521 7.71231C6.79591 7.85301 6.98673 7.93205 7.18571 7.93205C7.38468 7.93205 7.57551 7.85301 7.71621 7.71231C7.85691 7.57161 7.93595 7.38079 7.93595 7.18181C7.93595 6.98283 7.85691 6.79201 7.71621 6.65131L5.06371 3.99981L7.71571 1.34831C7.78534 1.27864 7.84057 1.19595 7.87824 1.10493C7.91592 1.01392 7.9353 0.916384 7.93527 0.817884C7.93525 0.719384 7.91582 0.621854 7.87811 0.530861C7.84039 0.439868 7.78512 0.357194 7.71546 0.287561C7.64579 0.217928 7.56309 0.162698 7.47208 0.125025C7.38107 0.0873522 7.28353 0.0679742 7.18503 0.0679974C7.08653 0.0680207 6.989 0.0874445 6.89801 0.12516C6.80701 0.162876 6.72434 0.218145 6.65471 0.287811L4.00371 2.93931L1.35221 0.287811C1.28306 0.216146 1.20033 0.158972 1.10884 0.119622C1.01736 0.0802729 0.918952 0.0595372 0.819368 0.0586252C0.719784 0.0577132 0.621016 0.076643 0.528827 0.11431C0.436637 0.151977 0.352872 0.207628 0.28242 0.278014C0.211968 0.3484 0.156238 0.432112 0.118484 0.524266C0.0807302 0.61642 0.0617072 0.715171 0.0625253 0.814755C0.0633434 0.914339 0.0839861 1.01276 0.123249 1.10429C0.162512 1.19581 0.219609 1.27859 0.291208 1.34781L2.94371 3.99981L0.291708 6.65181C0.220109 6.72103 0.163012 6.80382 0.123749 6.89534C0.0844863 6.98686 0.0638434 7.08528 0.0630253 7.18487C0.0622072 7.28445 0.0812301 7.3832 0.118984 7.47536C0.156738 7.56751 0.212467 7.65122 0.28292 7.72161C0.353372 7.79199 0.437137 7.84764 0.529327 7.88531C0.621516 7.92298 0.720284 7.94191 0.819868 7.941C0.919452 7.94008 1.01786 7.91935 1.10934 7.88C1.20083 7.84065 1.28356 7.78347 1.35271 7.71181L4.00371 5.06081Z" fill="black"></path>
                    </svg>
                    </button>
                </p>

            `);

            toolsContainer.append(tagElement);
        });

        // Update the hidden input with the current tags
        toolsHiddenInput.val(tools.join(","));
    }

    toolsContainer
        .off("click", ".remove-tool")
        .on("click", ".remove-tool", function () {
            let index = $(this).data("index");
            tools.splice(index, 1);
            if (tools.length < 7) {
                additionalToolsInput.css("border-color", "#ced4da");
                toolsError.text("");
            }
            renderTools();
        });
}

function initializeOnboardingBootstrapDatepicker(selectedTab) {
    $(selectedTab)
        .find(".start_date")
        .datepicker({
            format: "mm-dd-yyyy",
            autoclose: true,
            endDate: new Date(),
        })
        .on("changeDate", function (e) {
            let selectedStartDate = $(this).datepicker("getDate");
            let minEndDate = new Date(selectedStartDate);
            minEndDate.setDate(minEndDate.getDate() + 1);
            $(selectedTab)
                .find(".end_date")
                .datepicker("setStartDate", minEndDate);
        })
        .on("change", function () {
            if (!$(this).val()) {
                $(selectedTab)
                    .find(".end_date")
                    .datepicker("setStartDate", null);
            }
        })
        .on("keypress paste", function (e) {
            e.preventDefault();
            return false;
        });

    $(selectedTab)
        .find(".end_date")
        .datepicker({
            format: "mm-dd-yyyy",
            autoclose: true,
            enableOnReadonly: false,
            endDate: new Date(),
        })
        .on("changeDate", function (e) {
            let selectedEndDate = $(this).datepicker("getDate");
            let maxStartDate = new Date(selectedEndDate);
            maxStartDate.setDate(maxStartDate.getDate() - 1);
            $(selectedTab)
                .find(".start_date")
                .datepicker("setEndDate", maxStartDate);
        })
        .on("change", function () {
            if (!$(this).val()) {
                $(selectedTab)
                    .find(".start_date")
                    .datepicker("setEndDate", null);
            }
        })
        .on("keypress paste", function (e) {
            e.preventDefault();
            return false;
        });

    // Initialize issue_date datepicker
    $(selectedTab)
        .find(".issue_date")
        .datepicker({
            format: "mm-dd-yyyy",
            autoclose: true,
            endDate: new Date(), // Prevent selecting future dates
        })
        .on("changeDate", function () {
            const issueDate = $(this).datepicker("getDate");

            if (issueDate) {
                const minValidDate = new Date(issueDate);
                minValidDate.setDate(minValidDate.getDate() + 1);

                // Set validTill_date min date based on issue_date
                const $validTill = $(selectedTab).find(".validTill_date");
                $validTill.datepicker("setStartDate", minValidDate);
                $validTill.datepicker("setEndDate", null); // Allow open-ended future
            }
        })
        .on("change", function () {
            // If issue_date is cleared
            if (!$(this).val()) {
                const $validTill = $(selectedTab).find(".validTill_date");
                $validTill.datepicker("setStartDate", null);
                $validTill.datepicker("setEndDate", null);
            }
        })
        .on("keypress paste", function (e) {
            // Prevent manual typing or paste
            e.preventDefault();
            return false;
        });

    // Initialize validTill_date datepicker
    $(selectedTab)
        .find(".validTill_date")
        .datepicker({
            format: "mm-dd-yyyy",
            autoclose: true,
        })
        .on("changeDate", function () {
            const validTillDate = $(this).datepicker("getDate");

            if (validTillDate) {
                const maxIssueDate = new Date(validTillDate);
                maxIssueDate.setDate(maxIssueDate.getDate() - 1);

                // Set max selectable date for issue_date
                const $issueDate = $(selectedTab).find(".issue_date");
                $issueDate.datepicker("setEndDate", maxIssueDate);
            }
        })
        .on("change", function () {
            // If validTill_date is cleared
            if (!$(this).val()) {
                const $issueDate = $(selectedTab).find(".issue_date");
                $issueDate.datepicker("setEndDate", new Date());
            }
        })
        .on("show", function () {
            // Do not forcefully change the calendar min date here to avoid blocking navigation
            // Let changeDate logic handle restrictions dynamically
        })
        .on("keypress paste", function (e) {
            e.preventDefault(); // Prevent typing or pasting
            return false;
        });

    $(selectedTab)
        .find(".other_start_date")
        .datepicker({
            format: "mm-dd-yyyy",
            autoclose: true,
            endDate: new Date(),
        })
        .on("changeDate", function (e) {
            let selectedStartDate = $(this).datepicker("getDate");
            let minEndDate = new Date(selectedStartDate);
            minEndDate.setDate(minEndDate.getDate() + 1);
            $(selectedTab)
                .find(".other_end_date")
                .datepicker("setStartDate", minEndDate);
        })
        .on("change", function () {
            if (!$(this).val()) {
                $(selectedTab)
                    .find(".other_end_date")
                    .datepicker("setStartDate", null);
            }
        })
        .on("keypress paste", function (e) {
            e.preventDefault();
            return false;
        });

    $(selectedTab)
        .find(".other_end_date")
        .datepicker({
            format: "mm-dd-yyyy",
            autoclose: true,
            enableOnReadonly: false,
            endDate: new Date(),
        })
        .on("changeDate", function (e) {
            let selectedEndDate = $(this).datepicker("getDate");
            let maxStartDate = new Date(selectedEndDate);
            maxStartDate.setDate(maxStartDate.getDate() - 1);
            $(selectedTab)
                .find(".other_start_date")
                .datepicker("setEndDate", maxStartDate);
        })
        .on("change", function () {
            if (!$(this).val()) {
                $(selectedTab)
                    .find(".other_start_date")
                    .datepicker("setEndDate", null);
            }
        })
        .on("keypress paste", function (e) {
            e.preventDefault();
            return false;
        });
}

$("body").on("click", ".end-date svg", function () {
    console.log($(this).closest(".login__form").find("input"));
    $(this).closest(".login__form").find("end-date")[0].focus();
});

function initilizeOnboardingSelect2(selectedTab) {
    // $(selectedTab).find('.teaching_certification_states').select2({
    //     theme: 'bootstrap4',
    //     width: 'style',
    //     placeholder: 'Valid in (states)*',
    //     allowClear: Boolean($(this).data('allow-clear')),
    // });

    $(".teaching_certification_states").each(function () {
        $(this).select2({
            theme: "bootstrap4",
            width: "style",
            placeholder: "valid in (states)*",
            allowClear: Boolean($(this).data("allow-clear")),
        });
    });

    $(".experience_teaching_ages").each(function () {
        $(this).select2({
            theme: "bootstrap4",
            width: "style",
            placeholder: "Experience working with ages",
            allowClear: Boolean($(this).data("allow-clear")),
        });
    });

    $(".main_experience_teaching_ages").each(function () {
        $(this).select2({
            theme: "bootstrap4",
            width: "style",
            placeholder: "Experience working with ages",
            allowClear: Boolean($(this).data("allow-clear")),
        });
    });
    // Inject the CSS rule with !important once (outside the loop)
    $("<style>")
        .prop("type", "text/css")
        .html(
            ".select2-container .select2-selection { height: 55px !important; }"
        )
        .appendTo("head");

    // Initialize select2 on each element and apply padding & border-radius
    //   $('.credentialing_agency').each(function () {
    //     $(this).select2({
    //         theme: 'bootstrap4',
    //         width: 'style',
    //         placeholder:'Select certificate/license type',
    //         allowClear: Boolean($(this).data('allow-clear')),
    //     });

    //     // Apply padding and border-radius (height is handled by CSS above)
    //     $(this).next('.select2-container').find('.select2-selection').css({
    //         'padding': '8px 12px',
    //         'border-radius': '46px',
    //         'line-height': '40px'  // vertically center the text
    //     });
    // });

    // $('.credentialing_agency').each(function () {
    //     $(this).select2({
    //         theme: 'bootstrap4',
    //         width: 'style',
    //         placeholder: 'Credentialing Agency*',
    //         allowClear: Boolean($(this).data('allow-clear')),
    //     });

    //     // Style the selection box
    //     $(this).next('.select2-container').find('.select2-selection').css({
    //         'padding': '8px 12px',
    //         'border-radius': '46px',
    //         'line-height': '40px'
    //     });

    //     // Inject SVG icon into the search box when dropdown opens
    //     $(this).on('select2:open', function () {
    //         // Delay to ensure DOM is ready
    //         setTimeout(() => {
    //             const $searchField = $('.select2-container--open .select2-search__field');

    //             // Prevent multiple inserts
    //             if ($searchField.parent().find('.search-icon').length === 0) {
    //                 $searchField.css('padding-left', '30px');

    //                 $searchField.before(`
    //                    <div class="search-icon" style="position:absolute;left:14px;top:50%;transform:translateY(-50%);pointer-events:none;">
    //                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    //                   <path d="M6.28734 12.5747C4.53011 12.5747 3.04307 11.9659 1.82623 10.7484C0.609389 9.53096 0.000645367 8.04393 5.1179e-07 6.28734C-0.000644344 4.53075 0.608099 3.04372 1.82623 1.82623C3.04436 0.608743 4.5314 0 6.28734 0C8.04328 0 9.53064 0.608743 10.7494 1.82623C11.9682 3.04372 12.5766 4.53075 12.5747 6.28734C12.5747 6.99668 12.4618 7.66572 12.2361 8.29445C12.0104 8.92319 11.7041 9.47937 11.3172 9.96302L16.734 15.3798C16.9113 15.5571 17 15.7828 17 16.0569C17 16.331 16.9113 16.5567 16.734 16.734C16.5567 16.9113 16.331 17 16.0569 17C15.7828 17 15.5571 16.9113 15.3798 16.734L9.96302 11.3172C9.47937 11.7041 8.92319 12.0104 8.29445 12.2361C7.66572 12.4618 6.99668 12.5747 6.28734 12.5747ZM6.28734 10.6401C7.49644 10.6401 8.52434 10.2171 9.37104 9.37104C10.2177 8.52499 10.6408 7.49709 10.6401 6.28734C10.6395 5.07759 10.2164 4.05001 9.37104 3.20461C8.52563 2.3592 7.49773 1.93586 6.28734 1.93457C5.07695 1.93328 4.04937 2.35662 3.20461 3.20461C2.35985 4.05259 1.9365 5.08017 1.93457 6.28734C1.93263 7.49451 2.35598 8.52241 3.20461 9.37104C4.05324 10.2197 5.08082 10.6427 6.28734 10.6401Z" fill="#888"/>
    //                   </svg>
    //                   </div>

    //                 `);

    //                 $searchField.parent().css('position', 'relative');
    //             }
    //         }, 0);
    //     });
    // });

    $(".credentialing_agency").each(function () {
        $(this).select2({
            theme: "bootstrap4",
            width: "style",
            placeholder: "Credentialing Agency*",
            allowClear: Boolean($(this).data("allow-clear")),
        });

        // Style the selection box
        $(this).next(".select2-container").find(".select2-selection").css({
            padding: "8px 12px",
            "border-radius": "46px",
            "line-height": "40px",
        });

        // Inject SVG icon and add search placeholder
        $(this).on("select2:open", function () {
            setTimeout(() => {
                const $searchField = $(
                    ".select2-container--open .select2-search__field"
                );

                // ✅ Add search placeholder
                $searchField.attr("placeholder", "Search");

                // Prevent multiple inserts
                if ($searchField.parent().find(".search-icon").length === 0) {
                    $searchField.css("padding-left", "30px");

                    $searchField.before(`
                   <div class="search-icon" style="position:absolute;left:14px;top:50%;transform:translateY(-50%);pointer-events:none;">
                   <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6.28734 12.5747C4.53011 12.5747 3.04307 11.9659 1.82623 10.7484C0.609389 9.53096 0.000645367 8.04393 5.1179e-07 6.28734C-0.000644344 4.53075 0.608099 3.04372 1.82623 1.82623C3.04436 0.608743 4.5314 0 6.28734 0C8.04328 0 9.53064 0.608743 10.7494 1.82623C11.9682 3.04372 12.5766 4.53075 12.5747 6.28734C12.5747 6.99668 12.4618 7.66572 12.2361 8.29445C12.0104 8.92319 11.7041 9.47937 11.3172 9.96302L16.734 15.3798C16.9113 15.5571 17 15.7828 17 16.0569C17 16.331 16.9113 16.5567 16.734 16.734C16.5567 16.9113 16.331 17 16.0569 17C15.7828 17 15.5571 16.9113 15.3798 16.734L9.96302 11.3172C9.47937 11.7041 8.92319 12.0104 8.29445 12.2361C7.66572 12.4618 6.99668 12.5747 6.28734 12.5747ZM6.28734 10.6401C7.49644 10.6401 8.52434 10.2171 9.37104 9.37104C10.2177 8.52499 10.6408 7.49709 10.6401 6.28734C10.6395 5.07759 10.2164 4.05001 9.37104 3.20461C8.52563 2.3592 7.49773 1.93586 6.28734 1.93457C5.07695 1.93328 4.04937 2.35662 3.20461 3.20461C2.35985 4.05259 1.9365 5.08017 1.93457 6.28734C1.93263 7.49451 2.35598 8.52241 3.20461 9.37104C4.05324 10.2197 5.08082 10.6427 6.28734 10.6401Z" fill="#888"/>
                   </svg>
                   </div>
                `);

                    $searchField.parent().css("position", "relative");
                }
            }, 0);
        });
    });

    //  $('.certified_special_education').each(function () {

    //     $(this).select2({
    //         theme: 'bootstrap4',
    //         width: 'style',
    //         placeholder: ' Select certificate/license',
    //         allowClear: Boolean($(this).data('allow-clear')),
    //     });

    //     const $select2Container = $(this).next('.select2-container');

    //     // Style the main select2 box
    //     $select2Container.find('.select2-selection').css({
    //         'padding': '8px 12px',
    //         'border-radius': '46px',
    //         'line-height': '40px',
    //         'overflow': 'hidden',
    //         'white-space': 'nowrap',
    //         'text-overflow': 'ellipsis',
    //         'box-sizing': 'border-box'
    //     });

    //     // Only style the rendered text of this specific select
    //     const $rendered = $select2Container.find('.select2-selection__rendered')[0];
    //     if ($rendered) {
    //         $rendered.setAttribute(
    //             "style",
    //             "overflow: hidden !important; white-space: nowrap !important; text-overflow: ellipsis !important;"
    //         );
    //     }
    // });

    // $(selectedTab).find('.certified_special_education').select2({
    //     theme: 'bootstrap4',
    //     width: 'style',
    //     placeholder: 'Please specify*',
    //     allowClear: Boolean($(this).data('allow-clear')),
    // });

    // $(selectedTab).find('.experience_teaching_ages').select2({
    //     theme: 'bootstrap4',
    //     width: 'style',
    //     placeholder: 'Experience working with ages',
    //     allowClear: Boolean($(this).data('allow-clear')),
    // });
    $(".certified_special_education").each(function () {
        const $select = $(this);

        $select.select2({
            theme: "bootstrap4",
            width: "style",
            placeholder: "Certificate/License*",
            allowClear: Boolean($select.data("allow-clear")),
        });

        const $select2Container = $select.next(".select2-container");

        // Style the main select2 box
        $select2Container.find(".select2-selection").css({
            padding: "8px 12px",
            "border-radius": "46px",
            "line-height": "40px",
            overflow: "hidden",
            "white-space": "nowrap",
            "text-overflow": "ellipsis",
            "box-sizing": "border-box",
        });

        const $rendered = $select2Container.find(
            ".select2-selection__rendered"
        )[0];
        if ($rendered) {
            $rendered.setAttribute(
                "style",
                "overflow: hidden !important; white-space: nowrap !important; text-overflow: ellipsis !important;"
            );
        }

        // Add search icon + placeholder when dropdown is opened
        $select.on("select2:open", function () {
            setTimeout(function () {
                const $searchWrapper = $(
                    ".select2-container--open .select2-search"
                );

                if ($searchWrapper.find(".search-icon").length === 0) {
                    const $searchInput = $searchWrapper.find(
                        ".select2-search__field"
                    );

                    // Set parent position to relative
                    $searchWrapper.css("position", "relative");

                    // Add search icon before input
                    $searchWrapper.prepend(`
                    <div class="search-icon" style="position:absolute;left:14px;top:50%;transform:translateY(-50%);pointer-events:none;">
                        <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M6.28734 12.5747C4.53011 12.5747 3.04307 11.9659 1.82623 10.7484C0.609389 9.53096 0.000645367 8.04393 5.1179e-07 6.28734C-0.000644344 4.53075 0.608099 3.04372 1.82623 1.82623C3.04436 0.608743 4.5314 0 6.28734 0C8.04328 0 9.53064 0.608743 10.7494 1.82623C11.9682 3.04372 12.5766 4.53075 12.5747 6.28734C12.5747 6.99668 12.4618 7.66572 12.2361 8.29445C12.0104 8.92319 11.7041 9.47937 11.3172 9.96302L16.734 15.3798C16.9113 15.5571 17 15.7828 17 16.0569C17 16.331 16.9113 16.5567 16.734 16.734C16.5567 16.9113 16.331 17 16.0569 17C15.7828 17 15.5571 16.9113 15.3798 16.734L9.96302 11.3172C9.47937 11.7041 8.92319 12.0104 8.29445 12.2361C7.66572 12.4618 6.99668 12.5747 6.28734 12.5747ZM6.28734 10.6401C7.49644 10.6401 8.52434 10.2171 9.37104 9.37104C10.2177 8.52499 10.6408 7.49709 10.6401 6.28734C10.6395 5.07759 10.2164 4.05001 9.37104 3.20461C8.52563 2.3592 7.49773 1.93586 6.28734 1.93457C5.07695 1.93328 4.04937 2.35662 3.20461 3.20461C2.35985 4.05259 1.9365 5.08017 1.93457 6.28734C1.93263 7.49451 2.35598 8.52241 3.20461 9.37104C4.05324 10.2197 5.08082 10.6427 6.28734 10.6401Z" fill="#888"/>
                        </svg>
                    </div>
                `);

                    // Adjust input
                    $searchInput
                        .attr("placeholder", "Search") // ← Add the "Search" placeholder
                        .css("padding-left", "30px");
                }
            }, 10);
        });
    });

    $(selectedTab)
        .find("#i_prefer_to_teach")
        .select2({
            theme: "bootstrap4",
            width: "style",
            placeholder: "I prefer to teach",
            allowClear: Boolean($(this).data("allow-clear")),
        });

    $(selectedTab)
        .find("#language_teach_that_i_teach")
        .select2({
            theme: "bootstrap4",
            width: "style",
            placeholder: "Language(s) that I teach in",
            allowClear: Boolean($(this).data("allow-clear")),
        });

    // $(selectedTab).find('.selectRound').select2({
    //     theme: 'bootstrap4',
    //     width: 'style',
    //     placeholder: 'Select Subject',
    //     allowClear: Boolean($(this).data('allow-clear')),
    // });
    $(selectedTab)
        .find(".selectRound")
        .select2({
            theme: "bootstrap4",
            width: "style",
            placeholder: "Select Subject",
            allowClear: Boolean(
                $(selectedTab).find(".selectRound").data("allow-clear")
            ),
        })
        .on("select2:open", function () {
            setTimeout(function () {
                const $searchInput = $(
                    ".select2-container--open .select2-search__field"
                );

                if ($searchInput.parent().find(".search-icon").length === 0) {
                    $searchInput.css({
                        "padding-left": "30px",
                        position: "relative",
                    });

                    $searchInput.before(`
               <div class="search-icon" style="position:absolute;left:14px;top:50%;transform:translateY(-50%);pointer-events:none;z-index:3">
                        <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M6.28734 12.5747C4.53011 12.5747 3.04307 11.9659 1.82623 10.7484C0.609389 9.53096 0.000645367 8.04393 5.1179e-07 6.28734C-0.000644344 4.53075 0.608099 3.04372 1.82623 1.82623C3.04436 0.608743 4.5314 0 6.28734 0C8.04328 0 9.53064 0.608743 10.7494 1.82623C11.9682 3.04372 12.5766 4.53075 12.5747 6.28734C12.5747 6.99668 12.4618 7.66572 12.2361 8.29445C12.0104 8.92319 11.7041 9.47937 11.3172 9.96302L16.734 15.3798C16.9113 15.5571 17 15.7828 17 16.0569C17 16.331 16.9113 16.5567 16.734 16.734C16.5567 16.9113 16.331 17 16.0569 17C15.7828 17 15.5571 16.9113 15.3798 16.734L9.96302 11.3172C9.47937 11.7041 8.92319 12.0104 8.29445 12.2361C7.66572 12.4618 6.99668 12.5747 6.28734 12.5747ZM6.28734 10.6401C7.49644 10.6401 8.52434 10.2171 9.37104 9.37104C10.2177 8.52499 10.6408 7.49709 10.6401 6.28734C10.6395 5.07759 10.2164 4.05001 9.37104 3.20461C8.52563 2.3592 7.49773 1.93586 6.28734 1.93457C5.07695 1.93328 4.04937 2.35662 3.20461 3.20461C2.35985 4.05259 1.9365 5.08017 1.93457 6.28734C1.93263 7.49451 2.35598 8.52241 3.20461 9.37104C4.05324 10.2197 5.08082 10.6427 6.28734 10.6401Z" fill="#888"/>
                        </svg>
                    </div>
            `);

                    $searchInput.parent().css("position", "relative");
                }
            }, 10);
        });

    $(selectedTab)
        .find("#program_type")
        .select2({
            theme: "bootstrap4",
            width: "style",
            placeholder: "I prefer to teach",
            allowClear: Boolean($(this).data("allow-clear")),
        });
}

function initOnboardingAutocompletemap() {
    // Create the search box and link it to the UI element.
    const input = document.getElementById("location");
    const autocomplete = new google.maps.places.Autocomplete(input, {
        types: ["(cities)"],
    });
    if (input) {
        input.classList.add("gmap_configured");
        input.addEventListener("blur", () => {
            if (!input.placeSelected) {
                $('input[id="location"]').val("");
                $('input[name="state"]').val("");
            }
            input.placeSelected = false;
        });

        input.addEventListener("input", () => {
            input.placeSelected = false;
        });
    }
    autocomplete.addListener("place_changed", () => {
        const place = autocomplete.getPlace();
        let city = "";
        let state = "";
        let country = "";
        place.address_components.forEach((component) => {
            input.placeSelected = true;
            const types = component.types;
            if (types.includes("locality")) city = component.long_name;
            if (types.includes("administrative_area_level_1"))
                state = component.long_name;
            if (types.includes("country")) country = component.long_name;
        });
        $('input[name="city"]').val(city);
        if (state) {
            document.getElementById("state").value = state;
        }
    });
}

function handleTabSwitch(button, url) {
    const replaceHtmlId = button.dataset.bsTarget;
    console.log(replaceHtmlId);

    let selectedTab = $(replaceHtmlId);

    //this is if the rhere is no leme
    if (!selectedTab.length) {
        $.ajax({
            url: url,
            method: "GET",
            data: {
                type: activeType,
            },
            dataType: "json",
            success: function (res) {
                if (res.status === true) {
                    const selectedTab = document.createElement("div");
                    selectedTab.id = replaceHtmlId.replace("#", "");

                    selectedTab.innerHTML =
                        typeof res.view == "string" ? res.view : res.view?.view;

                    if (res.view.metaData) {
                        if (Array.isArray(res.view.metaData)) {
                            window["metaData"] = res.view.metaData;
                        } else
                            Object.entries(res.view.metaData).forEach(
                                ([key, data]) => (window[key] = data)
                            );
                    }

                    selectedTab.role = "tabpanel";
                    selectedTab.classList.add("tab-pane");
                    selectedTab.classList.add("fade");
                    document
                        .getElementById("data-container")
                        .appendChild(selectedTab);
                    initilizeOnboardingSelect2(selectedTab);
                    initializeOnboardingBootstrapDatepicker(selectedTab);
                    initOnboardingAutocompletemap();
                    initGoogleMapsCityAutocomplete();
                    profile_tags();
                    additional_tools();
                    toggleValidTillInputs();

                    updateView(selectedTab);
                    const contract = $("#contracthtml");
                    if (!contractDoc) contractDoc = contract.html();
                    if (
                        $("#legal_first_name")?.val() &&
                        $("#legal_last_name")?.val()
                    ) {
                        const legal_user = `${$("#legal_first_name").val()} ${$(
                            "#legal_last_name"
                        ).val()}`;
                        contract.html(
                            contractDoc.replace(/{{LEGAL_NAME}}/g, legal_user)
                        );
                    } else {
                        if ($("#full_name")?.val() && contractDoc) {
                            contract.html(
                                contractDoc.replace(
                                    /{{LEGAL_NAME}}/g,
                                    $("#full_name")?.val()
                                )
                            );
                        }
                    }
                    $("#agreement_text").val(contract.html());
                    const selected = $("#formatwo").is(":checked");
                    if (
                        $("#in-person-map").length &&
                        !window["inperson-map"] &&
                        selected
                    ) {
                        initMap();
                    }

                    if (replaceHtmlId == "#step2") {
                        fetchSubjectAreas();
                    } else if (replaceHtmlId == "#step4") {
                        updateAllSubjectAreaValues();
                    } else if (replaceHtmlId == "#step6") {
                        const main_div_subject_lessons = $(
                            ".main_div_subject_lesson"
                        );
                        main_div_subject_lessons.each(function (index) {
                            const subjectEl = $(this).find(".select_subject");
                            const selectedSubject = subjectEl[0].dataset.lesson;
                            subjects.forEach((subjectArea) => {
                                const optgroup = `<optgroup label="${subjectArea.title}" data-id="${subjectArea.id}" data-selected="${selectedSubject}"></optgroup>`;
                                subjectEl.append(optgroup);
                                if (
                                    subjectArea.subjects &&
                                    subjectArea.subjects.length > 0
                                ) {
                                    subjectArea.subjects.forEach(function (
                                        subject
                                    ) {
                                        subjectEl.append(
                                            `<option value="${
                                                subject.id
                                            }" data-value="${subject.id}" ${
                                                selectedSubject == subject.id
                                                    ? "selected"
                                                    : ""
                                            }>${subject.title}</option>`
                                        );
                                    });
                                }
                            });
                        });
                    }
                }
            },
        });
    } else {
        updateView(selectedTab[0]);
    }
}

function fetchSubjectAreas() {
    $.ajax({
        url: "/v1/subject-areas",
        type: "GET",
        dataType: "json",
        success: (response) => {
            subjects = response.data;
        },
        error: function (xhr, status, error) {
            console.error("Error fetching subjects:", error);
        },
    });
}

function updateAllSubjectAreaValues() {
    new Array(7).fill(0).forEach((_, index) => {
        subjects.forEach((subjectArea) => {
            $(`#subject_${index} optgroup[data-id="${subjectArea.id}"]`).each(
                function () {
                    const selected = this.dataset.selected;
                    if (
                        subjectArea.subjects &&
                        subjectArea.subjects.length > 0
                    ) {
                        let options = "";
                        subjectArea.subjects.forEach(function (subject) {
                            options += `<option value="${subject.id}" data-value="${subject.id}">${subject.title}</option>`;
                        });
                        $(this).html(options);
                        $(`#subject_${index}`).val(selected).trigger("change");
                    }
                }
            );
        });
    });
}

var subjects = {};

function updateView(selectedTab) {
    const parent = document.getElementById("data-container");

    Array.from(parent.children).forEach((element) => {
        element.classList.remove("active", "show");
        element.classList.add("d-none");
        $(`#step${activeTab - 1}-tab`).removeClass("active");
    });

    selectedTab.classList.add("active", "show");
    selectedTab.classList.remove("d-none");
    $("#skipTabButton").text("Skip");
    $("#prevTabButton").toggleClass("d-none", activeTab === 1);
    activeTab === 1 || activeTab === 2 || activeTab === 7
        ? $("#skipTabButton").addClass("d-none")
        : $("#skipTabButton").removeClass("d-none");
    activeTab === 1
        ? $("#myTabContent-btn").addClass("justify-content-end")
        : $("#myTabContent-btn").removeClass("justify-content-end");
    // activeTab === 1 ? $('.footer-txt').addClass('d-none') : $('.footer-txt').removeClass('d-none')
    activeTab === 2
        ? $(".footer-txt").addClass("d-none")
        : $(".footer-txt").removeClass("d-none");
    const steps = JSON.parse(window.pendingSteps || "[]");
    // if (activeTab == 6) {
    //     if(!steps.includes('step-6:assessment')){
    //         $('#skipTabButton').addClass('d-none');
    //     } else{
    //         $('#skipTabButton').removeClass('d-none');
    //     }
    // }
    $("#myTabContent-btn").toggleClass(
        "justify-content-between",
        activeTab >= 2
    );
    slider();
    activeTab === 2
        ? $("#nextTabButton").text("Continue")
        : $("#nextTabButton").text("Save & Continue");
    // activeTab === 6 ? $('#skipTabButton').text('Skip & Continue') : $('#skipTabButton').text('Skip');
    // setTimeout(()=> {
    //     const steps = JSON.parse(window.pendingSteps || '[]')
    //     if (activeTab == 6) {
    //         if(!steps.includes('step-6:assessment')){
    //             $('#skipTabButton').addClass('d-none');
    //         } else{
    //             $('#skipTabButton').removeClass('d-none');
    //         }
    //     }
    // }, 1000)
    // activeTab === 6 ? $('#nextTabButton').text('Submit') : (activeTab === 2 ? $('#nextTabButton').text('Continue') : $('#nextTabButton').text('Save & Continue'));

    if ($(selectedTab).find(".appendCertificate > .row").length === 0) {
        if ($(selectedTab).find("#certification_yes").is(":checked")) {
            $(selectedTab).find("#add-certification").click();
        }
    }
    if ($(".appendAcademics > .row").length === 0) {
        $("#add-academics").click();
    }
    if ($(selectedTab).find("#mySixTabContent > .tab-pane.active").length > 0) {
        var currentTab = document.querySelector(
            "#mySixTabContent .tab-pane.active"
        );
        const quizTab = document.getElementById("quiz_btn");
        if (currentTab.id === "quiz") {
            window.scrollTo({ top: 0, behavior: "smooth" });
            $("#skipTabButton").addClass("d-none");
            $("#nextTabButton").text("Submit");
        } else {
            $("#skipTabButton").text("Skip");
            $("#nextTabButton").text("Save & Continue");
        }
    }
}

function continueToTab(type, _this, isNext = true) {
    if (isNext) {
        if (activeTab <= 6) {
            const switchTabs = () => {
                activeTab = Math.min(activeTab + 1, 7);
                activeType = `step${activeTab}`;
                $(`#step${activeTab}-tab`).addClass("active");
                $(`#step${activeTab - 1}-tab`).removeClass("active");
                handleTabSwitch(
                    document.getElementById(`step${activeTab}-tab`),
                    $("#pageUrl").val()
                );
            };
            switch (activeType) {
                case "step1":
                    if (!newOnboardingSaveFirstStep(type, _this, switchTabs)) {
                        return; // Stop the process if validation fails
                    }
                    break;
                case "step2":
                    switchTabs();
                    window.scrollTo({ top: 0, behavior: "smooth" });
                    $("#nextTabButton").text("Save & Continue");
                    break;
                case "step3":
                    if (!newOnboardingSaveSecondStep(type, _this, switchTabs)) {
                        return; // Stop the process if validation fails
                    }
                    break;
                case "step4":
                    if (!newOnboardingSaveThiredStep(type, _this, switchTabs)) {
                        return; // Stop the process if validation fails
                    }
                    break;
                case "step5":
                    if (!newOnboardingSaveFifthStep(type, _this, switchTabs)) {
                        return; // Stop the process if validation fails
                    }
                    break;
                case "step6":
                    if (!newOnboardingSumbitQuiz(type, _this)) {
                        return; // Stop the process if validation fails
                    }
                    break;
                case "step7":
                    // if (!newOnboardingsubmitContract(type, _this)) {
                    if (!newOnboardingsubmitAgreement(type, _this)) {
                        return; // Stop the process if validation fails
                    }
                    break;
                default:
                    break;
            }
        }
    } else {
        initilizeOnboardingSelect2();

        $("<style>")
            .prop("type", "text/css")
            .html(
                ".select2-container .select2-selection { height: 55px !important; }"
            )
            .appendTo("head");

        // Initialize select2 on each element and apply padding & border-radius
        $(".credentialing_agency").each(function () {
            $(this).select2({
                theme: "bootstrap4",
                width: "style",
                placeholder: "Select certificate/license type",
                allowClear: Boolean($(this).data("allow-clear")),
            });

            // Apply padding and border-radius (height is handled by CSS above)
            $(this).next(".select2-container").find(".select2-selection").css({
                padding: "8px 12px",
                "border-radius": "46px",
                "line-height": "40px", // vertically center the text
            });
        });

        if (activeTab == 6) {
            if ($("#mySixTabContent > .tab-pane.active").length) {
                var currentTab = document.querySelector(
                    "#mySixTabContent .tab-pane.active"
                );
                if (currentTab.id !== "video_demonstration") {
                    const prev = currentTab.previousElementSibling;
                    currentTab.classList.remove("active", "show");
                    $(prev).removeClass("d-none");
                    prev.classList.add("active", "show");
                    $(`#${prev.id}_btn`).addClass("active");
                    $(`#${currentTab.id}_btn`).removeClass("active");
                    $("#skipTabButton").removeClass("d-none");
                    $("#nextTabButton").text("Save & Continue");
                    window.scrollTo({ top: 0, behavior: "smooth" });
                    setTimeout(() => {
                        const prevTabLinkEl = $("#myTab .active")
                            .closest("li")
                            .next("li")
                            .find("a")[0];
                        const prevTab = new bootstrap.Tab(prevTabLinkEl);
                        prevTab.show();
                    });
                }
            }
        }
        initilizeOnboardingSelect2();

        if (!currentTab || currentTab?.id == "video_demonstration") {
            activeTab = Math.max(activeTab - 1, 1);
            activeType = `step${activeTab}`;
            $("#nextTabButton").text(
                activeTab == 2 ? "Continue" : "Save & Continue"
            );
            handleTabSwitch(
                document.getElementById(`step${activeTab}-tab`),
                $("#pageUrl").val()
            );
        }
        window.scrollTo({ top: 0, behavior: "smooth" });
    }
    activeTab === 1
        ? $("#prevTabButton").addClass("d-none")
        : $("#prevTabButton").removeClass("d-none");
    activeTab === 1 || activeTab === 2 || (activeTab === 6 && isNext)
        ? $("#skipTabButton").addClass("d-none")
        : $("#skipTabButton").removeClass("d-none");
}

// #region New Onboarding Save Steps
function newOnboardingSavestep(type, _this) {
    if (activeTab <= 7) {
        switch (activeType) {
            case "step1":
                if (!newOnboardingSaveFirstStep(type, _this)) {
                    return; // Stop the process if validation fails
                }
                break;
            case "step2":
                break;
            case "step3":
                if (!newOnboardingSaveSecondStep(type, _this)) {
                    return; // Stop the process if validation fails
                }
                break;
            case "step4":
                if (!newOnboardingSaveThiredStep(type, _this)) {
                    return; // Stop the process if validation fails
                }
                break;
            case "step5":
                if (!newOnboardingSaveFifthStep(type, _this)) {
                    return; // Stop the process if validation fails
                }
                break;
            case "step6":
                if (!newOnboardingSumbitQuiz(type, _this)) {
                    return; // Stop the process if validation fails
                }
                break;

            case "step7":
                if (!newOnboardingsubmitAgreement(type, _this)) {
                    return; // Stop the process if validation fails
                }
                break;

            default:
                break;
        }
    }
}

let isAssessmentsFill = true;
function skipTab(_this, isNext = true) {
    if (isNext) {
        if (activeTab === 6) {
            // skipCall(_this,$('#instructorId').val(), $('#userId').val());
            var currentTab = document.querySelector(
                "#mySixTabContent .tab-pane.active"
            );
            const quizTab = document.getElementById("quiz_btn");
            if (currentTab.id === "quiz") {
                window.scrollTo({ top: 0, behavior: "smooth" });
                $("#skipTabButton").addClass("d-none");
                $("#nextTabButton").text("Submit");
            } else if (currentTab.id === "video_demonstration") {
                // let isValid=true;
                // if ($('.showRecording').hasClass('d-none')) {
                //     isValid = false;
                // }
                // if(!isValid){
                //     $(".video_demonstration_dot").empty();
                //     $(".video_demonstration_dot").append(`<svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                //             <circle cx="4" cy="4.5" r="4" fill="#CF1313"/>
                //            </svg>
                //          `);

                // }
                // else{

                //      $(".video_demonstration_dot").empty();
                // }
                if (
                    $(".showRecording").length &&
                    !$(".showRecording").hasClass("d-none")
                ) {
                    $(".video_demonstration_dot").empty();
                } else {
                    $(".video_demonstration_dot").empty();
                    $(".video_demonstration_dot")
                        .append(`<svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="4" cy="4.5" r="4" fill="#CF1313"/>
                           </svg>
                         `);
                }

                $("#sample_lesson_btn").click();
            } else if (currentTab.id == "sample_lesson") {
                let isValid = true; // tracks the entire form's validity

                document
                    .querySelectorAll(".required-field")
                    .forEach((field) => {
                        let fieldIsValid = true;

                        if (field.type === "file") {
                            // Find the closest existing input sibling or parent to check its value
                            const existingInput = field
                                .closest("label")
                                .querySelector(".existing_input");

                            const hasFileSelected = field.files.length > 0;
                            const existingValueNotEmpty =
                                existingInput &&
                                existingInput.value.trim() !== "";

                            fieldIsValid =
                                hasFileSelected || existingValueNotEmpty;

                            const label = field.closest("label");
                            if (!fieldIsValid) {
                                // label.style.border = "1px solid red";
                                isValid = false;
                            } else {
                                // label.style.border = "1px solid #ccc";
                            }
                        } else {
                            fieldIsValid = field.value.trim() !== "";
                            // field.style.border = fieldIsValid ? "1px solid #ccc" : "2px solid red";
                            if (!fieldIsValid) {
                                isValid = false;
                            }
                        }
                    });

                if (!isValid) {
                    // Avoid duplicating the SVG if already present
                    $(".sample_lesson_dot").empty();
                    $("#free_response_btn").click();
                    $(".sample_lesson_dot").append(`
        <svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="4" cy="4.5" r="4" fill="#CF1313"/>
        </svg>
        `);
                } else {
                    $(".sample_lesson_dot").empty();
                    $("#free_response_btn").click();
                }
            } else if (currentTab.id == "free_response") {
                let isValid = true;
                let missingResponse = null;
                $(".assment_que").each(function (index) {
                    if (
                        $("textarea[name='answer" + index + "']")
                            .val()
                            .trim().length === 0
                    ) {
                        // $('#answer-' + index).css('border-color', 'red');
                        isValid = false;
                        if (missingResponse == null)
                            missingResponse = "#answer-" + index;
                    } else {
                        $("#answer-" + index).css("border-color", "#ced4da");
                    }
                });

                if (isValid) {
                    const quizTab = document.getElementById("quiz_btn");
                    $(".free_response_dot ").empty();
                    quizTab.click();
                    window.scrollTo({ top: 0, behavior: "smooth" });
                } else {
                    $(".free_response_dot").empty();
                    $(".free_response_dot")
                        .append(`<svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="4" cy="4.5" r="4" fill="#CF1313"/>
                           </svg>
                         `);

                    quizTab.click();
                }

                //  $(".free_response_dot").empty();
                //      $(".free_response_dot").append(`<svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                //                 <circle cx="4" cy="4.5" r="4" fill="#CF1313"/>
                //                </svg>
                //              `);

                //     $("#sample_lesson_btn").click();

                //         $("#quiz_btn").click();
            }

            // else {
            //     quizTab.click();
            //     window.scrollTo({ top: 0, behavior: 'smooth' });
            //     $('#skipTabButton').addClass('d-none');
            //     $('#nextTabButton').text('Submit');
            // }
            let assessments = $(".ans-text");
            assessments.each(function () {
                if ($(this).val().trim() == "") {
                    isAssessmentsFill = false;
                    return false; // loop break karne ke liye (kyunki ek non-empty mil gaya)
                }
            });
            if (!isAssessmentsFill) {
                let userId = $("#instructorId").val();
                var url = APP_URL + "/k12connections/remove-assessment";

                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                            "content"
                        ),
                    },
                });

                $.ajax({
                    type: "POST",
                    url: url,
                    dataType: "json",
                    data: {
                        userId: userId,
                    },
                    success: function (data) {},
                });
            }
        }

        if (activeTab < 6) {
            activeTab = Math.min(activeTab + 1, 7);
            activeType = `step${activeTab}`;
            $(`#step${activeTab}-tab`).addClass("active");
            $(`#step${activeTab - 1}-tab`).removeClass("active");
            window.scrollTo({ top: 0, behavior: "smooth" });
            var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
            _this.innerHTML = loading;
            handleTabSwitch(
                document.getElementById(`step${activeTab}-tab`),
                $("#pageUrl").val()
            );
        }

        if (activeTab === 7) {
            $("#nextTabButton").text("Submit");
            // requiredFieldArray.forEach((element) => {
            //     let ele = element.split(':');
            //     var html = `<li style="display:block;">${ele[1]}</li>`;

            //     if (!$('#incomplete-list li').filter(function() {
            //         return $(this).text() === ele[1]; // Check if the text matches
            //     }).length) {
            //         // If not, append it to the list
            //         $('#incomplete-list').append(html);
            //     }
            // });
            // $('#AlertIncompleteProfileModal').modal('show')
        }

        if (activeTab == 6) {
            if ($("#instructor_video_url_s3").val() != "") {
                $("#progressContainer").hide();
                $("#uploadStatus").hide();
                $("#instructor_video_url_s3").val();
                $("#videoname").val();
                $(".video_uplod")
                    .html(`<input type="file" id="videoInputnew"  class="form-control videoInput myFile second" name="video" class="myFile"
                    accept="video/mp4,video/x-m4v,video/*">
                    Upload Video
                    <svg  width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>`);
            }
        }
    }
}

function skipCall(_this, instructorId, userId) {
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var url =
        APP_URL +
        "/k12connections/ajaxCheckAndUpdateProfilStatus/" +
        instructorId +
        "/" +
        userId;

    $.ajax({
        type: "POST",
        url: url,
        dataType: "json",
        beforeSend: function () {
            _this.innerHTML = loading;
            // $this.prop("disabled", true);
        },
        success: function (data) {
            _this.innerHTML = "skip and continue";
            if (data.success == true) {
                $("#AlertCompleteProfileModal").modal("show");
            } else {
                // if (data.isLater && data.isLater == 1) {
                //     window.location.href = data.redirect;
                // } else{
                let pendingSteps = data.pending;
                let pendingMap = {
                    "step-1": "Us Work Authorization",
                    "step-3": "Education & Experience",
                    "step-4": "Your Preferences",
                    "step-5": "Profile",
                    "step-6": "Assessment:Quiz",
                };
                $("#incomplete-list").empty();
                let uniqueSteps = new Set();
                pendingSteps.forEach((item) => {
                    let stepKey = item.split(":")[0];
                    if (pendingMap[stepKey] && !uniqueSteps.has(stepKey)) {
                        $("#incomplete-list").append(
                            `<li class="list_item" style="display:block; color: var(--contentColor); font-size: 16px;">
                                    ${pendingMap[stepKey]}
                                </li>`
                        );
                        uniqueSteps.add(stepKey); // Mark this step as added
                    }
                });

                $("#AlertIncompleteProfileModal").modal("show");
                // }
            }
        },
    });
}

function laterBtn(_this, instructorId, userId, isNext = true) {
    // if (isNext) {
    //     if (activeTab < 7) {
    //         activeTab = Math.min(activeTab + 1, 7);
    //         activeType = `step${activeTab}`;
    //         $(`#step${activeTab}-tab`).addClass('active');
    //         $(`#step${activeTab - 1}-tab`).removeClass('active');
    //         window.scrollTo({ top: 0, behavior: 'smooth' });
    //         var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    //         $('#AlertIncompleteProfileModal').modal('hide')
    //         $('#avtiveTab').val(6);
    //         handleTabSwitch(document.getElementById(`step${activeTab}-tab`), $('#pageUrl').val());
    //     }

    //     if (activeTab === 7) {

    //         var lasttThisBtn = $('#nextTabButton');
    //         var lasttThisBtnType = 'save_continue';
    //         var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    //         $('#completeLaterBtn').html(loading);
    //         $('#isLater').val(1);

    //         if (!newOnboardingsubmitAgreement(lasttThisBtnType, lasttThisBtn)) {
    //             return;  // Stop the process if validation fails
    //         }
    //     }
    // }
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var url =
        APP_URL +
        "/k12connections/ajaxLaterUpdateProfilStatus/" +
        instructorId +
        "/" +
        userId;

    $.ajax({
        type: "POST",
        url: url,
        dataType: "json",
        beforeSend: function () {
            _this.innerHTML = loading;
        },
        success: function (data) {
            window.location.href = data.redirect;
        },
    });
}

function submitReview(_this, instructorId, userId) {
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var url =
        APP_URL +
        "/k12connections/ajaxSubmitAndUpdateProfilStatus/" +
        instructorId +
        "/" +
        userId;

    $.ajax({
        type: "POST",
        url: url,
        dataType: "json",
        beforeSend: function () {
            _this.innerHTML = loading;
        },
        success: function (data) {
            window.location.href = data.redirect;
        },
    });
}

function continueBtn() {
    $("#AlertIncompleteProfileModal").modal("hide");
}

$("body").on("click", "#certification_no", function () {
    $("#add-certification").hide();
});

$("body").on("click", "#certification_yes", function () {
    $("#add-certification").show();
    if ($(".appendCertificate > .row").length == 0) {
        $("#add-certification").click();
    }
});

$("body").on("click", "#add-certification", function (e) {
    $("#add-certification").addClass("addMoreCertification");
    $(".addMoreCertification").html(`<span>+</span> Add More`);
});
$("body").on("click", ".addMoreCertification,#add-certification", function () {
    var id = $("#id").val();
    key++;
    var url = APP_URL + "/k12connections/get_certification/" + id;
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    $.ajax({
        url: url,
        method: "GET",
        dataType: "json",
        beforeSend: function () {
            $(".addMore").html(loading);
            $(".addMoreCertification").css("pointer-events", "none");
        },
        success: function (res) {
            if (res.status === true) {
                let newContent = $(res.view);
                const index = $(".appendCertificate > .row").length;
                if (index == 0)
                    newContent[0].innerHTML = newContent[0].innerHTML.replace(
                        '<hr style="margin-left: 1%; margin-bottom: 25px; width: 95.5%;">',
                        ""
                    );
                newContent[0].innerHTML = newContent[0].innerHTML.replace(
                    "##INDEX##",
                    index
                );
                // console.log(index)
                const id = Date.now();
                newContent.find("[id]").each(function () {
                    let newId = $(this).attr("id") + "_" + id; // Add a unique suffix
                    $(this).attr("id", newId);
                });
                $(".appendCertificate").append(newContent);
                if (index == 0) {
                    const certificateElem = $(".appendCertificate > .row")[0];
                    certificateElem.removeChild(
                        certificateElem.lastElementChild
                    );
                }
                initilizeOnboardingSelect2(newContent);
                $(".addMore").html(`<span>+</span> Add More`);
                $(".addMoreCertification").css("pointer-events", "all");
            }
        },
    });
});
$("body").on(
    "change",
    'select.credentialing_agency[name="credentialing_agency[]"]',
    function () {
        const id = this.selectedOptions[0].value;
        const _id = +this.id.replace("credentialing_agency_", "");
        const certificate$ = $("#certified_special_education_" + _id);
        const otherSection = $(this)
            .closest(".login__form")
            .next()
            .find(".credentialingAgencyOther");
        if (otherSection.hasClass("invisible")) {
            otherSection.removeClass("invisible");
        }
        if ("Other" == id) {
            otherSection.removeClass("d-none");
            certificate$.html("");
            certificate$.attr("disabled", false);
            certificate$.append(
                $("<option>", {
                    value: "Other",
                    text: "Other",
                    selected: true,
                })
            );
            certificate$
                .closest(".login__form")
                .next()
                .find(".Certificationsother")
                .removeClass("d-none");
            window["certificates"] = {};
        } else {
            certificate$
                .closest(".login__form")
                .next()
                .find(".Certificationsother")
                .addClass("d-none");
            otherSection.addClass("d-none");
            certificate$.html(
                '<option value="" hidden>Certificate/License*</option>'
            );
            if (window?.["certificates"]?.[id]) {
                console.log("already here ", window?.["certificates"]?.[id]);
                window?.["certificates"]?.[id].certificates.forEach(
                    (option) => {
                        certificate$.append(
                            $("<option>", {
                                value: option.certificate,
                                text: option.certificate,
                            })
                        );
                    }
                );
                certificate$.attr("disabled", false);
                certificate$.append(
                    $("<option>", {
                        value: "Other",
                        text: "Other",
                    })
                );
                return;
            }
            var url = APP_URL + "/k12connections/get_certificates/" + id;
            $.ajax({
                url: url,
                method: "GET",
                dataType: "json",
                beforeSend: function () {},
                success: function (res) {
                    if (res.status === true) {
                        const agency = res.data;
                        if (!window["certificates"]) {
                            window["certificates"] = {};
                        }
                        window["certificates"][id] = agency.certificates;
                        agency.certificates.forEach((option) => {
                            certificate$.append(
                                $("<option>", {
                                    value: option.certificate,
                                    text: option.certificate,
                                })
                            );
                        });
                        certificate$.attr("disabled", false);
                        certificate$.append(
                            $("<option>", {
                                value: "Other",
                                text: "Other",
                            })
                        );
                    }
                },
            });
        }
    }
);

$("body").on("change", ".highest_level_of_education1", function () {
    const selectedValue = this.selectedOptions[0].value;
    if (selectedValue == "Other Professional Degree") {
        $(this)
            .closest(".appendedcation")
            .find(".otherEducation-section")
            .removeClass("d-none");
    } else {
        $(this)
            .closest(".appendedcation")
            .find(".otherEducation-section")
            .addClass("d-none");
    }
});

$("body").on("change", "#language_teach_that_i_teach", function () {
    const selectedValues = $(this).val();
    const hasOther = selectedValues.includes("Other");

    const otherLanguageContainer = $("#other-language-container");
    if (hasOther) {
        otherLanguageContainer.removeClass("d-none");
    } else {
        otherLanguageContainer.addClass("d-none");
    }
});

$("body").on("change", "#program_type", function () {
    const selectedValues = $(this).val();
    const hasOther = selectedValues.includes("Other");

    const otherprogramContainer = $("#other-program-container");
    if (hasOther) {
        otherprogramContainer.removeClass("d-none");
    } else {
        otherprogramContainer.addClass("d-none");
    }
});

function DeleteOnboardingStep(_this, type = null) {
    const parent = $(_this).closest(".row").parent();
    $(_this).closest(".row").remove();
    if (
        parent.children().length >= 1 &&
        parent[0].children[0].children[0].nodeName == "HR"
    ) {
        parent[0].children[0].removeChild(parent[0].children[0].children[0]);
    }
    var id = $("#id").val();
    if (type !== null) {
        var url = APP_URL + "/k12connections/delete_onboarding_step/" + id;
        var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
        var formData = new FormData($("#secondstep")[0]);

        $.ajaxSetup({
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
        });
        $.ajax({
            url: url,
            method: "POST",
            data: formData,
            dataType: "json",
            processData: false,
            contentType: false,
            success: function (res) {
                console.log(res);
            },
        });
    }
}

$("body").on("click", "#add-academics", function () {
    $(".appendedcation").removeClass("d-none");
    $("#add-academics").addClass("addMoreAcademics");
    $(".addMoreAcademics").html(`<span>+</span> Add More`);
});

$("body").on("click", ".addMoreAcademics,#add-academics", function () {
    var id = $("#id").val();
    var url = APP_URL + "/k12connections/get_academics/" + id;
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";

    $.ajax({
        url: url,
        method: "GET",
        dataType: "json",
        beforeSend: function () {
            $(".addMoreAcademics").html(loading);
        },
        success: function (res) {
            if (res.status === true) {
                let newContent = $(res.view);
                const index = $(".appendAcademics")[0].children.length;
                if (index == 0)
                    newContent[0].innerHTML = newContent[0].innerHTML.replace(
                        '<hr style="margin-left: 1%; margin-bottom: 25px; width: 95.5%; margin-top: 25px;">',
                        ""
                    );
                const id = Date.now();
                newContent.find("[id]").each(function () {
                    let newId = $(this).attr("id") + "_" + id; // Add a unique suffix
                    $(this).attr("id", newId);
                });
                $(".appendAcademics").append(newContent);
                initilizeOnboardingSelect2(newContent);
                initGoogleMapsCityAutocomplete();
                $(".addMoreAcademics").html(`<span>+</span> Add More`);
            }
        },
    });
});

$("body").on("click", "#add-experience", function () {
    $(".apendExperience").first().children().first().find("hr").remove();

    // Find the <hr> inside that div and remove if it exists

    $(".experience-form-input").removeClass("d-none");
    $("#add-experience").addClass("addMoreExperience");
    $(".addMoreExperience").html(`<span>+</span> Add Experience`);
});

$("body").on("click", ".addMoreExperience,#add-experience", function () {
    // Find the <hr> inside that div and remove if it exists

    var id = $("#id").val();
    var url = APP_URL + "/k12connections/get_experience/" + id;
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    $.ajax({
        url: url,
        method: "GET",
        dataType: "json",
        beforeSend: function () {
            $(".addMoreExperience").html(loading);
        },
        success: function (res) {
            if (res.status === true) {
                let newContent = $(res.view);
                const index = $(".apendExperience > .row").length;
                if (index == 0) {
                    // newContent[0].innerHTML = newContent[0].innerHTML.replace('<hr style="margin-left: 1%; margin-bottom: 25px; width: 95.5%;">', '');
                    $(".appendExperience")
                        .first()
                        .children()
                        .first()
                        .find("hr")
                        .remove();

                    newContent.removeClass("mt-5");
                } else newContent.addClass("mt-5");
                $(".apendExperience")
                    .first()
                    .children()
                    .first()
                    .find("hr")
                    .remove();

                newContent[0].innerHTML = newContent[0].innerHTML.replace(
                    "##INDEX##",
                    index
                );
                const id = Date.now();
                newContent.find("[id]").each(function () {
                    let newId = $(this).attr("id") + "_" + id; // Add a unique suffix
                    $(this).attr("id", newId);
                });
                $(".apendExperience").append(newContent);
                if (index == 0) {
                    const experienceField = $(".apendExperience > .row")[0];
                    $(".apendExperience")
                        .first()
                        .children()
                        .first()
                        .find("hr")
                        .remove();
                    experienceField.removeChild(
                        experienceField.lastElementChild
                    );
                }
                $(".apendExperience")
                    .first()
                    .children()
                    .first()
                    .find("hr")
                    .remove();
                initilizeOnboardingSelect2(newContent);
                initializeOnboardingBootstrapDatepicker(newContent);
                initGoogleMapsCityAutocomplete();
                $(".addMoreExperience").html(`<span>+</span> Add Experience`);
            }
        },
    });
});

$("body").on("click", "#add-other-experience", function () {
    $(".other-experience-form-input").removeClass("d-none");
    $("#add-other-experience").addClass("addMoreOtherExperience");
    $(".addMoreOtherExperience").html(`<span>+</span> Add Experience`);
});

$("body").on(
    "click",
    ".addMoreOtherExperience,#add-other-experience",
    function () {
        var id = $("#id").val();
        var url = APP_URL + "/k12connections/get_other_experience/" + id;
        var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
        $.ajax({
            url: url,
            method: "GET",
            dataType: "json",
            beforeSend: function () {
                $(".addMoreOtherExperience").html(loading);
            },
            success: function (res) {
                if (res.status === true) {
                    let newContent = $(res.view);
                    const index = $(".apendOtherExperience")[0].children.length;
                    if (index == 0) {
                        newContent[0].innerHTML =
                            newContent[0].innerHTML.replace(
                                '<hr style="margin-left: 1%; margin-bottom: 25px; width: 95.5%;">',
                                ""
                            );
                        newContent.removeClass("mt-5");
                    } else newContent.addClass("mt-5");
                    const id = Date.now();
                    newContent.find("[id]").each(function () {
                        let newId = $(this).attr("id") + "_" + id; // Add a unique suffix
                        $(this).attr("id", newId);
                    });
                    $(".apendOtherExperience").append(newContent);
                    initilizeOnboardingSelect2(newContent);
                    initializeOnboardingBootstrapDatepicker(newContent);
                    initGoogleMapsCityAutocomplete();
                    $(".addMoreOtherExperience").html(
                        `<span>+</span> Add Experience`
                    );
                }
            },
        });
    }
);

$("body").on("click", "#add-reference-experience", function () {
    var id = $("#id").val();
    var url = APP_URL + "/k12connections/get_reference/" + id;
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    $.ajax({
        url: url,
        method: "GET",
        dataType: "json",
        beforeSend: function () {
            $("#add-reference-experience").html(loading);
        },
        success: function (res) {
            if (res.status === true) {
                let newContent = $(res.view);
                const index = $(".appendReferences")[0].children.length;
                if (index == 0) {
                    newContent[0].innerHTML = newContent[0].innerHTML.replace(
                        '<hr style="margin-left: 1%; margin-bottom: 25px; width: 95.5%;">',
                        ""
                    );
                    newContent.removeClass("mt-5");
                } else newContent.addClass("mt-5");
                const id = Date.now();
                newContent.find("[id]").each(function () {
                    let newId = $(this).attr("id") + "_" + id; // Add a unique suffix
                    $(this).attr("id", newId);
                });
                $(".appendReferences").append(newContent);

                // if (index == 0){
                //     const referenceField = $('.appendReferences > .row')[0]
                //     referenceField.removeChild(referenceField.lastElementChild)
                // }

                initilizeOnboardingSelect2(newContent);
                initializeOnboardingBootstrapDatepicker(newContent);
                initGoogleMapsCityAutocomplete();
                $("#add-reference-experience").html(
                    `<span>+</span> Add Reference`
                );
            }
        },
    });
});

function slider() {
    var pathArray = window.location.pathname.split("/");

    if (pathArray[2] == "application") {
        $(".input-proficiency-range").on("input", function () {
            const value = $(this).val();
            const min = $(this).attr("min");
            const max = $(this).attr("max");
            const percent = ((value - min) / (max - min)) * 100;

            // Get the color from data-color
            const color = $(this).data("color") || "#3C89FB";

            // Set the background gradient
            $(this).css(
                "background",
                `linear-gradient(to right, ${color} 0%, ${color} ${percent}%, #E2E2E2 ${percent}%, #E2E2E2 100%)`
            );

            // Match label number using the slider's ID
            const id = $(this).attr("id"); // e.g. "input-sliderr2"
            const labelClass =
                ".number--label" + id.replace("input-sliderr", ""); // e.g. ".number--label2"

            // Update label text
            $(labelClass).text(value);
        });
    }
}

// function slider() {
//     const pathArray = window.location.pathname.split('/');

//     if (pathArray[2] == 'application') {
//         $(".input-slider, .input-slider2, .input-slider3, .input-slider4, .input-slider5, .input-slider6, .input-slider7").on("input", function () {
//             const $slider = $(this);
//             const value = $slider.val();
//             const min = parseFloat($slider.attr("min")) || 0;
//             const max = parseFloat($slider.attr("max")) || 100;
//             const percent = ((value - min) / (max - min)) * 100;

//             // Fix: Use siblings to find the label even if it's not directly after
//             $slider.siblings(".number--label").text(value);

//             // Determine color based on class
//             let color;
//             if ($slider.hasClass("input-slider") || $slider.hasClass("input-slider5")) {
//                 color = "rgb(60 137 251)";
//             } else if ($slider.hasClass("input-slider2") || $slider.hasClass("input-slider6")) {
//                 color = "#FC697D";
//             } else if ($slider.hasClass("input-slider3")) {
//                 color = "#7EC7A9";
//             } else if ($slider.hasClass("input-slider4") || $slider.hasClass("input-slider7")) {
//                 color = "#FC9F43";
//             } else {
//                 color = "#000"; // fallback
//             }

//             // Update background
//             $slider.css("background", `linear-gradient(to right, ${color} 0%, ${color} ${percent}%, #E2E2E2 ${percent}%, #E2E2E2 100%)`);
//         });

//         // Trigger on page load to update background & numbers
//         $(".input-slider, .input-slider2, .input-slider3, .input-slider4, .input-slider5, .input-slider6, .input-slider7").each(function () {
//             $(this).trigger("input");
//         });
//     }
// }

function newOnboardingSaveFirstStep(type, _this, callback) {
    var isValid = true;
    let focusElm = "";
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    if ($("#reside_united_states_yes").is(":checked") == false) {
        $("#reside_united_states_yes").css("outline-color", "red");
        $("#reside_united_states_yes").css("outline-style", "solid");
        $("#reside_united_states_yes").css("outline-width", "thin");
        $("#reside_united_states_yes").css("height", "18px");
    }
    if ($("#reside_united_states_no").is(":checked") == false) {
        $("#reside_united_states_no").css("outline-color", "red");
        $("#reside_united_states_no").css("outline-style", "solid");
        $("#reside_united_states_no").css("outline-width", "thin");
        $("#reside_united_states_no").css("height", "18px");
    }

    if (document.getElementById("location").value == "") {
        $("#location").css("border-color", "red");
        isValid = false;
        if (!focusElm) {
            focusElm = $("#location");
        }
    }

    if (document.getElementById("zip_code").value == "") {
        $("#zip_code").css("border-color", "red");
        isValid = false;
        if (!focusElm) {
            focusElm = $("#zip_code");
        }
    }

    var specify_you_work_authorization = document.getElementById(
        "specify_you_work_authorization"
    ).value;
    if (specify_you_work_authorization == "") {
        $("#specify_you_work_authorization").css("border-color", "red");
        if (!focusElm) {
            focusElm = $("#specify_you_work_authorization");
        }
    }

    if (
        !document.getElementById("reside_united_states_yes").checked &&
        !document.getElementById("reside_united_states_no").checked
    ) {
        // $('#reside_united_states_error').html('Please select do you reside in the United States');
        $("#reside_united_states_yes").css("outline-color", "red");
        $("#reside_united_states_yes").css("outline-style", "solid");
        $("#reside_united_states_yes").css("outline-width", "thin");
        $("#reside_united_states_yes").css("height", "18px");
        $("#reside_united_states_no").css("outline-color", "red");
        $("#reside_united_states_no").css("outline-style", "solid");
        $("#reside_united_states_no").css("outline-width", "thin");
        $("#reside_united_states_no").css("height", "18px");
        document.getElementsByName("reside_united_states")[0].focus();
        isValid = false;
        if (!focusElm) {
            focusElm = $("#reside_united_states_yes");
        }
    } else {
        $("#reside_united_states_yes").css("outline-color", "");
        $("#reside_united_states_yes").css("outline-style", "");
        $("#reside_united_states_yes").css("outline-width", "");

        $("#reside_united_states_no").css("outline-color", "");
        $("#reside_united_states_no").css("outline-style", "");
        $("#reside_united_states_no").css("outline-width", "");

        $("#reside_united_states_error").html("");
    }

    var residevalue = document.querySelector(
        'input[name="reside_united_states"]:checked'
    ).value;
    if (residevalue == "yes") {
        if (document.getElementById("location").value == "") {
            // $('#city_error').html('Please Enter Your City');
            $("#location").css("border-color", "red");
            isValid = false;
            if (!focusElm) {
                focusElm = $("#location");
            }
        } else {
            $("#city_error").html("");
            $("#location").css("border-color", "#d8dadc");
        }

        if (document.getElementById("zip_code").value == "") {
            // $('#zip_code_error').html('Please Enter Your Zip Code');
            $("#zip_code").css("border-color", "red");
            isValid = false;
            if (!focusElm) {
                focusElm = $("#zip_code");
            }
        } else {
            $("#zip_code_error").html("");
            $("#zip_code").css("border-color", "#d8dadc");
        }
        if (
            !document.getElementById("i_am_authorized_yes").checked &&
            !document.getElementById("i_am_authorized_no").checked
        ) {
            $("#i_am_authorized_yes").css("outline-color", "red");
            $("#i_am_authorized_yes").css("outline-style", "solid");
            $("#i_am_authorized_yes").css("outline-width", "thin");
            $("#i_am_authorized_yes").css("height", "18px");
            $("#i_am_authorized_no").css("outline-color", "red");
            $("#i_am_authorized_no").css("outline-style", "solid");
            $("#i_am_authorized_no").css("outline-width", "thin");
            $("#i_am_authorized_no").css("height", "18px");
            //$('#i_am_authorized_error').html('Please select I am Authorized to work in the United States');
            // document.getElementsByName('i_am_authorized')[0].focus();
            isValid = false;
        } else {
            $("#i_am_authorized_yes").css("outline-color", "");
            $("#i_am_authorized_yes").css("outline-style", "");
            $("#i_am_authorized_yes").css("outline-width", "");
            $("#i_am_authorized_yes").css("height", "");
            $("#i_am_authorized_no").css("outline-color", "");
            $("#i_am_authorized_no").css("outline-style", "");
            $("#i_am_authorized_no").css("outline-width", "");
            $("#i_am_authorized_no").css("height", "");
            $("#i_am_authorized_error").html("");
        }

        var authorized = document.querySelector(
            'input[name="i_am_authorized"]:checked'
        ).value;
        if (authorized == "yes") {
            var specify_you_work_authorization = document.getElementById(
                "specify_you_work_authorization"
            ).value;
            if (specify_you_work_authorization.length == "") {
                $("#specify_you_work_authorization").css("border-color", "red");
                // $('#specify_you_work_authorization_error').html('Please Enter Your Please specify your work authorization ');
                document.getElementById(
                    "specify_you_work_authorization"
                ).value = "";
                document
                    .getElementById("specify_you_work_authorization")
                    .focus();
                isValid = false;
            } else {
                $("#specify_you_work_authorization_error").html("");
                $("#specify_you_work_authorization").css(
                    "border-color",
                    "#d8dadc"
                );
            }
        }
    }

    if (!isValid) {
        focusElm.focus();
        return false;
    }

    // requiredFieldArray.remove('step-1:us-work-authorization');
    requiredFieldArray.splice(0, 1);
    //  $('.firststepbtn').addClass('next');

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var url = APP_URL + "/k12connections/submitFirstStepInstructor";
    var th = _this;
    $.ajax({
        type: "POST",
        url: url,
        data: $("#firststepid").serialize(),
        dataType: "json",
        beforeSend: function () {
            $(".firststepbtn").prop("disabled", true);
            th.innerHTML = loading;
        },
        success: function (data) {
            if (typeof callback != "undefined") {
                callback(data); //for the tab switching
            }
            if (data.success == true) {
                if (type == "save_continue") {
                    $("#step2-tab").removeClass("disableClick");
                    th.innerHTML = "Continue";
                    window.scrollTo({ top: 0, behavior: "smooth" });
                } else {
                    alertify.success(data.message);
                    th.innerHTML = "Save";
                }
            } else {
                if (type == "save_continue") {
                    th.innerHTML = "Continue";
                } else {
                    th.innerHTML = "Save";
                }
                alertify.error(data.message);
            }
        },
    });
    return true;
}

$("body").on("change", ".transcript_visible_to_school", function () {
    var hiddenInput = $(this)
        .closest(".transcript-group")
        .find('input[type="hidden"]');
    if ($(this).is(":checked")) {
        hiddenInput.val("1");
    } else {
        hiddenInput.val("0");
    }
});

// function initializeAdditionalCertificateValidation() {
//     let isValid = true;
//     let firstInvalid = null;

//     if ($('.additional_certificate').length > 0) {
//         $('.additional_certificate_error').each(function () {
//             const $el = $(this);
//             const value = $el.val();
//             const type = $el.attr('type');
//             const $label = $el.closest('label.additional_certificate_error');

//             let isEmpty = false;

//             if (type === 'file') {
//                 const existingFile = $el.attr('data-bs-file');
//                 const hasNewFile = $el[0].files && $el[0].files.length > 0;
//                 isEmpty = !existingFile && !hasNewFile;
//             } else if (!value || value.trim() === '') {
//                 if (!($el.is('input') && $el.prop('disabled'))) {
//                     isEmpty = true;
//                 }
//             }

//             const target = $label.length > 0 ? $label : $el;

//             if (isEmpty) {
//                 target.css('border', '1px solid red');
//                 if (!firstInvalid) {
//                     firstInvalid = $el;
//                 }
//                 isValid = false;
//             } else {
//                 target.css('border', '');
//             }
//         });

//         if (!isValid && firstInvalid) {
//             const scrollOffset = 100;
//             $('html, body').animate({
//                 scrollTop: firstInvalid.offset().top - scrollOffset
//             }, 500);
//         }
//     }

//     return firstInvalid;
// }
function initializeAdditionalCertificateValidation() {
    let isValid = true;
    let firstInvalid = null;

    $(".additional_certificate_error").each(function () {
        const $element = $(this);
        let $input = $element;

        // If the element is a LABEL, find the nested input
        if ($element.is("label")) {
            $input = $element.find('input.add_cert_upload_input[type="file"]');
        }

        // Skip if no input found
        if ($input.length === 0) return;

        const type = $input.attr("type");
        const value = $input.val();
        let isEmpty = false;

        if (type === "file") {
            const existingFile = $input.attr("data-bs-file");
            // const hasNewFile = $input[0].files && $input[0].files.length > 0;
            isEmpty = !existingFile;
        } else if (!value || value.trim() === "") {
            if (!($input.is("input") && $input.prop("disabled"))) {
                isEmpty = true;
            }
        }

        const target = $element.is("label") ? $element : $input;

        if (isEmpty) {
            target.css("border", "1px solid red");
            if (!firstInvalid) {
                firstInvalid = $input;
            }
            isValid = false;
        } else {
            target.css("border", "");
        }
    });

    if (!isValid && firstInvalid) {
        const scrollOffset = 100;
        $("html, body").animate(
            {
                scrollTop: firstInvalid.offset().top - scrollOffset,
            },
            500
        );
    }

    return firstInvalid;
}

function newOnboardingSaveSecondStep(type, _this, callback) {
    // initializeAdditionalCertificateValidation();
    let isValid = true;
    let focusElm = "";
    const firstInvalidElm = initializeAdditionalCertificateValidation();

    if (firstInvalidElm !== null) {
        // Optionally focus the element
        firstInvalidElm.focus();
        return;
    }

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    // Example output: ['1', '0', '1', '1', '0']
    const Validtillcheckboxvalues = Array.from(
        document.querySelectorAll(".valid_till_check_box")
    ).map((cb) => (cb.checked ? "1" : "0"));
    const visibletoschoolcheckboxes = Array.from(
        document.querySelectorAll(".upload_certification_checkbox")
    ).map((cb) => (cb.checked ? "1" : "0"));

    const valid_till_select_dataes = Array.from(
        document.querySelectorAll(".add_cert_valid_till_input")
    ).map((select) => (select.disabled ? "none" : select.value));

    $(".teaching_certification_states").each(function () {
        // Get the Select2 container related to this select element
        const select2Container = $(this).next(".select2"); // or use .siblings() if needed
        // Add the class to the visual Select2 container
        select2Container
            .find(".select2-selection")
            .addClass("error_certificate_license");
    });

    //for the updation of theadd category input
    // $(".other_subcategory_input_field").each(function() {

    //     let inputVal = $(this).val();
    //     if (inputVal) {
    //         let parentDiv = $(this).closest(".other_subcategory_select_div");
    //         let select = parentDiv.find("select.add_sub_category");

    //         let otherOption = select.find("option").filter(function() {
    //             return $(this).text().trim() === "Other";
    //         });

    //         otherOption.attr("value", inputVal);
    //         select.val(inputVal);
    //     }
    // });

    // $(".other_category_input_field").each(function() {

    //     let inputVal = $(this).val();
    //     if (inputVal) {
    //         let parentDiv = $(this).closest(".select_category_div");
    //         let select = parentDiv.find("select.add_select_category");

    //         let otherOption = select.find("option").filter(function() {
    //             return $(this).text().trim() === "Other";
    //         });

    //         otherOption.attr("value", inputVal);
    //         select.val(inputVal);
    //     }
    // });

    // let firstInvalidInput = null;
    // // Validate .other_input fields
    // $(".other_input").each(function()
    // {
    //     if ($(this).css("display") !== "none" && !$(this).val().trim()) {
    //         $(this).css("border", "1px solid red");

    //         // Save the first invalid input to scroll to
    //         if (!firstInvalidInput) {
    //             firstInvalidInput = $(this);
    //         }
    //     }
    //     else{
    //         $(this).css("border", "");

    //     }
    // });

    // If there's an invalid input, scroll to the first one and stop further processing
    // if (firstInvalidInput) {
    //     $('html, body').animate({
    //         scrollTop: firstInvalidInput.offset().top - 100
    //     }, 500);
    //     // return;
    //     isValid = false;
    // }

    //    if ($('.additional_certificate').length !== 0) {

    //         $('.additional_certificate_error').each(function ()
    //         {

    //             const $el = $(this);

    //             if ($el.is('input, select, textarea'))
    //                 {
    //                 const value = $el.val();
    //                 const type = $el.attr('type');
    //                 const $label = $el.closest('label.additional_certificate_error');

    //                 let isEmpty = false;

    //                 if (type === 'file')
    //             {
    //                     const existingFile = $el.attr('data-bs-file');

    //                     // ✅ Validation fails only if BOTH are empty
    //                     isEmpty = !(existingFile);

    //                 }
    //                 else
    //                 {
    //                     if (!value || value.trim() === '') {
    //                         if (!($el.is('input') && $el.is(':disabled'))) {
    //                             isEmpty = true;
    //                         }
    //                     }
    //                 }

    //                 if (isEmpty) {
    //                     if ($label.length > 0) {
    //                         $label.css('border', '1px solid red');
    //                         focusElm = $label;
    //                     } else {
    //                         $el.css('border', '1px solid red');
    //                         focusElm = $el;
    //                     }
    //                     isValid = false;
    //                 } else {
    //                     if ($label.length > 0) {
    //                         $label.css('border', '');
    //                     } else {
    //                         $el.css('border', '');
    //                     }
    //                 }
    //             }
    //         });
    //     }

    if ($("#certification_yes").is(":checked") == false) {
        $("#certification_yes").css("outline-color", "red");
        $("#certification_yes").css("outline-style", "solid");
        $("#certification_yes").css("outline-width", "thin");
        $("#certification_yes").css("height", "18px");
    }
    if ($("#certification_no").is(":checked") == false) {
        $("#certification_no").css("outline-color", "red");
        $("#certification_no").css("outline-style", "solid");
        $("#certification_no").css("outline-width", "thin");
        $("#certification_no").css("height", "18px");
    }

    if (
        !document.getElementById("certification_yes").checked &&
        !document.getElementById("certification_no").checked
    ) {
        // $('#certification_error').html('Please select I am a certified teacher');
        $("#certification_yes").css("outline-color", "red");
        $("#certification_yes").css("outline-style", "solid");
        $("#certification_yes").css("outline-width", "thin");
        $("#certification_yes").css("height", "18px");
        $("#certification_no").css("outline-color", "red");
        $("#certification_no").css("outline-style", "solid");
        $("#certification_no").css("outline-width", "thin");
        $("#certification_no").css("height", "18px");

        document.getElementsByName("certification")[0].focus();
        isValid = false;
        focusElm = $("#certification_no");
    } else {
        $("#certification_yes").css("outline-color", "");
        $("#certification_yes").css("outline-style", "");
        $("#certification_yes").css("outline-width", "");
        $("#certification_yes").css("height", "");
        $("#certification_no").css("outline-color", "");
        $("#certification_no").css("outline-style", "");
        $("#certification_no").css("outline-width", "");
        $("#certification_no").css("height", "");

        $("#certification_error").html("");
    }
    var certification = document.querySelector(
        'input[name="certification"]:checked'
    ).value;
    if (certification == "yes") {
        var teaching_certification_year = $(".teaching_certification_year");
        var teaching_certification_states = $(".teaching_certification_states");
        var certified_special_education = $(".certified_special_education");
        var certificate = $(".certificate");
        var certificate_value = $(".certificate_value");
        var certification_visible_to_school = $(
            ".certification_visible_to_school"
        );
        var credentialing_agency = $(".credentialing_agency");

        if (
            teaching_certification_year.filter(function () {
                return $(this).val().length > 0;
            }).length == teaching_certification_year.length &&
            teaching_certification_states.filter(function () {
                return $(this).val().length > 0;
            }).length == teaching_certification_states.length &&
            certified_special_education.filter(function () {
                return $(this).val().length > 0;
            }).length == certified_special_education.length &&
            credentialing_agency.filter(function () {
                return $(this).val().length > 0;
            }).length == credentialing_agency.length
        ) {
            teaching_certification_year.each(function () {
                $(this).css("border-color", "#d8dadc");
            });
            teaching_certification_states.each(function () {
                $(this).css("border-color", "#d8dadc");
                $(this).next().children().children().removeClass("brederror");
            });
            certified_special_education.each(function () {
                $(this).css("border-color", "#d8dadc");
                $(this).next().children().children().removeClass("brederror");
            });

            credentialing_agency.each(function () {
                $(this).css("border-color", "#d8dadc");
            });

            certificate_value.each(function () {
                var inputElement = $(this).next("input").next("input");
                // var file = inputElement.length != 0 ? inputElement[0].files[0] : undefined;
                var file = inputElement[0]?.files?.[0];
                var ext =
                    file != undefined
                        ? inputElement.val().split(".").pop().toLowerCase()
                        : $(this)
                              .next("input")
                              .val()
                              .split(".")
                              .pop()
                              .toLowerCase();
                if (ext == "") {
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .css("border-color", "red");
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .addClass("brederror");
                    $(this)
                        .closest(".login__form")
                        .next(".certificate_err")
                        .text("");
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this)
                            .closest(".login__form")
                            .find(".certificate");
                    }
                    // $(this).next("input").focus();
                } else if (
                    $.inArray(ext, [
                        "doc",
                        "docx",
                        "pdf",
                        "png",
                        "jpg",
                        "jpeg",
                    ]) == -1
                ) {
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .css("border-color", "red");
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .addClass("brederror");
                    $(this)
                        .closest(".login__form")
                        .next(".certificate_err")
                        .text(
                            "The file format is invalid. Please upload a file in one of the supported formats."
                        );
                    // $(this).closest('.login__form').find('.certificate').focus();
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this)
                            .closest(".login__form")
                            .find(".certificate");
                    }
                } else if (file && file.size > 1048576) {
                    // Check file size (1MB = 1048576 bytes)
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .css("border-color", "red");
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .addClass("brederror");
                    $(this)
                        .closest(".login__form")
                        .next(".certificate_err")
                        .text("File size exceeds the maximum limit.");
                    // $(this).closest('.login__form').find('.certificate').focus();
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this)
                            .closest(".login__form")
                            .find(".certificate");
                    }
                } else {
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .css("border-color", "#d8dadc");
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .removeClass("brederror");
                    $(this)
                        .closest(".login__form")
                        .next(".certificate_err")
                        .text("");
                }
            });

            var certi = $(".certified_special_education")
                .children("option:selected")
                .toArray()
                .map((item) => item.value);

            if (jQuery.inArray("Other", certi) != -1) {
                // var certifications_other = document.getElementById('certifications_other').value;
                var certifications_other = $(".certifications_other");
                certifications_other.each(function () {
                    let certificationHasClass = $(this)
                        .closest(".Certificationsother ")
                        .hasClass("d-none");
                    if (!certificationHasClass) {
                        if ($(this).val().trim() == "") {
                            $(this).css("border-color", "red");
                            isValid = false;
                            if (!focusElm) {
                                focusElm = $(this);
                            }
                        } else {
                            $(this).css("border-color", "#d8dadc");
                        }
                    }
                });
            }

            var agency = $(".credentialing_agency")
                .children("option:selected")
                .toArray()
                .map((item) => item.value);

            if (jQuery.inArray("Other", agency) != -1) {
                // var credentialing_agency_other = document.getElementById('credentialing_agency_other').value;
                var credentialing_agency_other = $(
                    ".credentialing_agency_other"
                );
                credentialing_agency_other.each(function () {
                    let agencyHasClass = $(this)
                        .closest(".credentialingAgencyOther")
                        .hasClass("d-none");
                    let agencyHasVisible = $(this)
                        .closest(".credentialingAgencyOther")
                        .hasClass("invisible");
                    if (!agencyHasClass && !agencyHasVisible) {
                        if ($(this).val().trim() == "") {
                            $(this).css("border-color", "red");
                            isValid = false;
                            if (!focusElm) {
                                focusElm = $(this);
                            }
                        } else {
                            $(this).css("border-color", "#d8dadc");
                        }
                    }
                });
            }
        } else {
            teaching_certification_year.each(function () {
                if ($(this).val().length === 0) {
                    $(this).css("border-color", "red");
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this).next("svg");
                    }
                    // $(this).next('svg').focus();
                } else {
                    $(this).css("border-color", "#d8dadc");
                }
            });
            teaching_certification_states.each(function () {
                if (this.selectedOptions.length === 0) {
                    $(this).css("border-color", "red");
                    isValid = false;
                    // $(this).focus();
                    if (!focusElm) {
                        focusElm = $(this);
                    }
                    $(this).next().children().children().addClass("brederror");
                } else {
                    $(this).css("border-color", "#d8dadc");
                    $(this)
                        .next()
                        .children()
                        .children()
                        .removeClass("brederror");
                }
            });
            certified_special_education.each(function () {
                if ($(this).val().length === 0) {
                    $(this).css("border-color", "red");
                    isValid = false;
                    // $(this).focus();
                    if (!focusElm) {
                        focusElm = $(this);
                    }
                    $(this).next().children().children().addClass("brederror");
                } else {
                    $(this).css("border-color", "#d8dadc");
                    $(this)
                        .next()
                        .children()
                        .children()
                        .removeClass("brederror");
                    // var certificationCheckboxes = $('.certification_visible_to_school');
                    // certificationCheckboxes.each(function () {
                    //     if ($(this).is(':checked')) {
                    //         $(this).next('input').val('1'); // If any checkbox is checked, set isChecked to true

                    //     } else {
                    //         $(this).next('input').val('0');
                    //         $(this).next('input')
                    //     }
                    // });
                }
            });
            credentialing_agency.each(function () {
                if ($(this).val().length === 0) {
                    $(this).css("border-color", "red");
                    isValid = false;
                    // $(this).focus();
                    if (!focusElm) {
                        focusElm = $(this);
                    }
                } else {
                    $(this).css("border-color", "#d8dadc");
                }
            });

            certificate_value.each(function () {
                var inputElement = $(this).next("input").next("input");
                var file = inputElement[0]?.files?.[0];
                if ($(this).val() == "") {
                    var ext =
                        file != undefined
                            ? inputElement.val().split(".").pop().toLowerCase()
                            : $(this)
                                  .next("input")
                                  .val()
                                  .split(".")
                                  .pop()
                                  .toLowerCase();
                    if (ext == "") {
                        $(this)
                            .next("input")
                            .next("input")
                            .next("span")
                            .css("border-color", "red");
                        $(this)
                            .next("input")
                            .next("input")
                            .next("span")
                            .addClass("brederror");
                        $(this)
                            .closest(".login__form")
                            .next(".certificate_err")
                            .text("");
                        isValid = false;
                        if (!focusElm) {
                            focusElm = $(this)
                                .closest(".login__form")
                                .find(".certificate");
                        }
                        // $(this).next("input").focus();
                    } else if (
                        $.inArray(ext, [
                            "doc",
                            "docx",
                            "pdf",
                            "png",
                            "jpg",
                            "jpeg",
                        ]) == -1
                    ) {
                        $(this)
                            .next("input")
                            .next("span")
                            .css("border-color", "red");
                        $(this)
                            .next("input")
                            .next("span")
                            .addClass("brederror");
                        $(this)
                            .closest(".login__form")
                            .next(".certificate_err")
                            .text(
                                "The file format is invalid. Please upload a file in one of the supported formats."
                            );
                        // $(this).closest('.login__form').find('.certificate').focus();
                        isValid = false;
                        if (!focusElm) {
                            focusElm = $(this)
                                .closest(".login__form")
                                .find(".certificate");
                        }
                    } else if (file && file.size > 1048576) {
                        // Check file size (1MB = 1048576 bytes)
                        $(this)
                            .next("input")
                            .next("span")
                            .css("border-color", "red");
                        $(this)
                            .next("input")
                            .next("span")
                            .addClass("brederror");
                        $(this)
                            .closest(".login__form")
                            .next(".certificate_err")
                            .text("File size exceeds the maximum limit.");
                        // $(this).next("input").focus();
                        isValid = false;
                        if (!focusElm) {
                            focusElm = $(this)
                                .closest(".login__form")
                                .find(".certificate");
                        }
                    } else {
                        $(this)
                            .next("input")
                            .next("input")
                            .next("span")
                            .css("border-color", "#d8dadc");
                        $(this)
                            .next("input")
                            .next("input")
                            .next("span")
                            .removeClass("brederror");
                        $(this)
                            .closest(".login__form")
                            .next(".certificate_err")
                            .text("");
                        const $this = $(this)
                            .next("input")
                            .next("input")
                            .next("span");
                        const file = $this[0]?.files?.item(0);
                        const text =
                            $this[0]?.parentElement.querySelector(
                                ".certificate_text"
                            );
                        if (file && text) {
                            text.innerText = file.name;
                        }
                    }

                    // if ($(this).next("input").val() == "") {
                    //     $(this).next("input").next("span").css("border-color", "red");
                    //     $(this).next("input").next("span").addClass('brederror');
                    //     isValid = false;
                    //     $(this).next("input").focus();
                    // } else {
                    //     var ext = $(this).next("input").val().split('.').pop().toLowerCase();
                    //     if ($.inArray(ext, ["doc", "docx", "pdf", "png", "jpg", "jpeg"]) == -1) {
                    //         $(this).next("input").next("span").css("border-color", "red");
                    //         $(this).next("input").next("span").addClass('brederror');
                    //         $(this).closest('.login__form').next('.certificate_err').text('The file format is invalid. Please upload a file in one of the supported formats.');
                    //         $(this).closest('.login__form').find('.certificate').focus();
                    //         isValid = false;
                    //     } else if (file && file.size > 1048576) { // Check file size (1MB = 1048576 bytes)
                    //         $(this).next("input").next("span").css("border-color", "red");
                    //         $(this).next("input").next("span").addClass('brederror');
                    //         $(this).closest('.login__form').next('.certificate_err').text('The file size is invalid. Please upload a file in one of the supported size.');
                    //         $(this).next("input").focus();
                    //         isValid = false;
                    //     } else {
                    //         $(this).next("input").next("input").next("span").css("border-color", "#d8dadc");
                    //         $(this).next("input").next("input").next("span").removeClass('brederror');
                    //         $(this).closest('.login__form').next('.certificate_err').text('');
                    //         const $this = $(this).next("input").next("input").next("span");
                    //         const file = $this[0]?.files?.item(0);
                    //         const text = $this[0]?.parentElement.querySelector('.certificate_text');
                    //         if (file && text) {
                    //             text.innerText = file.name;
                    //         }
                    //     }
                    // }
                } else {
                    var ext = $(this)
                        .next("input")
                        .val()
                        .split(".")
                        .pop()
                        .toLowerCase();
                    if (
                        $.inArray(ext, [
                            "doc",
                            "docx",
                            "pdf",
                            "png",
                            "jpg",
                            "jpeg",
                        ]) == -1
                    ) {
                        $(this)
                            .next("input")
                            .next("span")
                            .css("border-color", "red");
                        $(this)
                            .next("input")
                            .next("span")
                            .addClass("brederror");
                        $(this)
                            .closest(".login__form")
                            .next(".certificate_err")
                            .text(
                                "The file format is invalid. Please upload a file in one of the supported formats."
                            );
                        $(this)
                            .closest(".login__form")
                            .find(".certificate")
                            .focus();
                        isValid = false;
                        if (!focusElm) {
                            focusElm = $(this)
                                .closest(".login__form")
                                .find(".certificate");
                        }
                    }
                }
            });

            // certification_visible_to_school.each(function() {
            //     if ($(this).prop('checked') == false) {
            //         $(this).css("border-color", "red");
            //         $(this).focus();
            //     } else {
            //         $(this).css('border-color', '#d8dadc');
            //     }
            // });

            var certi = $(".certified_special_education")
                .children("option:selected")
                .toArray()
                .map((item) => item.value);

            if (jQuery.inArray("Other", certi) != -1) {
                // var certifications_other = document.getElementById('certifications_other').value;
                var certifications_other = $(".certifications_other");
                certifications_other.each(function () {
                    let certificationHasClass = $(this)
                        .closest(".Certificationsother ")
                        .hasClass("d-none");
                    if (!certificationHasClass) {
                        if ($(this).val().trim() == "") {
                            $(this).css("border-color", "red");
                            isValid = false;
                            if (!focusElm) {
                                focusElm = $(this);
                            }
                        } else {
                            $(this).css("border-color", "#d8dadc");
                        }
                    }
                });
            }

            var agency = $(".credentialing_agency")
                .children("option:selected")
                .toArray()
                .map((item) => item.value);

            if (jQuery.inArray("Other", agency) != -1) {
                // var credentialing_agency_other = document.getElementById('credentialing_agency_other').value;
                var credentialing_agency_other = $(
                    ".credentialing_agency_other"
                );
                credentialing_agency_other.each(function () {
                    let agencyHasClass = $(this)
                        .closest(".credentialingAgencyOther")
                        .hasClass("d-none");
                    let agencyHasVisible = $(this)
                        .closest(".credentialingAgencyOther")
                        .hasClass("invisible");
                    if (!agencyHasClass && !agencyHasVisible) {
                        if ($(this).val().trim() == "") {
                            $(this).css("border-color", "red");
                            isValid = false;
                            if (!focusElm) {
                                focusElm = $(this);
                            }
                        } else {
                            $(this).css("border-color", "#d8dadc");
                        }
                    }
                });
            }
        }
    } else {
        var profile_type = document.getElementById("profile_type").value;
        if (profile_type.length == "") {
            // $('#profile_type_error').html('Please Enter Profile Type');
            $("#profile_type").css("border-color", "red");
            document.getElementById("profile_type").value = "";
            // document.getElementById('profile_type').focus();
            isValid = false;
            if (!focusElm) {
                focusElm = document.getElementById("profile_type");
            }
        } else {
            $("#profile_type_error").html("");
            $("#profile_type").css("border-color", "#d8dadc");
        }

        if (profile_type == "Other") {
            var other_specify = document.getElementById("other_specify").value;
            if (other_specify.length == "") {
                // $('#profile_type_error').html('Please Enter Profile Type');
                $("#other_specify").css("border-color", "red");
                document.getElementById("other_specify").value = "";
                // document.getElementById('other_specify').focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = document.getElementById("other_specify");
                }
            } else {
                $("#other_specify_error").html("");
                $("#other_specify").css("border-color", "#d8dadc");
            }
        }
    }
    // certification_visible_to_school.each(function() {
    //     if (!$(this).prop('checked')) {
    //         $(this).val('0');
    //     }
    // });

    var highest_level_of_education = $(".highest_level_of_education1");
    var month_and_year_graduation = $(".month_and_year_graduation1");
    var GPA = $(".GPA1");
    var transcript = $(".transcript");
    var transcript_visible_to_school = $(".transcript_visible_to_school");
    var transcript_value = $(".transcript_value");
    var major = $(".major");
    var school_college_name = $(".school_college_name");
    var school_location = $(".school_location");

    if ($(".appendAcademics .row").length == 0) {
        $(".error_education").text("Add atleast one education");
        $(".error_education")[0].scrollIntoView();
        isValid = false;
        if (!focusElm) {
            focusElm = $(".error_education");
        }
    }
    if (
        month_and_year_graduation.filter(function () {
            return $(this).val().length > 0;
        }).length == month_and_year_graduation.length &&
        highest_level_of_education.filter(function () {
            return $(this).val().length > 0;
        }).length == highest_level_of_education.length &&
        GPA.filter(function () {
            return $(this).val().length > 0;
        }).length == GPA.length &&
        major.filter(function () {
            return $(this).val().length > 0;
        }).length == major.length &&
        school_college_name.filter(function () {
            return $(this).val().length > 0;
        }).length == school_college_name.length &&
        school_location.filter(function () {
            return $(this).val().length > 0;
        }).length == school_location.length
    ) {
        month_and_year_graduation.each(function () {
            $(this).css("border-color", "#d8dadc");
            $(".error_education").text("");
        });
        highest_level_of_education.each(function () {
            $(this).css("border-color", "#d8dadc");
            $(this).next().children().children().removeClass("brederror");
            $(".error_education").text("");
        });
        GPA.each(function () {
            $(this).css("border-color", "#d8dadc");
            $(".error_education").text("");
        });

        major.each(function () {
            $(this).css("border-color", "#d8dadc");
            $(".error_education").text("");
        });

        transcript_value.each(function () {
            var inputElement = $(this).next("input").next("input");
            var file = inputElement[0]?.files?.[0];
            var ext =
                file != undefined
                    ? inputElement.val().split(".").pop().toLowerCase()
                    : $(this)
                          .next("input")
                          .val()
                          .split(".")
                          .pop()
                          .toLowerCase();
            if (ext == "") {
                $(this)
                    .next("input")
                    .next("input")
                    .next("span")
                    .css("border-color", "red");
                $(this)
                    .next("input")
                    .next("input")
                    .next("span")
                    .addClass("brederror");
                $(this)
                    .closest(".login__form")
                    .next(".transcript_err")
                    .text("");
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this)
                        .closest(".login__form")
                        .find(".transcript");
                }
                // $(this).next("input").focus();
            } else if (
                $.inArray(ext, ["doc", "docx", "pdf", "png", "jpg", "jpeg"]) ==
                -1
            ) {
                $(this)
                    .next("input")
                    .next("input")
                    .next("span")
                    .css("border-color", "red");
                $(this)
                    .next("input")
                    .next("input")
                    .next("span")
                    .addClass("brederror");
                $(this)
                    .closest(".login__form")
                    .next(".transcript_err")
                    .text(
                        "The file format is invalid. Please upload a file in one of the supported formats."
                    );
                // $(this).closest('.login__form').find('.transcript').focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this)
                        .closest(".login__form")
                        .find(".transcript");
                }
            } else if (file && file.size > 1048576) {
                // Check file size (1MB = 1048576 bytes)
                $(this)
                    .next("input")
                    .next("input")
                    .next("span")
                    .css("border-color", "red");
                $(this)
                    .next("input")
                    .next("input")
                    .next("span")
                    .addClass("brederror");
                $(this)
                    .closest(".login__form")
                    .next(".transcript_err")
                    .text("File size exceeds the maximum limit.");
                // $(this).closest('.login__form').find('.transcript').focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this)
                        .closest(".login__form")
                        .find(".transcript");
                }
            } else {
                $(this)
                    .next("input")
                    .next("input")
                    .next("span")
                    .css("border-color", "#d8dadc");
                $(this)
                    .next("input")
                    .next("input")
                    .next("span")
                    .removeClass("brederror");
                $(".error_education").text("");
                $(this)
                    .closest(".login__form")
                    .next(".transcript_err")
                    .text("");
            }
        });

        school_college_name.each(function () {
            $(this).css("border-color", "#d8dadc");
        });

        school_location.each(function () {
            $(this).css("border-color", "#d8dadc");
        });
    } else {
        month_and_year_graduation.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this).next("svg");
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });
        highest_level_of_education.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                isValid = false;
                // $(this).focus();
                if (!focusElm) {
                    focusElm = $(this);
                }
                $(this).next().children().children().addClass("brederror");
            } else {
                $(this).css("border-color", "#d8dadc");
                $(this).next().children().children().removeClass("brederror");
            }
        });
        GPA.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                isValid = false;
                // $(this).focus();
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        major.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                isValid = false;
                // $(this).focus();
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        transcript_value.each(function () {
            var inputElement = $(this).next("input").next("input");
            var file = inputElement[0]?.files?.[0];
            if ($(this).val() == "") {
                var ext =
                    file != undefined
                        ? inputElement.val().split(".").pop().toLowerCase()
                        : $(this)
                              .next("input")
                              .val()
                              .split(".")
                              .pop()
                              .toLowerCase();
                if (ext == "") {
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .css("border-color", "red");
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .addClass("brederror");
                    $(this)
                        .closest(".login__form")
                        .next(".transcript_err")
                        .text("");
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this)
                            .closest(".login__form")
                            .find(".transcript");
                    }
                    $(this).next("input").focus();
                } else if (
                    $.inArray(ext, [
                        "doc",
                        "docx",
                        "pdf",
                        "png",
                        "jpg",
                        "jpeg",
                    ]) == -1
                ) {
                    $(this)
                        .next("input")
                        .next("span")
                        .css("border-color", "red");
                    //$('#resume_error').text('The file format is invalid. Please upload a file in one of the supported formats.');
                    $(this)
                        .closest(".login__form")
                        .next(".transcript_err")
                        .text(
                            "The file format is invalid. Please upload a file in one of the supported formats."
                        );
                    // $(this).closest('.login__form').find('.transcript').focus();
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this)
                            .closest(".login__form")
                            .find(".transcript");
                    }
                } else if (file && file.size > 1048576) {
                    // Check file size (1MB = 1048576 bytes)
                    $(this)
                        .next("input")
                        .next("span")
                        .css("border-color", "red");
                    $(this).next("input").next("span").addClass("brederror");
                    $(this)
                        .closest(".login__form")
                        .next(".transcript_err")
                        .text("File size exceeds the maximum limit.");
                    // $(this).closest('.login__form').find('.transcript').focus();
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this)
                            .closest(".login__form")
                            .find(".transcript");
                    }
                } else {
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .css("border-color", "#d8dadc");
                    $(this)
                        .next("input")
                        .next("input")
                        .next("span")
                        .removeClass("brederror");
                    $(this)
                        .closest(".login__form")
                        .next(".transcript_err")
                        .text("");
                }

                // if ($(this).next("input").val() == "") {
                //     $(this).next("input").next("input").next("span").css("border-color", "red");
                //     $(this).next("input").next("input").next("span").addClass('brederror');
                //     isValid = false;
                //     $(this).next("input").focus();
                // } else {
                //     var ext = $(this).next("input").val().split('.').pop().toLowerCase();
                //     if ($.inArray(ext, ["doc", "docx", "pdf", "png", "jpg", "jpeg"]) == -1) {
                //         $(this).next("input").next("span").css("border-color", "red");
                //         //$('#resume_error').text('The file format is invalid. Please upload a file in one of the supported formats.');
                //         $(this).closest('.login__form').next('.transcript_err').text('The file format is invalid. Please upload a file in one of the supported formats.');
                //         $(this).closest('.login__form').find('.transcript').focus();
                //         isValid = false;
                //     } else if (file && file.size > 1048576) { // Check file size (1MB = 1048576 bytes)
                //         $(this).next("input").next("span").css("border-color", "red");
                //         $(this).next("input").next("span").addClass('brederror');
                //         $(this).closest('.login__form').next('.transcript_err').text('The file size is invalid. Please upload a file in one of the supported size.');
                //         $(this).closest('.login__form').find('.transcript').focus();
                //         isValid = false;
                //     } else {
                //         $(this).next("input").next("input").next("span").css("border-color", "#d8dadc");
                //         $(this).next("input").next("input").next("span").removeClass('brederror');
                //         $(this).closest('.login__form').next('.transcript_err').text('');
                //     }
                // }
            } else {
                var ext = $(this)
                    .next("input")
                    .val()
                    .split(".")
                    .pop()
                    .toLowerCase();
                if (
                    $.inArray(ext, [
                        "doc",
                        "docx",
                        "pdf",
                        "png",
                        "jpg",
                        "jpeg",
                    ]) == -1
                ) {
                    $(this)
                        .next("input")
                        .next("span")
                        .css("border-color", "red");
                    //$('#resume_error').text('The file format is invalid. Please upload a file in one of the supported formats.');
                    $(this)
                        .closest(".login__form")
                        .next(".transcript_err")
                        .text(
                            "The file format is invalid. Please upload a file in one of the supported formats."
                        );
                    $(this).closest(".login__form").find(".transcript").focus();
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this)
                            .closest(".login__form")
                            .find(".transcript");
                    }
                }
            }
        });

        school_college_name.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                isValid = false;
                // $(this).focus();
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        school_location.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                isValid = false;
                // $(this).focus();
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        $(".error_education").text("");

        // transcript_visible_to_school.each(function() {
        //     if ($(this).prop('checked') == false) {
        //         $(this).css("border-color", "red");
        //         isValid = false;
        //         $(this).focus();
        //     } else {
        //         $(this).css('border-color', '#d8dadc');
        //     }
        // });
    }

    var education = $(".highest_level_of_education1")
        .children("option:selected")
        .toArray()
        .map((item) => item.value);

    if (jQuery.inArray("Other Professional Degree", education) != -1) {
        var other_education = $(".other_degree");
        other_education.each(function () {
            let hasVisible = $(this)
                .closest(".otherEducation-section")
                .hasClass("d-none");
            console.log(!hasVisible);
            console.log($(this).val().trim());
            if (!hasVisible) {
                if ($(this).val().trim() == "") {
                    $(this).css("border-color", "red");
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this);
                    }
                } else {
                    $(this).css("border-color", "#d8dadc");
                }
            }
        });
    }

    if ($("#total_experience").val() == "") {
        $("#total_experience").css("border-color", "red");
        isValid = false;
        if (!focusElm) {
            focusElm = $("#total_experience");
        }
        // $('#total_experience').focus();
    } else {
        $("#total_experience").css("border-color", "#d8dadc");
    }

    var employer_name = $(".employer_name");
    var location = $(".location");
    var position = $(".position");
    var start_date = $(".start_date");
    var end_date = $(".end_date");
    var working_here = $(".currently_working_here");
    // var experience_teaching_ages = $('.experience_teaching_ages');
    // var main_experience_teaching_ages = $('.main_experience_teaching_ages').val();
    if (
        employer_name.filter(function () {
            return $(this).val().length > 0;
        }).length == employer_name.length &&
        location.filter(function () {
            return $(this).val().length > 0;
        }).length == location.length &&
        position.filter(function () {
            return $(this).val().length > 0;
        }).length == position.length &&
        start_date.filter(function () {
            return $(this).val().length > 0;
        }).length == start_date.length &&
        end_date.filter(function () {
            return $(this).val().length;
        }).length == end_date
    ) {
        employer_name.each(function () {
            $(this).css("border-color", "#d8dadc");
        });

        location.each(function () {
            $(this).css("border-color", "#d8dadc");
        });
        position.each(function () {
            $(this).css("border-color", "#d8dadc");
        });
        start_date.each(function () {
            $(this).css("border-color", "#d8dadc");
        });
        end_date.each(function () {
            $(this).css("border-color", "#d8dadc");
        });
        // experience_teaching_ages.each(function () {
        //     $(this).css('border-color', '#d8dadc');
        // });
    } else {
        employer_name.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                isValid = false;
                // $(this).focus();
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        location.each(function () {
            if ($(this).val() === "") {
                // $(this).focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this);
                }
                $(this).css("border-color", "red");
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        position.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                isValid = false;
                // $(this).focus();
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        start_date.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                isValid = false;
                // $(this).next('svg').focus();
                if (!focusElm) {
                    focusElm = $(this).next("svg");
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        if (working_here.filter(":checked").length > 0) {
            // If there's any checked checkbox in the working_here group
            working_here.each(function () {
                var working_here_checkbox = $(this);
                var end_date_field = working_here_checkbox.closest(".end_date");
                var hidden_input = working_here_checkbox.siblings(
                    'input[type="hidden"][name="working_here_status"]'
                );

                // If the checkbox is unchecked
                if (working_here_checkbox.is(":checked") == false) {
                    end_date_field.each(function () {
                        if ($(this).val() === "") {
                            $(this).css("border-color", "red");
                            isValid = false;
                            // $(this).next('svg').focus();
                            if (!focusElm) {
                                focusElm = $(this).next("svg");
                            }
                        } else {
                            $(this).css("border-color", "#d8dadc");
                        }
                    });
                } else {
                    // If the checkbox is checked, reset border color
                    end_date_field.each(function () {
                        $(this).css("border-color", "#d8dadc");
                    });
                }
            });
        } else {
            // If no checkbox is checked or there is no data in working_here, run the second validation
            end_date.each(function () {
                if ($(this).val() === "") {
                    $(this).css("border-color", "red");
                    isValid = false;
                    // $(this).next('svg').focus();
                    if (!focusElm) {
                        focusElm = $(this).next("svg");
                    }
                } else {
                    $(this).css("border-color", "#d8dadc");
                }
            });
        }

        // experience_teaching_ages.each(function () {
        //     if ($(this).val().length == 0) {
        //         $(this).css('border-color', 'red');
        //         $(this).next().children().children().addClass('brederror');
        //         isValid = false;
        //     } else {
        //         $(this).css('border-color', '#d8dadc');
        //         $(this).next().children().children().removeClass('brederror');
        //     }
        // });
    }

    // if (main_experience_teaching_ages.length == 0) {
    //     $('.main_experience_teaching_ages').next().children().children().addClass('brederror');
    //     $('.main_experience_teaching_ages').focus()
    //     isValid = false;
    // }else{
    //     $('.main_experience_teaching_ages').next().children().children().removeClass('brederror');
    // }

    var other_employer_name = $(".other_employer_name");
    var other_location = $(".other_location");
    var other_position = $(".other_position");
    var other_start_date = $(".other_start_date");
    var other_end_date = $(".other_end_date");
    var other_working_here = $(".other_currently_working_here");

    if (
        other_employer_name.filter(function () {
            return $(this).val().length > 0;
        }).length == other_employer_name.length &&
        other_location.filter(function () {
            return $(this).val().length > 0;
        }).length == other_location.length &&
        other_position.filter(function () {
            return $(this).val().length > 0;
        }).length == other_position.length &&
        other_start_date.filter(function () {
            return $(this).val().length > 0;
        }).length == other_start_date.length &&
        other_end_date.filter(function () {
            return $(this).val().length > 0;
        }).length == other_end_date.length
    ) {
        other_employer_name.each(function () {
            $(this).css("border-color", "#d8dadc");
        });

        other_location.each(function () {
            $(this).css("border-color", "#d8dadc");
        });
        other_position.each(function () {
            $(this).css("border-color", "#d8dadc");
        });
        other_start_date.each(function () {
            $(this).css("border-color", "#d8dadc");
        });
        other_end_date.each(function () {
            $(this).css("border-color", "#d8dadc");
        });
    } else {
        other_employer_name.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                // $(this).focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        other_location.each(function () {
            if ($(this).val() === "") {
                // $(this).focus();
                $(this).css("border-color", "red");
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        other_position.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                // $(this).focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this).next("svg");
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        other_start_date.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                // $(this).next('svg').focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this).next("svg");
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        if (other_working_here.filter(":checked").length > 0) {
            // If there's any checked checkbox in the other_working_here group
            other_working_here.each(function () {
                var other_working_here_checkbox = $(this);
                var end_date_field =
                    other_working_here_checkbox.closest(".other_end_date");

                // If the checkbox is unchecked
                if (other_working_here_checkbox.is(":checked") == false) {
                    end_date_field.each(function () {
                        if ($(this).val() === "") {
                            $(this).css("border-color", "red");
                            isValid = false;
                            // $(this).next('svg').focus();
                            if (!focusElm) {
                                focusElm = $(this).next("svg");
                            }
                        } else {
                            $(this).css("border-color", "#d8dadc");
                        }
                    });
                } else {
                    // If the checkbox is checked, reset the border color
                    end_date_field.each(function () {
                        $(this).css("border-color", "#d8dadc");
                    });
                }
            });
        } else {
            // If no checkbox is checked in other_working_here, validate other_end_date fields
            $(".other_end_date").each(function () {
                if ($(this).val() === "") {
                    $(this).css("border-color", "red");
                    isValid = false;
                    // $(this).next('svg').focus();
                    if (!focusElm) {
                        focusElm = $(this).next("svg");
                    }
                } else {
                    $(this).css("border-color", "#d8dadc");
                }
            });
        }
    }

    var resume_value = document.getElementById("resume_value").value;

    var myFile = document.getElementById("myFile").value;
    var ext = myFile.split(".").pop().toLowerCase();
    if (resume_value == "") {
        if (myFile == "") {
            // $('#resume_error').text('Please Select Your File');
            $(".resumefile").css("border-color", "red");
            // document.getElementById("myFile").focus();
            $("#resume_error").text("");
            isValid = false;
            if (!focusElm) {
                focusElm = document.getElementById("myFile");
            }
            if (!focusElm) {
                focusElm = $(this).next("input");
            }
        } else if (myFile != "") {
            if ($.inArray(ext, ["doc", "docx", "ppt", "pdf"]) == -1) {
                $(".resumefile").css("border-color", "red");
                $("#resume_error").text(
                    "The file format is invalid. Please upload a file in one of the supported formats"
                );
                document.getElementById("myFile").value = "";
                // document.getElementById("myFile").focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = document.getElementById("myFile");
                }
                if (!focusElm) {
                    focusElm = $(this).next("input");
                }
            }
        }
    } else {
        if ($("#resume_name").val() != "") {
            var ext = $("#resume_name").val().split(".").pop().toLowerCase();
            if ($.inArray(ext, ["doc", "docx", "ppt", "pdf"]) == -1) {
                $(".resumefile").css("border-color", "red");
                $("#resume_error").text(
                    "The file format is invalid. Please upload a file in one of the supported formats"
                );
                document.getElementById("myFile").value = "";
                // document.getElementById("myFile").focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = document.getElementById("myFile");
                }
            }
        }
    }

    if (myFile != "") {
        var selectedFile = $("#myFile")[0].files[0];
        var fileSizeInBytes = selectedFile.size;
        const fileSizeInKB = fileSizeInBytes / 1024;

        // if (Math.round(fileSizeInKB) > 151) {
        if (Math.round(fileSizeInKB) >= 2048) {
            $(".resumefile").addClass("brederror").css("border-color", "red");
            $("#resume_error").text("File size exceeds the maximum limit.");
            isValid = false;
            if (!focusElm) {
                focusElm = $("#myFile");
            }
        } else {
            // File size is within the limit
            $(".resumefile").removeClass("brederror").css("border-color", "");
            $("#resume_error").text("");
        }
    }

    var full_name = $(".full_name");
    var email = $(".email");
    var phone = $(".phone");
    var reference_visible = $(".reference_visible");

    if (
        full_name.filter(function () {
            return $(this).val().length > 0;
        }).length == full_name.length &&
        email.filter(function () {
            return $(this).val().length > 0;
        }).length == email.length &&
        phone.filter(function () {
            return $(this).val().length > 0;
        }).length == phone.length
    ) {
        full_name.each(function () {
            $(this).css("border-color", "#d8dadc");
        });

        email.each(function () {
            var emailfilter =
                /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i;
            var regemailid = emailfilter.test($(this).val());
            if (regemailid == false) {
                $(this)
                    .closest(".references-form-input")
                    .find(".email_error")
                    .text("Please enter valid email address");
                $(this).css("border-color", "red");
                // $(this).focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this)
                    .closest(".references-form-input")
                    .find(".email_error")
                    .text("");
                $(this).css("border-color", "#d8dadc");
            }
        });
        phone.each(function () {
            if ($(this).val().length !== 10) {
                $(this)
                    .closest(".references-form-input")
                    .find(".phone_error")
                    .text("Phone number must be 10 digits");
                $(this).css("border-color", "red");
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this)
                    .closest(".references-form-input")
                    .find(".phone_error")
                    .text("");
                $(this).css("border-color", "#d8dadc");
            }
        });
    } else {
        full_name.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                // $(this).focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                $(this).css("border-color", "#d8dadc");
            }
        });

        email.each(function () {
            if ($(this).val() === "") {
                // $(this).focus();
                $(this).css("border-color", "red");
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                var emailfilter =
                    /^\w+[\+\.\w-]*@([\w-]+\.)*\w+[\w-]*\.([a-z]{2,4}|\d+)$/i;
                var regemailid = emailfilter.test($(this).val());
                if (regemailid == false) {
                    $(this)
                        .closest(".references-form-input")
                        .find(".email_error")
                        .text("Please enter valid email address");
                    $(this).css("border-color", "red");
                    // $(this).focus();
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this);
                    }
                } else {
                    $(this)
                        .closest(".references-form-input")
                        .find(".email_error")
                        .text("");
                    $(this).css("border-color", "#d8dadc");
                }
            }
        });

        phone.each(function () {
            if ($(this).val() === "") {
                $(this).css("border-color", "red");
                // $(this).focus();
                isValid = false;
                if (!focusElm) {
                    focusElm = $(this);
                }
            } else {
                if ($(this).val().length !== 10) {
                    $(this)
                        .closest(".references-form-input")
                        .find(".phone_error")
                        .text("Phone number must be 10 digits");
                    $(this).css("border-color", "red");
                    isValid = false;
                    if (!focusElm) {
                        focusElm = $(this);
                    }
                } else {
                    $(this)
                        .closest(".references-form-input")
                        .find(".phone_error")
                        .text("");
                    $(this).css("border-color", "#d8dadc");
                }
            }
        });
    }

    if (!isValid && focusElm && typeof focusElm.focus === "function") {
        focusElm.focus();
        return false;
    }

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var url = APP_URL + "/k12connections/submitSecondStepInstructor";
    var formData = new FormData($("#secondstep")[0]);

    formData.append(
        "valid_till_checkbox_values",
        JSON.stringify(Validtillcheckboxvalues)
    );
    formData.append(
        "visibletoschoolcheckboxes",
        JSON.stringify(visibletoschoolcheckboxes)
    );
    formData.append(
        "valid_till_select_dataes",
        JSON.stringify(valid_till_select_dataes)
    );

    $.ajax({
        type: "POST",
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            _this.innerHTML = loading;
        },
        success: function (data) {
            if (typeof callback != "undefined") {
                callback(data);
            }
            if (data.success == true) {
                if (type == "save_continue") {
                    $("#step3-tab").removeClass("disableClick");
                    _this.innerHTML = "Save &amp; Continue";
                    window.scrollTo({ top: 0, behavior: "smooth" });
                } else {
                    _this.innerHTML = "Save";
                    alertify.success(data.message);
                }
            } else {
                if (type == "save_continue") {
                    _this.innerHTML = "Save &amp; Continue";
                } else {
                    _this.innerHTML = "Save";
                }
                alertify.error(data.message);
            }
        },
        error: function (error) {
            window.scrollTo({ top: 0, behavior: "smooth" });
            _this.innerHTML = "Save &amp; Continue";
        },
    });
    return isValid;
}

function newOnboardingSaveThiredStep(type, _this, callback) {
    var isValid = true;

    let focusElm = "";
    const checkbox = $("#curriculumOne");

    if (!checkbox.is(":checked")) {
        checkbox.addClass("error-border");
        focusElm = $("#curriculumOne");
        isValid = false;
    } else {
        checkbox.removeClass("error-border");
    }
    var selectedArray =
        '<option value="" data-value="">Select Subject</option>';
    var arraysubject = [];
    $(".displayblock .selectRound").each(function () {
        var value = $(this).find("option").filter(":selected").val();
        var text = $(this).find("option").filter(":selected").text();
        var datavalue = $(this)
            .find("option")
            .filter(":selected")
            .attr("data-value");
        if (value == "Other") {
            var othervalue = $(this).attr("data-id");
            var othersubj = $(".subject_other_" + othervalue).val();
            arraysubject.push({
                value: value,
                "data-value": datavalue,
                text: text + ":" + othersubj,
                "data-id": "",
            });
            selectedArray +=
                '<option value="' +
                value +
                '" data-value="' +
                datavalue +
                '">' +
                text +
                ": " +
                othersubj +
                "</option>";
        } else {
            var othervalue = $(this).attr("data-id");
            var subsubjval = $(this)
                .parents(".displayblock")
                .find(".subsubject" + othervalue)
                .find("option")
                .filter(":selected")
                .val();
            var subsubj = $(this)
                .parents(".displayblock")
                .find(".subsubject" + othervalue)
                .find("option")
                .filter(":selected")
                .text();
            selectedArray +=
                '<option value="' +
                value +
                '" data-value="' +
                datavalue +
                '">' +
                text +
                ": " +
                subsubj +
                "</option>";
            arraysubject.push({
                value: value,
                "data-value": datavalue,
                text: text + ": " + subsubj,
                "data-id": subsubjval,
            });
        }
    });

    $("#subject_data_teaching").html(selectedArray);

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var select_button_text = $("#i_prefer_to_teach option:selected")
        .toArray()
        .map((item) => item.value);

    if (select_button_text.length == "0") {
        // $('#i_prefer_to_teach_error').html('Please Enter I Prefer To Teach');
        $(".fluid").css("border-color", "red");
        document.getElementById("i_prefer_to_teach").value = "";

        isValid = false;
        if (!focusElm) {
            focusElm = document.getElementById("i_prefer_to_teach");
        }
    } else {
        $("#i_prefer_to_teach_error").html("");
        $(".fluid").css("border-color", "#d8dadc");
    }
    // if (select_button_text.length == "0") {
    //     $('#i_prefer_to_teach').next().children().children().css("border-color", "red");
    //     document.getElementById('i_prefer_to_teach').focus();
    // }

    var i_prefer_to_teach = $("#i_prefer_to_teach").val();

    if (i_prefer_to_teach == "") {
        $("#i_prefer_to_teach")
            .siblings(".select2-container")
            .find(".select2-selection")
            .css("border-color", "red");
        $("#i_prefer_to_teach").focus();
        isValid = false;
        if (!focusElm) {
            focusElm = $("#i_prefer_to_teach");
        }
    } else {
        $("#i_prefer_to_teach")
            .siblings(".select2-container")
            .find(".select2-selection")
            .css("border-color", "#d8dadc");
    }

    var subone = $("#subject_0").val(); // Use jQuery here for consistency
    if (!subone || subone.trim() === "") {
        $("#subject_0")
            .next(".select2-container")
            .find(".select2-selection")
            .css("border-color", "red");

        if (!focusElm) {
            focusElm = $("#subject_0");
        }
    } else {
        $("#subject_0")
            .next(".select2-container")
            .find(".select2-selection")
            .css("border-color", "");
    }

    if ($(".displayblock")[0]) {
        var onlinerange1 = false;
        $(".displayblock .selectRound").each(function () {
            if (
                $(this).find("option").filter(":selected").val() == "" ||
                $(this).find("option").filter(":selected").val() == "0"
            ) {
                onlinerange1 = false;
                if (!focusElm) {
                    focusElm = $(this).find("option");
                }

                $(this).next().children().children().css("border-color", "red");
            } else {
                if (
                    $(this).find("option").filter(":selected").val() == "Other"
                ) {
                    var o = $(this).attr("data-id");
                    $(".subject_other_" + o).css("border-color", "red");

                    onlinerange1 = false;
                    if (!focusElm) {
                        focusElm = $(this).find("option");
                    }

                    if ($(".subject_other_" + o).val() != "") {
                        onlinerange1 = true;
                        $(".subject_other_" + o).css("border-color", "");
                    }
                } else {
                    onlinerange1 = true;
                    $(".displayblock .subsubject_slect").each(function () {
                        var subselectoption = $(this)
                            .find("option")
                            .filter(":selected")
                            .toArray()
                            .map((item) => item.value);

                        if ($(this).val() == "") {
                            $(this).css("border-color", "red");
                            $(this)
                                .next()
                                .children()
                                .children()
                                .css("border-color", "red");
                            onlinerange1 = false;
                            if (!focusElm) {
                                focusElm = $(this).find("option");
                            }
                        } else {
                            $(this).css("border-color", "");
                            $(this)
                                .next()
                                .children()
                                .children()
                                .css("border-color", "");

                            onlinerange1 = true;
                        }
                    });

                    $(this).removeAttr("style");
                    // onlinerange1 = true;
                }
            }
        });
    } else {
        if ($("#subone").find("option").filter(":selected").val() == "Other") {
            var p = $("#subone").attr("data-id");
            if ($(".subject_other_" + p).val() == "") {
                $(".subject_other_1").css("border-color", "red");
                onlinerange1 = false;
                if (!focusElm) {
                    focusElm = $(this).find("option");
                }
            } else {
                onlinerange1 = true;
            }
        } else {
            var subselectoptiond = $(".subsubject_slect")
                .find("option")
                .filter(":selected")
                .val();

            if (subselectoptiond == "") {
                $(".subsubject_slect").css("border-color", "red");
                //$('.subsubject_slect').next().children().children().css("border-color", "red");
                onlinerange1 = false;
                if (!focusElm) {
                    focusElm = $(this).find("option");
                }
            } else {
                $(".subsubject_slect").css("border-color", "");
                onlinerange1 = true;
            }
        }
    }

    var onlinerdange1 = false;
    $(".subsubject_slect").each(function () {
        var subselectoption = $(this).find("option").filter(":selected").val();
        // .toArray().map(item => item.value);

        if (subselectoption == "") {
            $(this).css("border-color", "red");
            //$(this).next().children().children().css("border-color", "red");
            onlinerdange1 = false;
        } else {
            $(this).css("border-color", "");
            $(this).next().children().children().css("border-color", "");

            onlinerdange1 = true;
        }
    });

    if ($("#language_teach_that_i_teach").val() == "") {
        $("#language_teach_that_i_teach")
            .siblings(".select2-container")
            .find(".select2-selection")
            .css("border-color", "red");
        // $("#language_teach_that_i_teach").focus();
        isValid = false;
        if (!focusElm) {
            focusElm = $("#language_teach_that_i_teach");
        }
    } else {
        $("#language_teach_that_i_teach")
            .siblings(".select2-container")
            .find(".select2-selection")
            .css("border-color", "#d8dadc");
    }

    if ($("#language_teach_that_i_teach").val().includes("Other")) {
        if ($("#other_language").val() == "") {
            $("#other_language").css("border-color", "red");
            isValid = false;
            // $('#other_language').focus();
            if (!focusElm) {
                focusElm = $("#other_language");
            }
        } else {
            $("#other_language").css("border-color", "");
        }
    }

    if ($("#program_type").val() == "") {
        $("#program_type")
            .siblings(".select2-container")
            .find(".select2-selection")
            .css("border-color", "red");
        // $("#program_type").focus();
        isValid = false;
        if (!focusElm) {
            focusElm = $("#program_type");
        }
    } else {
        $("#program_type")
            .siblings(".select2-container")
            .find(".select2-selection")
            .css("border-color", "#d8dadc");
    }

    if ($("#program_type").val().includes("Other")) {
        if ($("#other_program").val() == "") {
            $("#other_program").css("border-color", "red");
            isValid = false;
            if (!focusElm) {
                focusElm = $("#other_program");
            }
            // $('#other_program').focus();
        } else {
            $("#other_program").css("border-color", "");
        }
    }

    if (
        $("#formatone").is(":checked") == false &&
        $("#formatwo").is(":checked") == false &&
        $("#formathree").is(":checked") == false
    ) {
        $("#formatone").closest(".form__check").addClass("error");
        $("#formatwo").closest(".form__check").addClass("error");
        $("#formathree").closest(".form__check").addClass("error");
        isValid = false;
        if (!focusElm) {
            focusElm = $("#formatone");
        }
    } else {
        $("#formatone").closest(".form__check").removeClass("error");
        $("#formatwo").closest(".form__check").removeClass("error");
        $("#formathree").closest(".form__check").removeClass("error");
        if ($("#formatone").is(":checked")) {
            if ($("#internet_connection").is(":checked")) {
                $("#internet_connection")
                    .closest(".form__check")
                    .removeClass("error");
            } else {
                isValid = false;
                if (!focusElm) {
                    focusElm = $("#internet_connection");
                }
                $("#internet_connection")
                    .closest(".form__check")
                    .addClass("error");
            }

            if ($("#work_from_home_setup").is(":checked")) {
                $("#work_from_home_setup")
                    .closest(".form__check")
                    .removeClass("error");
            } else {
                isValid = false;
                if (!focusElm) {
                    focusElm = $("#work_from_home_setup");
                }
                $("#work_from_home_setup")
                    .closest(".form__check")
                    .addClass("error");
            }
        }
    }

    // $(".form__check input").on("change", function () {
    //     if (
    //         $("#formatone").is(":checked") == false &&
    //         $("#formatwo").is(":checked") == false &&
    //         $("#formathree").is(":checked") == false
    //     ) {
    //         $(".form__check").addClass("error");
    //         isValid = false;
    //         if (!focusElm) {
    //             focusElm = $("#work_from_home_setup").closest(".form__check");
    //         }
    //     } else {
    //         $(".form__check").removeClass("error");
    //     }
    // });

    var format = document.querySelector('input[name="format[]"]:checked');
    var formatElm = document.querySelector(
        'input[type="checkbox"][name="format[]"]'
    );

    if (!format && formatElm) {
        isValid = false;
        if (!focusElm) {
            focusElm = document.querySelector(
                'input[type="checkbox"][name="format[]"]'
            );
        }
    } else {
        $("#formatone").css("outline-color", "");
        $("#formatone").css("outline-style", "");
        $("#formatone").css("outline-width", "");
        $("#formatone").css("height", "");

        $("#formatwo").css("outline-color", "");
        $("#formatwo").css("outline-style", "");
        $("#formatwo").css("outline-width", "");
        $("#formatwo").css("height", "18px");

        $("#formathree").css("outline-color", "");
        $("#formathree").css("outline-style", "");
        $("#formathree").css("outline-width", "");
        $("#formathree").css("height", "");
        $("#formathree").css("outline-color", "");
        $("#format_error").html("");
    }

    var subj = $("#subject_data_teaching").html();
    if ($("#select_subject").val() == "yes") {
        $("#subject1").html(subj);
    }

    if (
        $("#formatone").is(":checked") &&
        $("#compensation_online").val() === ""
    ) {
        $("#compensation_online").css("border-color", "red");
        // $('#compensation_online').focus();
        isValid = false;
        if (!focusElm) {
            focusElm = $("#compensation_online");
        }
    } else {
        $("#compensation_online").css("border-color", "#d8dadc");
    }

    if (
        $("#formatwo").is(":checked") &&
        $("#compensation_in_person").val() == ""
    ) {
        $("#compensation_in_person").css("border-color", "red");
        // $('#compensation_in_person').focus();
        isValid = false;
        if (!focusElm) {
            focusElm = $("#compensation_in_person");
        }
    } else {
        $("#compensation_in_person").css("border-color", "#d8dadc");
    }

    if ($("#formathree").is(":checked")) {
        if (
            $("#compensation_online").val() == "" &&
            $("#compensation_in_person").val() == ""
        ) {
            $("#compensation_online").css("border-color", "red");
            $("#compensation_online").focus();
            $("#compensation_in_person").css("border-color", "red");
            // $('#compensation_in_person').focus();
            isValid = false;
            if (!focusElm) {
                focusElm = $("#compensation_in_person");
            }
        } else if ($("#compensation_online").val() == "") {
            $("#compensation_online").css("border-color", "red");
            // $('#compensation_online').focus();
            isValid = false;
            if (!focusElm) {
                focusElm = $("#compensation_online");
            }
        } else if ($("#compensation_in_person").val() == "") {
            $("#compensation_in_person").css("border-color", "red");
            // $('#compensation_in_person').focus();
            isValid = false;
            if (!focusElm) {
                focusElm = $("#compensation_in_person");
            }
        } else {
            $("#compensation_online").css("border-color", "#d8dadc");
            $("#compensation_in_person").css("border-color", "#d8dadc");
        }
    }

    // if ($('#curriculumone').is(':checked') === false && $('#curriculumtwo').is(':checked') === false) {
    //     $('#curriculumone').closest('.curriculum').find(".form__check").addClass('error');
    //     $('#curriculumtwo').closest('.curriculum').find(".form__check").addClass('error');
    //     isValid = false;
    // } else {
    //     $('#curriculumone').closest('.curriculum').find(".form__check").removeClass('error');
    //     $('#curriculumtwo').closest('.curriculum').find(".form__check").removeClass('error');
    // }

    $(".form__check input").on("change", function () {
        // if (
        //     $("#curriculumone").is(":checked") == false &&
        //     $("#curriculumtwo").is(":checked") == false
        // ) {
        //     $('.curriculum').find(".form__check").addClass("error");
        //     isValid = false;
        // } else {
        //     $('.curriculum').find(".form__check").removeClass("error");
        // }

        if (
            $("#formatone").is(":checked") == false &&
            $("#formatwo").is(":checked") == false &&
            $("#formathree").is(":checked") == false
        ) {
            $(".format").find(".form__check").addClass("error");
            isValid = false;
            if (!focusElm) {
                focusElm = $(".format");
            }
        } else {
            // $('.format').find(".form__check").removeClass("error");
            if ($("#internet_connection").is(":checked")) {
                $("#internet_connection")
                    .closest(".form__check")
                    .removeClass("error");
            } else {
                isValid = false;
                if (!focusElm) {
                    focusElm = $("#internet_connection");
                }
                $("#internet_connection")
                    .closest(".form__check")
                    .addClass("error");
            }

            if ($("#work_from_home_setup").is(":checked")) {
                $("#work_from_home_setup")
                    .closest(".form__check")
                    .removeClass("error");
            } else {
                isValid = false;
                if (!focusElm) {
                    focusElm = $("#work_from_home_setup");
                }
                $("#work_from_home_setup")
                    .closest(".form__check")
                    .addClass("error");
            }
        }
    });

    // if ($(".curriculum_radio").is(":checked") == false) {
    //     $(".curriculum_radio").css('border-color', 'red');
    //     focusElm = $(".curriculum_radio");
    //     isValid = false;
    // } else {
    //     $(".curriculum_radio").css('border-color', '');
    // }

    if ($("#curriculumThree").is(":checked") == false) {
        $("#curriculumThree").closest(".form__check").addClass("error");
        focusElm = $("#curriculumThree");
        isValid = false;
    } else {
        $("#curriculumThree").closest(".form__check").removeClass("error");
    }

    if (!isValid || !onlinerange1) {
        if (focusElm) {
            if (focusElm.is("option")) {
                focusElm.parent().focus();
            } else {
                focusElm.focus();
            }
        }
        return false;
    }

    // console.log($('#thirdstepid').serialize())
    // return false;
    if (onlinerange1 == true && isValid == true) {
        requiredFieldArray.splice(0, 5);
        $.ajaxSetup({
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
        });
        var url = APP_URL + "/k12connections/submitThirdStepInstructor";
        $.ajax({
            type: "POST",
            url: url,
            data: $("#thirdstepid").serialize(),
            dataType: "json",
            beforeSend: function () {
                _this.innerHTML = loading;
            },
            success: function (data) {
                if (typeof callback != "undefined") {
                    callback(data);
                }
                if (data.success == true) {
                    if (type == "save_continue") {
                        var arraypush = [];
                        $('input[name="format[]"]').each(function (idx, el) {
                            if ($(el).is(":checked")) {
                                var selectedValue = $(el).val();
                                arraypush.push(selectedValue);
                            }
                        });

                        if (
                            jQuery.inArray("online", arraypush) != -1 &&
                            jQuery.inArray("in-person", arraypush) != -1 &&
                            jQuery.inArray("hybrid", arraypush) != -1
                        ) {
                            $(".onlines").removeClass("displaynones");
                            $(".inperson").removeClass("displaynones");
                            $("#formate_value").val("3");
                        } else if (
                            jQuery.inArray("online", arraypush) != -1 &&
                            jQuery.inArray("in-person", arraypush) != -1 &&
                            jQuery.inArray("hybrid", arraypush) == -1
                        ) {
                            $(".onlines").removeClass("displaynones");
                            $(".inperson").removeClass("displaynones");
                            $("#formate_value").val("3");
                        } else if (
                            jQuery.inArray("online", arraypush) != -1 &&
                            jQuery.inArray("in-person", arraypush) == -1 &&
                            jQuery.inArray("hybrid", arraypush) == -1
                        ) {
                            $(".onlines").removeClass("displaynones");
                            $(".inperson").addClass("displaynones");
                            $("#formate_value").val("1");
                        } else if (
                            jQuery.inArray("online", arraypush) == -1 &&
                            jQuery.inArray("in-person", arraypush) != -1 &&
                            jQuery.inArray("hybrid", arraypush) == -1
                        ) {
                            $(".inperson").removeClass("displaynones");
                            $(".onlines").addClass("displaynones");
                            $("#formate_value").val("2");
                        } else if (
                            jQuery.inArray("online", arraypush) == -1 &&
                            jQuery.inArray("in-person", arraypush) != -1 &&
                            jQuery.inArray("hybrid", arraypush) != -1
                        ) {
                            $(".onlines").removeClass("displaynones");
                            $(".inperson").removeClass("displaynones");
                            $("#formate_value").val("3");
                        } else if (
                            jQuery.inArray("online", arraypush) != -1 &&
                            jQuery.inArray("in-person", arraypush) == -1 &&
                            jQuery.inArray("hybrid", arraypush) != -1
                        ) {
                            $(".onlines").removeClass("displaynones");
                            $(".inperson").removeClass("displaynones");
                            $("#formate_value").val("3");
                        } else if (
                            jQuery.inArray("online", arraypush) == -1 &&
                            jQuery.inArray("in-person", arraypush) == -1 &&
                            jQuery.inArray("hybrid", arraypush) != -1
                        ) {
                            $(".onlines").removeClass("displaynones");
                            $(".inperson").removeClass("displaynones");
                            $("#formate_value").val("3");
                        }

                        $("#teach").val(arraypush);

                        window.scrollTo({ top: 0, behavior: "smooth" });
                        _this.innerHTML = "Save &amp; Continue";
                    } else {
                        _this.innerHTML = "Save";
                        alertify.success(data.message);
                        // window.location.href = APP_URL + "/submit";
                    }
                } else {
                    if (type == "save_continue") {
                        _this.innerHTML = "Save &amp; Continue";
                    } else {
                        _this.innerHTML = "Save";
                    }
                    alertify.error(data.message);
                }
            },
            error: function (error) {
                window.scrollTo({ top: 0, behavior: "smooth" });
                _this.innerHTML = "Save &amp; Continue";
            },
        });
    }
    // teachingsubject(arraysubject);
    return true;
}

function newOnboardingSaveFourthstep(type, _this) {
    var isValid = true;
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";

    var formate_value = document.getElementById("formate_value").value;

    if (formate_value == "1" || formate_value == "3") {
        var online_class_price =
            document.getElementById("online_class_price").value;
        if (online_class_price == "") {
            $("#online_class_price").css("border-color", "red");
        }
    }
    if (formate_value == "2" || formate_value == "3") {
        var in_person_classes_price = document.getElementById(
            "in_person_classes_price"
        ).value;
        if (in_person_classes_price == "") {
            $("#in_person_classes_price").css("border-color", "red");
        }
    }

    var formate_value = document.getElementById("formate_value").value;

    if (formate_value == "1" || formate_value == "3") {
        // if (checked == true) {
        var online_class_price =
            document.getElementById("online_class_price").value;
        if (online_class_price.length == "") {
            $("#online_class_price").css("border-color", "red");
            //  $('#online_class_price_error').html('Please enter price');
            document.getElementsByName("online_class_price")[0].focus();
            isValid = false;
        } else {
            $("#online_class_price_error").html("");
        }
    } else {
        $("#online_class_price_error").html(" ");
    }

    if (formate_value == "2" || formate_value == "3") {
        var in_person_classes_price = document.getElementById(
            "in_person_classes_price"
        ).value;
        if (in_person_classes_price.length == "") {
            //  $('#in_person_classes_price_error').html('Please enter price');
            $("#in_person_classes_price").css("border-color", "red");
            document.getElementsByName("in_person_classes_price")[0].focus();
            isValid = false;
        } else {
            $("#in_person_classes_price_error").html("");
        }
    } else {
        $("#in_person_classes_price_error").html(" ");
    }

    if (!isValid) {
        return false;
    }

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    var url = APP_URL + "/k12connections/submitFourthStep";
    $.ajax({
        type: "POST",
        url: url,
        data: $("#fourthid").serialize(),
        dataType: "json",
        beforeSend: function () {
            _this.innerHTML = loading;
        },
        success: function (data) {
            // $("#fourthid")[0].reset();
            if (data.success == true) {
                if (type == "save_continue") {
                    _this.innerHTML = "Save &amp; Continue";
                    $("#step5-tab").removeClass("disableClick");

                    window.scrollTo({ top: 0, behavior: "smooth" });
                } else {
                    _this.innerHTML = "Save";
                    alertify.success(data.message);
                }
            } else {
                if (type == "save_continue") {
                    _this.innerHTML = "Save &amp; Continue";
                } else {
                    _this.innerHTML = "Save";
                }
                alertify.error(data.message);
            }
        },
        error: function (error) {
            window.scrollTo({ top: 0, behavior: "smooth" });
            _this.innerHTML = "Save &amp; Continue";
        },
    });
    return true;
}

function newOnboardingSaveFifthStep(type, _this, callback) {
    var isValid = true;
    let focusElm = "";
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";

    var profile_title = document.getElementById("profile_title").value;
    var profile_desc = document.getElementById("profile_desc").value;
    var tags_hidden = document.getElementById("tags_hidden").value;

    var imageData = $("#image_data").val();

    var ext = upload_image.split(".").pop().toLowerCase();

    if (profile_title == "") {
        $("#profile_title").css("border-color", "red");
        isValid = false;
        focusElm = $("#profile_title");

        if (profile_desc == "") {
            $("#profile_desc").css("border-color", "red");
            isValid = false;
            focusElm = $("#profile_desc");
        } else {
            $("#profile_desc").css("border-color", "#ced4da");
        }

        if (imageData == "") {
            if (upload_image == "") {
                $("#image_preview").css("border-color", "red");
                $("#profile_image_error").text("Please Upload profile image");
                isValid = false;
                focusElm = document.getElementById("image_preview");
            } else if (upload_image != "") {
                if ($.inArray(ext, ["jpg", "png", "jpeg"]) == -1) {
                    $(".image_preview").css("border-color", "red");
                    $("#profile_image_error").text(
                        "Invalid file type. Allowed: .jpg, .png, .jpeg,"
                    );
                    document.getElementById("profile_image").value = "";
                    isValid = false;
                    focusElm = document.getElementById("profile_image");
                } else {
                    $("#profile_image_error").text("");
                }
            }
        }
    } else if (profile_desc == "") {
        $("#profile_desc").css("border-color", "red");
        document.getElementById("profile_desc").focus();
        isValid = false;
        focusElm = document.getElementById("profile_image");
    } else if (tags_hidden == "") {
        $("#profile_tags").css("border-color", "red");
        // $('#profile_tags').focus();
        isValid = false;
        focusElm = $("#profile_tags");
    } else {
        if (profile_title != "") {
            $("#profile_title").css("border-color", "#ced4da");
        }
        if (profile_desc != "") {
            $("#profile_desc").css("border-color", "#ced4da");
        }
        if (imageData == "") {
            if (upload_image != "") {
                if ($.inArray(ext, ["jpg", "png", "jpeg"]) == -1) {
                    $(".image_preview").css("border-color", "red");
                    $("#profile_image_error").text(
                        "Invalid file type. Allowed: .jpg, .png, .jpeg"
                    );
                    document.getElementById("profile_image").value = "";
                    isValid = false;
                    focusElm = document.getElementById("profile_image");
                } else {
                    $("#profile_image_error").text("");
                }
            } else {
                $("#profile_image_error").text("Please Upload profile image");
                isValid = false;
                focusElm = document.getElementById("profile_image");
            }
        }
    }

    if (tags_hidden == "") {
        $("#profile_tags").css("border-color", "red");
        $("#profile_tags").focus();
    } else {
        $("#profile_tags").css("border-color", "#ced4da");
        $(".tags_err").text("");
    }

    if (!isValid) {
        focusElm.focus();
        return false;
    }

    $("#progressContainer").hide();
    $("#uploadStatus").hide();

    var url = APP_URL + "/k12connections/submitFifthStepInstructor";
    var formData = new FormData($("#fifthid")[0]);

    $.ajax({
        type: "POST",
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            _this.innerHTML = loading;
        },
        success: function (data) {
            if (typeof callback != "undefined") {
                callback(data);
            }
            if (data.success == true) {
                if (type == "save_continue") {
                    _this.innerHTML = "Save & Continue";

                    window.scrollTo({ top: 0, behavior: "smooth" });
                } else {
                    _this.innerHTML = "Save & Continue";
                    alertify.success(data.message);
                }
            } else {
                if (type == "save_continue") {
                    _this.innerHTML = "Save & Continue";
                } else {
                    _this.innerHTML = "Save & Continue";
                }
                // alertify.error('Incorrect Email');
            }
        },
        error: function (error) {
            window.scrollTo({ top: 0, behavior: "smooth" });
            _this.innerHTML = "Save & Continue";
        },
    });
    return true;
}

function savefiveclassroom(type, _this) {
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var classroomfile =
        document.getElementsByClassName("classroomfile")[0].value;
    if (classroomfile == "") {
        $(".cfile").css("border-color", "red");
    }
    var class_value = document.getElementById("class_value").value;
    var classroomfile =
        document.getElementsByClassName("classroomfile")[0].value;
    var ext = classroomfile.split(".").pop().toLowerCase();
    if (class_value == "") {
        if (classroomfile == "") {
            $(".cfile").css("border-color", "red");
            //$('#classroomfile_error').text('Please Select Your Video');
            document.getElementsByClassName("classroomfile")[0].focus();
            return false;
        } else if (classroomfile != "")
            ["pdf", "doc", "docx", "xls", "png", "jpg", "jpeg"];
        {
            if (
                $.inArray(ext, ["m4v", "avi", "mpg", "mp4", "webm", "mov"]) ==
                -1
            ) {
                $(".cfile").css("border-color", "red");
                $("#classroomfile_error").text(
                    "Wrong File Format!..Please Select Right Format"
                );
                document.getElementsByClassName("classroomfile")[0].value = "";
                document.getElementsByClassName("classroomfile")[0].focus();
                return false;
            }
        }
    } else {
        if (classroomfile != "") {
            if (
                $.inArray(ext, ["m4v", "avi", "mpg", "mp4", "webm", "mov"]) ==
                -1
            ) {
                $(".cfile").css("border-color", "red");
                $("#classroomfile_error").text(
                    "Wrong File Format!..Please Select Right Format"
                );
                document.getElementsByClassName("classroomfile")[0].value = "";
                document.getElementsByClassName("classroomfile")[0].focus();
                return false;
            }
        } else {
            $(".cfile").css("border-color", "");
        }
    }

    if (classroomfile != "") {
        var selectedFile = $(".cfile")[0].files[0];
        var fileSizeInBytes = selectedFile.size;
        const fileSizeInKB = fileSizeInBytes / (1024 * 1024);

        if (Math.round(fileSizeInKB) > 21) {
            $(".cfile").addClass("brederror").css("border-color", "red");
            $("#classroomfile_error").text(
                "File is too large, Please upload maximum file size is 20 MB."
            );
            return false;
        } else {
            // File size is within the limit
            $(".cfile").removeClass("brederror").css("border-color", "");
            $("#classroomfile_error").text("");
        }
    }
    var $this = $(this);

    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";
    var url = APP_URL + "/k12connections/submit_classrorm";
    var formData = new FormData($("#fivethirddid")[0]);

    $(".stepdisable").addClass("disableClick");
    $(".sintro").addClass("disableClick");
    $(".teach").addClass("disableClick");
    $(".previous").addClass("disableClick");
    $(".nextsave").addClass("disableClick");
    $(".savefiveclassroom").addClass("disableClick");
    $.ajax({
        type: "POST",
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            $(".savefiveclassroom").prop("disabled", true);
            _this.innerHTML = loading;
        },
        success: function (data) {
            $(".quiz").removeClass("disableClick");
            $(".stepdisable").removeClass("disableClick");
            $(".sintro").removeClass("disableClick");
            $(".teach").removeClass("disableClick");
            $(".previous").removeClass("disableClick");
            $(".nextsave").removeClass("disableClick");
            $(".savefiveclassroom").removeClass("disableClick");
            if (data.success == true) {
                if (type == "save_continue") {
                    _this.innerHTML = "Save &amp; Continue";
                    $("#quiz").click();
                    window.scrollTo({ top: 0, behavior: "smooth" });
                } else {
                    _this.innerHTML = "Save";
                }
                $(".savefiveclassroom").prop("disabled", false);
            } else {
                if (type == "save_continue") {
                    _this.innerHTML = "Save &amp; Continue";
                } else {
                    _this.innerHTML = "Save";
                }
                $(".savefiveclassroom").prop("disabled", false);
                alertify.error("Incorrect Email");
            }
        },
    });
}

function newOnboardingSumbitQuiz(type, _this) {
    var isValid = true;
    let missingResponse = null;
    var currentTab = document.querySelector(
        "#mySixTabContent .tab-pane.active"
    );

    if (currentTab.id === "free_response") {
        $(".assment_que").each(function (index) {
            if (
                $("textarea[name='answer" + index + "']")
                    .val()
                    .trim().length === 0
            ) {
                $("#answer-" + index).css("border-color", "red");
                isValid = false;
                if (missingResponse == null)
                    missingResponse = "#answer-" + index;
            } else {
                $("#answer-" + index).css("border-color", "#ced4da");
            }
        });

        if (isValid) {
            const quizTab = document.getElementById("quiz_btn");
            $(".free_response_dot ").empty();
            quizTab.click();
            window.scrollTo({ top: 0, behavior: "smooth" });
        } else {
            document
                .getElementById(missingResponse.replace("#", ""))
                .scrollIntoView();
        }
        return false;
    } else if (currentTab.id == "video_demonstration") {
        if (
            $("#video_loading_spinner").hasClass("d-none") &&
            $(".showRecording").hasClass("d-none")
        ) {
            isValid = false;
        }
        if (!isValid) {
            document.getElementById("recordVideo").style.border =
            "1px solid red";
            return;
        } else {
            isValid = false;
            window.scrollTo({ top: 0, behavior: "smooth" });
            $(".video_demonstration_dot").empty();
            $("#sample_lesson_btn").click();
        }
    } else if (currentTab.id == "sample_lesson") {
        if ($(".main_div_subject_lesson").children().length == 0) {
            isValid = false;
            $(".sample_lesson_error").text("Please add a Lesson");
            return;
        } else {
            $(".sample_lesson_error").empty();

            document.querySelectorAll(".required-field").forEach((field) => {
                let fieldIsValid = true;

                if (field.type === "file") {
                    const existingInput = field
                        .closest("label")
                        .querySelector(".existing_input");
                    const hasFileSelected = field.files.length > 0;
                    const existingValueNotEmpty =
                        existingInput && existingInput.value.trim() !== "";

                    fieldIsValid = hasFileSelected || existingValueNotEmpty;

                    const label = field.closest("label");
                    if (!fieldIsValid) {
                        label.style.border = "1px solid red";
                        isValid = false;
                    } else {
                        label.style.border = "1px solid #ccc";
                    }
                } else {
                    fieldIsValid = field.value.trim() !== "";
                    field.style.border = fieldIsValid
                        ? "1px solid #ccc"
                        : "1px solid red";
                    if (!fieldIsValid) {
                        isValid = false;
                    }
                }
            });

            // Scroll to the first red-bordered element
            const firstInvalid = document.querySelector(
                '[style*="1px solid red"]'
            );
            if (firstInvalid) {
                firstInvalid.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                });
            }

            if (isValid == false) {
                return;
            } else {
                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                            "content"
                        ),
                    },
                });

                const formElement = document.getElementById("samplelessonform");
                const formdata = new FormData(formElement);

                $(".sample_lesson_dot").empty();

                const url = APP_URL + "/k12connections/submit_sample_lesson";
                isValid = false;
                $("#free_response_btn").click();

                $.ajax({
                    type: "POST",
                    url: url,
                    data: formdata,
                    processData: false, // Required for FormData (don't process into query string)
                    contentType: false, // Required for FormData (don't set default content-type)
                    success: function (response) {
                        window.scrollTo({ top: 0, behavior: "smooth" });
                    },
                    error: function (xhr, status, error) {
                        console.error("Error:", error);
                        // Optional: show error feedback to user
                    },
                });
            }
        }
    }
    let lessonEmpty = false;
    if ($(".main_div_subject_lesson").children().length != 0) {
        $(".main_div_subject_lesson .ParentLessonDiv").each(function () {
            const subject = $(this)
                .find('select[name="Lessonsubject[]"]')
                .val()
                ?.trim();
            const description = $(this)
                .find('textarea[name="lessondescription[]"]')
                .val()
                ?.trim();
            const uploadCount = $(this).find(
                'input[name="lesson_upload[]"]'
            ).length;
            const subjectTextCount = $(this).find(
                ".lesson_subject_text_div"
            ).length;

            if (uploadCount !== subjectTextCount) {
                console.log(
                    "Mismatch in lesson uploads and subject text divs."
                );
                // You can set a flag or take action here
                lessonEmpty = true;
            }

            if (!subject || !description) {
                lessonEmpty = true;
                return false; // exit loop early
            }
        });
    }
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";

    if (isValid == false) return false;
    var url = APP_URL + "/k12connections/submit_quiz_instructor";
    let formData = $("#sixthid").serialize();
    formData += "&lessonEmpty=" + lessonEmpty;
    $.ajax({
        type: "POST",
        url: url,
        dataType: "json",
        data: formData,
        beforeSend: function () {
            _this.innerHTML = loading;
        },
        success: function (data) {
            _this.innerHTML = "Submit";

            if (data.success == true) {
                $("#AlertCompleteProfileModal").modal("show");
            } else {
                let pendingMap = {
                    "step-1": "Us Work Authorization",
                    "step-3": "Education & Experience",
                    "step-4": "Your Preferences",
                    "step-5": "Profile",
                    "step-6": "Assignment",
                };
                $("#incomplete-list").empty();

                let pendingSteps = data.pending;
                let uniqueSteps = new Set();
                pendingSteps.forEach((item) => {
                    let stepKey = item.split(":")[0];
                    if (pendingMap[stepKey] && !uniqueSteps.has(stepKey)) {
                        $("#incomplete-list").append(
                            `<li class="list_item" style="display:block; color: var(--contentColor); font-size: 16px;">
                                    ${pendingMap[stepKey]}
                                </li>`
                        );
                        uniqueSteps.add(stepKey);
                    }
                });

                $("#AlertIncompleteProfileModal").modal("show");
            }
        },
    });
    return true;
}

function newOnboardingsubmitAgreement(type, _this) {
    var isValid = true;
    var legal_first_name = $("#legal_first_name").val();
    var legal_last_name = $("#legal_last_name").val();
    var legal_address = $("#legal_address").val();
    var check1 = "";
    var check2 = "";
    var check3 = "";

    if ($("#check1").is(":checked") == true) {
        check1 = "yes";
    } else {
        $("#check1").css("border-color", "red");
        document.getElementById("check1").focus();
        $("#check_error").text("Please check the checkboxs");
        isValid = false;
    }

    if ($("#check2").is(":checked") == true) {
        check2 = "yes";
    } else {
        $("#check2").css("border-color", "red");
        document.getElementById("check2").focus();
        $("#check_error").text("Please check the checkboxs");
        isValid = false;
    }

    if ($("#check3").is(":checked") == true) {
        check3 = "yes";
    } else {
        $("#check3").css("border-color", "red");
        document.getElementById("check3").focus();
        $("#check_error").text("Please check the checkboxs");
        isValid = false;
    }

    if (legal_first_name == "") {
        $("#legal_first_name").css("border-color", "red");
        document.getElementById("legal_first_name").focus();
        isValid = false;
    } else {
        $("#legal_first_name").css("border-color", "#ced4da");
    }

    if (legal_last_name == "") {
        $("#legal_last_name").css("border-color", "red");
        document.getElementById("legal_last_name").focus();
        isValid = false;
    } else {
        $("#legal_last_name").css("border-color", "#ced4da");
    }

    if (legal_address == "") {
        $("#legal_address").css("border-color", "red");
        document.getElementById("legal_address").focus();
        isValid = false;
    } else {
        $("#legal_address").css("border-color", "#ced4da");
    }

    if (check1 != "" && check2 != "" && check3 != "") {
        $("#check_error").text("");
    }

    if (!isValid) {
        return false;
    }
    $("#skipTabButton").addClass("d-none");
    if (isValid == true) {
        // requiredFieldArray.splice(-2,1);

        var url = APP_URL + "/k12connections/submit_instructor_agreement";
        $.ajax({
            type: "POST",
            url: url,
            dataType: "json",
            data: $("#sevenid").serialize(),
            beforeSend: function () {
                _this.innerHTML = "Submit";
                // $this.prop("disabled", true);
            },
            success: function (data) {
                _this.innerHTML = "Submit";
                if (data.success == true) {
                    $("#AlertCompleteProfileModal").modal("show");
                } else {
                    if (data.isLater && data.isLater == 1) {
                        window.location.href = data.redirect;
                    } else {
                        let pendingSteps = data.pending;
                        let pendingMap = {
                            "step-1": "Us Work Authorization",
                            "step-3": "Education & Experience",
                            "step-4": "Your Preferences",
                            "step-5": "Profile",
                            "step-6": "Assessment:Quiz",
                        };
                        $("#incomplete-list").empty();
                        let uniqueSteps = new Set();
                        pendingSteps.forEach((item) => {
                            let stepKey = item.split(":")[0];
                            if (
                                pendingMap[stepKey] &&
                                !uniqueSteps.has(stepKey)
                            ) {
                                $("#incomplete-list").append(
                                    `<li class="list_item" style="display:block; color: var(--contentColor); font-size: 16px;">
                                        ${pendingMap[stepKey]}
                                    </li>`
                                );
                                uniqueSteps.add(stepKey); // Mark this step as added
                            }
                        });

                        $("#AlertIncompleteProfileModal").modal("show");
                    }
                }
            },
        });
    }
    return true;
}

$(document).on("change", ".certified_special_education", function () {
    var certi = $(this)
        .children("option:selected")
        .toArray()
        .map((item) => item.value);
    const agencyOther = $(this)
        .closest(".certification-list")
        .prev()
        .find(".credentialingAgencyOther");
    if (agencyOther.hasClass("invisible")) {
        agencyOther.removeClass("invisible");
        agencyOther.addClass("d-none");
    }
    if (jQuery.inArray("Other", certi) != -1) {
        if (agencyOther.hasClass("d-none")) {
            agencyOther.addClass("invisible");
            agencyOther.removeClass("d-none");
        }
        $(this)
            .closest(".login__form")
            .next()
            .find(".Certificationsother")
            .removeClass("d-none");
        $(this)
            .closest(".login__form")
            .next()
            .find(".Certificationsother")
            .find("input")
            .val("");
    } else {
        $(this)
            .closest(".login__form")
            .next()
            .find(".Certificationsother")
            .addClass("d-none");
        $(this)
            .closest(".login__form")
            .next()
            .find(".Certificationsother")
            .find("input")
            .val("");
    }
});

function newOnboardingsavefiveintro(type, _this) {
    var isValid = true;
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";

    var intro_value = document.getElementById("intro_value").value;
    var intro_file = document.getElementsByClassName("intro_file")[0].value;
    var ext = intro_file.split(".").pop().toLowerCase();
    if (intro_value == "") {
        if (intro_file == "") {
            // $('#intro_file_error').text('Please Select Your Video');
            $(".intro_files").css("border-color", "red");
            document.getElementsByClassName("intro_file")[0].focus();
            isValid = false;
        } else if (intro_file != "") {
            if (
                $.inArray(ext, ["m4v", "avi", "mpg", "mp4", "webm", "mov"]) ==
                -1
            ) {
                $(".intro_files").css("border-color", "red");
                // $('#intro_file_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("intro_file")[0].value = "";
                document.getElementsByClassName("intro_file").focus();
                isValid = false;
            }
        }
    } else {
        if (intro_file != "") {
            if (
                $.inArray(ext, ["m4v", "avi", "mpg", "mp4", "webm", "mov"]) ==
                -1
            ) {
                $(".intro_files").css("border-color", "red");
                // $('#intro_file_error').text('Wrong File Format!..Please Select Right Format');
                document.getElementsByClassName("intro_file")[0].value = "";
                // document.getElementsByClassName("intro_file").focus();
                isValid = false;
            }
        }
    }
    if (intro_file != "") {
        var selectedFile = $("#intro_file")[0].files[0];
        var fileSizeInBytes = selectedFile.size;
        const fileSizeInKB = fileSizeInBytes / (1024 * 1024);

        if (Math.round(fileSizeInKB) > 21) {
            // File size exceeds 50 KB

            $(".intro_file").addClass("brederror").css("border-color", "red");
            $("#intro_file_error").text(
                "File is too large, Please upload maximum file size is 20 MB."
            );
            isValid = false;
        } else {
            // File size is within the limit
            $(".intro_file").removeClass("brederror").css("border-color", "");
            $("#intro_file_error").text("");
        }
    }

    if (!isValid) {
        return false;
    }

    var url = APP_URL + "/k12connections/submit_intro";
    var formData = new FormData($("#fivestepid")[0]);

    $(".stepdisable").addClass("disableClick");
    $(".previous").addClass("disableClick");
    $(".nextsave").addClass("disableClick");

    $(".savefiveintro").addClass("disableClick");
    $.ajax({
        type: "POST",
        url: url,
        data: formData,
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        beforeSend: function () {
            _this.innerHTML = loading;
        },
        success: function (data) {
            $(".teach").removeClass("disableClick");
            $(".stepdisable").removeClass("disableClick");
            $(".previous").removeClass("disableClick");
            $(".nextsave").removeClass("disableClick");
            $(".savefiveintro").removeClass("disableClick");

            if (data.success == true) {
                if (type == "save_continue") {
                    _this.innerHTML = "Save &amp; Continue";

                    $("#teaching").click();
                    window.scrollTo({ top: 0, behavior: "smooth" });
                } else {
                    _this.innerHTML = "Save";
                    alertify.success(data.message);
                }

                $(".topic").css("padding-top", "16px !important");
            } else {
                if (type == "save_continue") {
                    _this.innerHTML = "Save &amp; Continue";
                } else {
                    _this.innerHTML = "Save";
                }
                // alertify.error('Incorrect Email');
            }
        },
    });
    return true;
}

//image upload for profile in step 5
let upload_image = "";
function showUploadedImage(event) {
    const fileInput = event.target;
    const previewContainer = document.getElementById("image_preview");
    if (!fileInput.files || fileInput.files.length === 0) {
        $("profile_image").val(upload_image);
        return;
    }

    if (fileInput.files && fileInput.files[0]) {
        const file = fileInput.files[0];
        const fileName = file.name;
        upload_image = file.name;
        const ext = fileName.split(".").pop().toLowerCase();
        if ($.inArray(ext, ["png", "jpg", "jpeg"]) == -1) {
            $("#profile_image_error").text(
                "The file format is invalid. Please upload a file in PNG, JPG, or JPEG format."
            );
            $("#btn-del").addClass("d-none");
            return;
        }
        const fileSizeMB = file.size / (1024 * 1024); // Convert bytes to MB

        if (fileSizeMB > 2) {
            $("#profile_image_error").text(
                "Please upload an image less than or equal to 1 MB in size."
            );
            $("#btn-del").addClass("d-none");
            return;
        }

        previewContainer.innerHTML = "";
        const reader = new FileReader();

        reader.onload = function (e) {
            const img = document.createElement("img");
            img.src = e.target.result;
            img.title = fileName;
            img.alt = fileName;
            previewContainer.appendChild(img);
        };

        reader.readAsDataURL(file);
        if ($("#delete_button").is(":visible")) {
            $("#delete_button").addClass("d-none");
        }
        $("#btn-del").removeClass("d-none");
        $("#profile_image_error").text("");
        $("#profile_image").attr("title", fileName);
    } else {
        previewContainer.innerHTML = `
            <svg width="50px" height="50px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="13" r="3" stroke="#1C274C" stroke-width="1.5" />
                <path d="M9.77778 21H14.2222C17.3433 21 18.9038 21 20.0248 20.2646C20.51 19.9462 20.9267 19.5371 21.251 19.0607C22 17.9601 22 16.4279 22 13.3636C22 10.2994 22 8.76721 21.251 7.6666C20.9267 7.19014 20.51 6.78104 20.0248 6.46268C19.3044 5.99013 18.4027 5.82123 17.022 5.76086C16.3631 5.76086 15.7959 5.27068 15.6667 4.63636C15.4728 3.68489 14.6219 3 13.6337 3H10.3663C9.37805 3 8.52715 3.68489 8.33333 4.63636C8.20412 5.27068 7.63685 5.76086 6.978 5.76086C5.59733 5.82123 4.69555 5.99013 3.97524 6.46268C3.48995 6.78104 3.07328 7.19014 2.74902 7.6666C2 8.76721 2 10.2994 2 13.3636C2 16.4279 2 17.9601 2.74902 19.0607C3.07328 19.5371 3.48995 19.9462 3.97524 20.2646C5.09624 21 6.65675 21 9.77778 21Z" stroke="#1C274C" stroke-width="1.5" />
                <path d="M19 10H18" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" />
            </svg>
        `;
        $("#btn-del").addClass("d-none");
    }
}

$("body").on("click", "#btn-del", function () {
    $("#btn-del").addClass("d-none");
    const previewContainer = document.getElementById("image_preview");
    upload_image = "";
    previewContainer.innerHTML = "";
    previewContainer.innerHTML = `
            <svg width="50px" height="50px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="13" r="3" stroke="#1C274C" stroke-width="1.5" />
                <path d="M9.77778 21H14.2222C17.3433 21 18.9038 21 20.0248 20.2646C20.51 19.9462 20.9267 19.5371 21.251 19.0607C22 17.9601 22 16.4279 22 13.3636C22 10.2994 22 8.76721 21.251 7.6666C20.9267 7.19014 20.51 6.78104 20.0248 6.46268C19.3044 5.99013 18.4027 5.82123 17.022 5.76086C16.3631 5.76086 15.7959 5.27068 15.6667 4.63636C15.4728 3.68489 14.6219 3 13.6337 3H10.3663C9.37805 3 8.52715 3.68489 8.33333 4.63636C8.20412 5.27068 7.63685 5.76086 6.978 5.76086C5.59733 5.82123 4.69555 5.99013 3.97524 6.46268C3.48995 6.78104 3.07328 7.19014 2.74902 7.6666C2 8.76721 2 10.2994 2 13.3636C2 16.4279 2 17.9601 2.74902 19.0607C3.07328 19.5371 3.48995 19.9462 3.97524 20.2646C5.09624 21 6.65675 21 9.77778 21Z" stroke="#1C274C" stroke-width="1.5" />
                <path d="M19 10H18" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" />
            </svg>
        `;
    $("#profile_image").val("");
    $("#image_data").val("");
});

// Find the closest parent container of the checkbox that contains the end_date input field
function FadeNearEnd(checkbox) {
    var parentContainer = $(checkbox).closest(".row"); // Adjust the selector if needed

    // Find the nearest 'end_date' input field within that parent container
    var endDateField = parentContainer.find(".end_date");

    // When the checkbox is checked
    if ($(checkbox).is(":checked")) {
        // Disable the nearest 'end_date' input field and change background color
        endDateField.prop("readonly", true).css("background-color", "#dadada");
    } else {
        // Enable the nearest 'end_date' input field and reset background color
        endDateField.prop("readonly", false).css("background-color", "#fff");
    }
}

function FadeEndDate(checkbox) {
    // Find the closest parent container of the checkbox that contains the end_date input field
    var parentContainer = $(checkbox).closest(".row"); // Adjust the selector if needed

    // Find the nearest 'end_date' input field within that parent container
    var endDateField = parentContainer.find(".other_end_date");

    // When the checkbox is checked
    if ($(checkbox).is(":checked")) {
        // Disable the nearest 'end_date' input field and change background color
        endDateField.prop("disabled", true).css("background-color", "#dadada");
    } else {
        // Enable the nearest 'end_date' input field and reset background color
        endDateField.prop("disabled", false).css("background-color", "#fff");
    }
}
// instructor dashboard profile completion
function updateCompletion(percentage) {
    // Ensure the percentage is between 0 and 100
    if (percentage < 0 || percentage > 100) {
        console.error("Percentage must be between 0 and 100");
        return;
    }

    const circle = document.getElementById("completion");
    const text = document.getElementById("completionText");

    if (!circle) return;
    // Calculate the stroke-dashoffset based on the percentage
    const circumference = 565.48; // Circumference of the circle (2 * π * r, where r = 90)
    const offset = circumference - circumference * (percentage / 100);

    // Update the stroke-dashoffset to fill the circle accordingly
    circle.style.strokeDashoffset = offset;

    // Update the text to display the percentage
    text.textContent = `${percentage}%`;
}

if (
    $("#pendingCount").val() !== "" ||
    $("#pendingCount").val() != "undefined"
) {
    let pendingCount = $("#pendingCount").val();
    let totalCount = $("#totalCount").val();
    const percentage = (pendingCount / totalCount) * 100;
    updateCompletion(percentage);
}

// Example: Call this function to update the circle dynamically
//   updateCompletion(80); // 30% completed

function newOnboardingsubmitContract() {
    //    var forgot= $('input[name="forgot"]:checked').val();;
    const signature = document.getElementById("signature").value;
    const address = document.getElementById("address").value;

    if (address) {
        $("#address").css({
            "outline-color": "",
            "outline-style": "",
            "outline-width": "",
        });
    } else {
        $("#address").css({
            "outline-color": "red",
            "outline-style": "solid",
            "outline-width": "thin",
        });
        return;
    }

    if (signature) {
        $("#signatureCanvas").css({
            "outline-color": "",
            "outline-style": "",
            "outline-width": "",
        });
    } else {
        $("#signatureCanvas").css({
            "outline-color": "red",
            "outline-style": "solid",
            "outline-width": "thin",
        });
        return;
    }
    const addressContainerElement = document.getElementById("address-input");
    const signatureContainerElement =
        document.getElementById("signature-input");

    addressContainerElement.innerHTML = "";
    addressContainerElement.classList.remove("d-inline-none");
    addressContainerElement.classList.add("d-none");
    addressContainerElement.nextSibling.classList.add("d-none");
    signatureContainerElement.classList.remove("d-inline-none");
    signatureContainerElement.classList.add("d-none");
    signatureContainerElement.parentNode.classList.add("d-flex");
    signatureContainerElement.parentNode.classList.add("align-items-center");
    createElement({
        appendTo: addressContainerElement.parentNode,
        innerHtml: address,
        selector: "span",
    });
    createElement({
        appendTo: addressContainerElement.parentNode,
        innerHtml: address,
        selector: "br",
    });
    signatureContainerElement.innerHTML = "";
    createElement({
        appendTo: signatureContainerElement.parentNode,
        selector: "img",
        attributes: {
            src: signature,
            alt: "User Signature",
        },
    });
    const signImage =
        signatureContainerElement.parentNode.parentElement.querySelectorAll(
            "img"
        )[1];

    var url = APP_URL + "/k12connections/submitContract";
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    convertImageUrlToBase64(signImage.src, (err, imgSrc) => {
        if (err) return console.error(err);
        signImage.src = imgSrc;
        const contracthtml = $("#contracthtml").html();
        debugger;
        $.ajax({
            type: "POST",
            url: url,
            data: {
                signature: signature,
                contracthtml: contracthtml,
                address: address,
            },
            dataType: "html",
            success: function (data) {
                window.location.href = APP_URL + "/web-dashboard";
            },
        });
    });
}
function convertImageUrlToBase64(url, callback) {
    fetch(url, {
        headers: {
            accept: "image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        },
        referrerPolicy: "strict-origin-when-cross-origin",
        body: null,
        method: "GET",
    })
        .then((r) => r.blob())
        .then((blob) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onloadend = function () {
                callback(null, reader.result);
            };
        })
        .catch((error) => {
            callback(new Error(`AJAX error: ${status} - ${error}`));
        });
}
// Get the canvas element and create a drawing context
// const canvas = document.getElementById('signatureCanvas');
// const context = canvas.getContext('2d');

// let isDrawing = false;
// let lastX = 0;
// let lastY = 0;

// // Add event listeners to track mouse movements
// canvas.addEventListener('mousedown', startDrawing);
// canvas.addEventListener('mousemove', draw);
// canvas.addEventListener('mouseup', stopDrawing);
// canvas.addEventListener('mouseout', stopDrawing);
// canvas.addEventListener('touchstart', startDrawing);
// canvas.addEventListener('touchmove', draw);
// canvas.addEventListener('touchend', stopDrawing);
// canvas.addEventListener('touchcancel', stopDrawing);

// Function to start drawing
function startDrawing(event) {
    var isTouch = event.type.startsWith("touch"); // Check if it's a touch event
    $("#signatureCanvas").css({
        "outline-color": "",
        "outline-style": "",
        "outline-width": "",
    });
    if (isTouch) {
        var touch = event.touches[0]; // Get the first touch point
        lastX = touch.clientX;
        lastY = touch.clientY;
    } else {
        lastX = event.offsetX;
        lastY = event.offsetY;
    }

    isDrawing = true;
    // [lastX, lastY] = [event.offsetX, event.offsetY];
}

// Function to draw
function draw(event) {
    if (!isDrawing) return;

    var isTouch = event.type.startsWith("touch"); // Check if it's a touch event

    var x, y;

    if (isTouch) {
        var touch = event.touches[0]; // Get the first touch point
        x = touch.clientX;
        y = touch.clientY;
    } else {
        x = event.offsetX;
        y = event.offsetY;
    }

    context.beginPath();
    context.moveTo(lastX, lastY);
    context.lineTo(x, y);
    context.stroke();

    lastX = x;
    lastY = y;
}

// Function to stop drawing
function stopDrawing() {
    isDrawing = false;
    var signature = document.getElementsByName("signature")[0];
    signature.value = canvas.toDataURL();
}

// Clear the canvas when the clear button is clicked
// const clearButton = document.getElementById('clearButton');
// clearButton.addEventListener('click', clearCanvas);

function clearCanvas() {
    var signature = document.getElementsByName("signature")[0];
    signature.value = "";
    context.clearRect(0, 0, canvas.width, canvas.height);
}

// Function to convert canvas to JPEG image
function convertCanvasToJPEG() {
    // Get the data URL of the canvas as a JPEG image
    const dataURL = canvas.toDataURL("image/jpg");

    // Create a link element and set the data URL as its href
    const link = document.createElement("a");
    link.href = dataURL;

    // Set the download attribute to specify the filename
    link.download = "signature.jpeg";

    // Programmatically trigger a click event on the link element
    link.click();
}

// Clear the canvas when the clear button is clicked
// const downloadButton = document.getElementById('downloadButton');
// downloadButton.addEventListener('click', downloadSignature);

function downloadSignature() {
    convertCanvasToJPEG();
}

function updateCompensation(selection, elem) {
    const [
        online,
        inPerson,
        hybrid,
        compSection,
        onlineComp,
        inPersonComp,
        internetSection,
        workFromHomeSection,
    ] = [
        document.getElementById("formatone"),
        document.getElementById("formatwo"),
        document.getElementById("formathree"),
        document.getElementById("compensation-section"),
        document.getElementById("online-compensation"),
        document.getElementById("in-person-compensation"),
        document.getElementById("internetConnectionSection"),
        document.getElementById("workFromHomeSection"),
    ];

    const updateVisibility = (elem, condition) => {
        if (condition) {
            if (elem.classList.contains("show")) {
                elem.classList.remove("hide");
            } else {
                elem.classList.add("show");
                elem.value = "";
                elem.classList.remove("hide");
            }
        } else {
            if (elem.classList.contains("hide")) {
                elem.classList.remove("show");
            } else {
                elem.classList.add("hide");
                const inputField = elem.querySelector("input");
                if (inputField) {
                    inputField.value = "";
                }
                elem.classList.remove("show");
            }
        }
    };

    switch (selection) {
        case "hybrid":
            online.checked = elem.checked;
            inPerson.checked = elem.checked;
            internetSection.checked = elem.checked;
            break;
        case "online":
            internetSection.checked = elem.checked;
        case "in-person":
            hybrid.checked = online.checked && inPerson.checked;
            break;
    }

    // Handle the visibility of the compensation sections
    updateVisibility(onlineComp, online.checked);
    updateVisibility(inPersonComp, inPerson.checked);
    updateVisibility(compSection, inPerson.checked || online.checked);
    updateVisibility(internetSection, online.checked || hybrid.checked);
    updateVisibility(workFromHomeSection, online.checked || hybrid.checked);
}

if ($("#formatwo").is(":checked")) {
    $(".maps").addClass("show");
    $(".maps").removeClass("hide");
} else {
    $(".maps").removeClass("show");
    $(".maps").addClass("hide");
}

function getSixTabData(e, url) {
    console.log("we are inside the function");

    const replaceHtmlId = e.dataset.bsTarget;

    let selectedTab = $(replaceHtmlId);
    // updateView(selectedTab[0])
    const parent = document.getElementById("mySixTabContent");

    Array.from(parent.children).forEach((element) => {
        element.classList.remove("active", "show");
        element.classList.add("d-none");
        $(`#step${activeTab - 1}-tab`).removeClass("active");
    });

    selectedTab[0].classList.add("active", "show");
    selectedTab[0].classList.remove("d-none");

    $("#skipTabButton").removeClass("d-none");
    $("#nextTabButton").text("Save and Continue");
}

function DeleteProfileImg(_this) {
    var url = $(_this).data("url");

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $.ajax({
        type: "POST",
        url: url,
        data: {},
        dataType: "json",
        processData: false,
        contentType: false,
        dataType: "json",
        success: function (data) {
            if (data.success == true) {
                $("#image_preview")
                    .html(`<svg width="50px" height="50px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="13" r="3" stroke="#1C274C" stroke-width="1.5" />
                            <path d="M9.77778 21H14.2222C17.3433 21 18.9038 21 20.0248 20.2646C20.51 19.9462 20.9267 19.5371 21.251 19.0607C22 17.9601 22 16.4279 22 13.3636C22 10.2994 22 8.76721 21.251 7.6666C20.9267 7.19014 20.51 6.78104 20.0248 6.46268C19.3044 5.99013 18.4027 5.82123 17.022 5.76086C16.3631 5.76086 15.7959 5.27068 15.6667 4.63636C15.4728 3.68489 14.6219 3 13.6337 3H10.3663C9.37805 3 8.52715 3.68489 8.33333 4.63636C8.20412 5.27068 7.63685 5.76086 6.978 5.76086C5.59733 5.82123 4.69555 5.99013 3.97524 6.46268C3.48995 6.78104 3.07328 7.19014 2.74902 7.6666C2 8.76721 2 10.2994 2 13.3636C2 16.4279 2 17.9601 2.74902 19.0607C3.07328 19.5371 3.48995 19.9462 3.97524 20.2646C5.09624 21 6.65675 21 9.77778 21Z" stroke="#1C274C" stroke-width="1.5" />
                            <path d="M19 10H18" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" />
                        </svg>`);
                $("#image_data").val("");
                $("#profile_image").val("");
                $("#delete_button").hide();
                alertify.success(data.message);
            } else {
                alertify.error(data.message);
            }
        },
    });
}

function updateEndDateState(checkbox) {
    var row = $(checkbox).closest(".row");
    var endDateField = row.find(".end_date");
    var hiddenInput = row.find(".working_here_value");
    console.log($(checkbox).is(":checked"));

    if ($(checkbox).is(":checked")) {
        endDateField
            .css("border-color", "#d8dadc")
            .prop("readonly", true)
            .css("background-color", "#dadada");
        endDateField.val("");
        hiddenInput.val(1);
    } else {
        if (endDateField.val() === "") {
            endDateField
                .css("border-color", "red")
                .prop("readonly", false)
                .css("background-color", "#fff");
        } else {
            endDateField
                .css("border-color", "#d8dadc")
                .prop("readonly", false)
                .css("background-color", "#fff");
        }
        hiddenInput.val(0);
    }
}

function updateOtherEndDateState(checkbox) {
    var row = $(checkbox).closest(".row");
    var endDateField = row.find(".other_end_date");
    var hiddenInput = row.find(".other_working_here_value");

    if ($(checkbox).is(":checked")) {
        endDateField
            .css("border-color", "#d8dadc")
            .prop("readonly", true)
            .css("background-color", "#dadada");
        endDateField.val("");
        hiddenInput.val(1);
    } else {
        if (endDateField.val() === "") {
            endDateField
                .css("border-color", "red")
                .prop("readonly", false)
                .css("background-color", "#fff");
        } else {
            endDateField
                .css("border-color", "#d8dadc")
                .prop("readonly", false)
                .css("background-color", "#fff");
        }
        hiddenInput.val(0);
    }
}

function CheckCertification(checkbox) {
    var row = $(checkbox).closest(".row");
    var hiddenInput = row.find(".check_certification");

    if ($(checkbox).is(":checked")) {
        hiddenInput.val(1);
    } else {
        hiddenInput.val(0);
    }
}

function updateReferenceVisible(checkbox) {
    var row = $(checkbox).closest(".row");
    var hiddenInput = row.find(".reference_visible_value");

    if ($(checkbox).is(":checked")) {
        hiddenInput.val(1);
    } else {
        hiddenInput.val(0);
    }
}

$("body").on("input", ".ans-text", function (e) {
    const text = this.value;
    const words = text
        .trim()
        .split(/\s+/)
        .filter(function (word) {
            return word.length > 0;
        });
    if (words.length > 500) {
        this.value = words.slice(0, 500).join(" ");
        e.stopPropagation();
    }
});

$("body").on("click", "#free_response_btn", function () {
    $("#nextTabButton").text("Save & Continue");
    $("#skipTabButton").removeClass("d-none");
});

$("body").on("click", "#quiz_btn", function () {
    $("#nextTabButton").text("Submit");
    $("#skipTabButton").addClass("d-none");
});

function graduationSvg(svg) {
    var row = $(svg).closest(".login__form");
    var focusElement = row.find(".month_and_year_graduation");
    focusElement.focus();
}

function teachingYearSvg(svg) {
    var row = $(svg).closest(".login__form");
    var focusElement = row.find(".teaching_certification_year");
    focusElement.focus();
}

function issueDateSvg(svg) {
    var row = $(svg).closest(".login__form");
    var focusElement = row.find(".issue_date");
    focusElement.focus();
}

function validTillDateSvg(svg) {
    var row = $(svg).closest(".login__form");
    var focusElement = row.find(".validTill_date");
    focusElement.focus();
}

function startDateSvg(svg) {
    var row = $(svg).closest(".login__form");
    var focusElement = row.find(".start_date");
    focusElement.focus();
}

function endDateSvg(svg) {
    var row = $(svg).closest(".login__form");
    var focusElement = row.find(".end_date");
    focusElement.focus();
}

function otherStartDateSvg(svg) {
    var row = $(svg).closest(".login__form");
    var focusElement = row.find(".other_start_date");
    focusElement.focus();
}

function otherEndDateSvg(svg) {
    var row = $(svg).closest(".login__form");
    var focusElement = row.find(".other_end_date");
    focusElement.focus();
}

function isDeclined() {
    const url = APP_URL + "/k12connections/declined-contract";
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $.ajax({
        url: url,
        method: "POST",
        dataType: "json",
        success: function (response) {
            if (response.success) {
                window.location.href = response.redirectRoute;
            }
        },
        error: function (xhr, status, error) {
            alertify.error(error);
        },
    });
}

/**
 * @type {VideoRecorderService | null}
 */
let videoSource = null;
$("body").on("click", "#recordVideo", function () {
    $("#VideoRecordModal").modal("show");
    if (videoSource) {
        $("body").off(
            "click",
            "#video-record-close",
            videoSource.handleCloseFunction
        );
    }
    videoSource = new VideoRecorderService();
    $("body").on(
        "click",
        "#video-record-close",
        videoSource.handleCloseFunction
    );
    videoSource.setupUI("profile_video_record_container");
    videoSource.onUploadCompleted((data) => {
        if (data.detail?.status) {
            $("#video_loading_spinner").addClass("d-none");
            $(".recording-container").html(`recorded_video.mp4
                <svg width="25" height="26" onclick="showHideVideoDemo()" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0 13C0 6.09644 5.59644 0.5 12.5 0.5C19.4036 0.5 25 6.09644 25 13C25 19.9036 19.4036 25.5 12.5 25.5C5.59644 25.5 0 19.9036 0 13Z" fill="#004CBD"></path>
                    <path d="M15.8303 11.8089C16.0326 11.9238 16.2017 12.0954 16.3197 12.3052C16.4377 12.515 16.5 12.7552 16.5 13C16.5 13.2448 16.4377 13.485 16.3197 13.6948C16.2017 13.9046 16.0326 14.0762 15.8303 14.1911L10.4356 17.3262C9.56698 17.8315 8.5 17.1745 8.5 16.1355V9.86495C8.5 8.82548 9.56698 8.16895 10.4356 8.67338L15.8303 11.8089Z" fill="white"></path>
                </svg>
                <svg width="12" id="remove_recording_video" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11 1L1 11M1 1L11 11" stroke="#004CBD" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>`);
            $("#instructor_video_url_s3").val(data.detail.base64);
            $("#videoname").val("recorded_video.mp4");
            $("#video_source").val("recording");
            $(".video_uplod").addClass("disabled_video_upload");
            $("#recordVideo").addClass("disabled_video_upload");
            $(".showRecording").removeClass("d-none");
            $(".video-play-container").removeClass("d-none");
            // ✅ Dynamically update video src
            const videoElement = $("#video_demo").find(".video-player")[0];
            if (videoElement) {
                videoElement.pause(); // Stop if already playing
                videoElement.src = data.detail.base64; // Update src dynamically
                videoElement.load(); // Reload the video
            }
        } else {
            $("#recordVideo").html(`Record your video
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="10" cy="10" r="10" fill="#CF1313"></circle>
                </svg>`);
        }
    });
});

$("#VideoRecordModal").on("hidden.bs.modal", function () {
    if (typeof videoSource !== undefined) {
        videoSource.continueRecording = false;
        videoSource.stopRecording();
        videoSource.stopAllStreams();
    }
    // const url = APP_URL + "/k12connections/get-recording";

    // $.ajax({
    //     url: url,
    //     method: "GET",
    //     dataType: "json",
    //     success: function (response) {
    //         if (response.success) {
    //             $("#video_loading_spinner").addClass("d-none");
    //             $(".recording-container").html(`recorded_video.mp4
    //                 <svg width="25" height="26" onclick="showHideVideoDemo()" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg">
    //                     <path d="M0 13C0 6.09644 5.59644 0.5 12.5 0.5C19.4036 0.5 25 6.09644 25 13C25 19.9036 19.4036 25.5 12.5 25.5C5.59644 25.5 0 19.9036 0 13Z" fill="#004CBD"></path>
    //                     <path d="M15.8303 11.8089C16.0326 11.9238 16.2017 12.0954 16.3197 12.3052C16.4377 12.515 16.5 12.7552 16.5 13C16.5 13.2448 16.4377 13.485 16.3197 13.6948C16.2017 13.9046 16.0326 14.0762 15.8303 14.1911L10.4356 17.3262C9.56698 17.8315 8.5 17.1745 8.5 16.1355V9.86495C8.5 8.82548 9.56698 8.16895 10.4356 8.67338L15.8303 11.8089Z" fill="white"></path>
    //                 </svg>
    //                 <svg width="12" class="cursor-pointer" id="remove_recording_video" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    //                     <path d="M11 1L1 11M1 1L11 11" stroke="#004CBD" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
    //                 </svg>`);
    //             $("#instructor_video_url_s3").val(response.base64);
    //             $("#videoname").val('recorded_video.mp4');
    //             $("#video_source").val('recorded');
    //             $(".video_uplod").addClass("disabled_video_upload");
    //             $("#recordVideo").addClass("disabled_video_upload");
    //             $(".showRecording").removeClass("d-none");
    //             $(".video-play-container").removeClass("d-none");
    //             // ✅ Dynamically update video src
    //             const videoElement = $("#video_demo").find(".video-player")[0];
    //             if (videoElement) {
    //                 videoElement.pause(); // Stop if already playing
    //                 videoElement.src = response.base64; // Update src dynamically
    //                 videoElement.load(); // Reload the video
    //             }
    //         } else {
    //             $("#recordVideo").html(`Record your video
    //                 <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    //                     <circle cx="10" cy="10" r="10" fill="#CF1313"></circle>
    //                 </svg>`);
    //         }
    //     },
    //     error: function (xhr, status, error) {
    //         console.log(error);
    //     },
    // });
});

// function removeRecording()
// {
//     // $('#recordVideo').html(`Record a Video
//     //     <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
//     //         <path
//     //             d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11"
//     //             stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
//     //         <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5"
//     //             stroke-linecap="round" stroke-linejoin="round"></path>
//     //         <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round"
//     //             stroke-linejoin="round"></path>
//     //     </svg>`)
//     $('.showRecording').addClass('d-none');
//     $('#instructor_video_url_s3').val('');
//     $('#videoname').val('');
//     $('.video_uplod').removeClass('disabled_video_upload');
//     $('.video_record').removeClass('disabled_video_upload');

//     const url = APP_URL + '/k12connections/remove-recording-video';
//     const userId = $('#instructorId').val();
//     $.ajaxSetup({
//         headers: {
//             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
//         }
//     });

//     $.ajax({
//         url: url,
//         method: 'POST',
//         dataType: 'json',
//         data: {
//             userId: userId,
//         },
//         success: function (response) {
//             if (response.success) {
//             }
//         },
//         error: function (xhr, status, error) {
//             console.log(error);
//         }
//     });
// }

$(document).on("click", "#remove_recording_video", function () {
    $("#VideoRecordModaldelete").modal("show");
});
$(document).on("click", "#yes_delete", function () {
    $("#VideoRecordModaldelete").modal("hide");
    $(".showRecording").addClass("d-none");
    $("#instructor_video_url_s3").val("");
    $("#videoname").val("");
    $(".video_uplod").removeClass("disabled_video_upload");
    $(".video_record").removeClass("disabled_video_upload");

    const url = APP_URL + "/k12connections/remove-recording-video";
    const userId = $("#instructorId").val();
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $.ajax({
        url: url,
        method: "POST",
        dataType: "json",
        data: {
            userId: userId,
        },
        success: function (response) {
            if (response.success) {
            }
        },
        error: function (xhr, status, error) {
            console.log(error);
        },
    });
});
$(document).on("click", ".closedeltemodal_video_recorder", function () {
    $("#VideoRecordModaldelete").modal("hide");
});

$("body").on("change", ".videoInput", function (e) {
    let fileInput = e.target;

    // Get the selected file from that input
    let file = fileInput.files[0];

    // Validate file format and size
    if (file) {
        let validFormats = [
            "video/mp4",
            "video/avi",
            "video/mov",
            "video/quicktime",
            "video/x-matroska",
            ".mkv",
        ]; // Add other valid formats as needed
        let maxSize = 21 * 1024 * 1024; // 21 MB

        if (!validFormats.includes(file.type)) {
            $("#progressContainer").hide();
            $("#uploadStatus").show();
            $(fileInput).val("");
            $("#uploadStatus")
                .text(
                    "The file format is invalid. Please upload a file in one of the supported formats."
                )
                .addClass("text-danger");
            return; // Stop the upload process
        }

        if (file.size > maxSize) {
            $("#progressContainer").hide();
            $("#uploadStatus").show();
            $(fileInput).val("");

            $("#uploadStatus")
                .text("File size exceeds the maximum limit.")
                .addClass("text-danger");
            return; // Stop the upload process
        }
        // Set up the CSRF token for the request
        $.ajaxSetup({
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
        });

        // Prevent default form submission behavior
        e.preventDefault();

        const url = APP_URL + "/k12connections/videouploads3";

        let formData = new FormData();
        formData.append("video", this.files[0]);

        let progressBar = $("#progressBar");
        let progressContainer = $("#progressContainer");
        progressBar.show();
        $("#uploadStatus").show();

        // Reset progress bar immediately to 0% when a new file is selected
        progressBar.css({ width: "0%", transition: "none" });

        // Re-enable the transition for smooth animation
        setTimeout(() => {
            progressBar.css({ transition: "width 0.3s ease-in-out" });
        }, 50); // A small timeout to ensure the reset happens smoothly

        progressContainer.show(); // Ensure the progress bar container is visible

        // Perform the AJAX request to upload the file
        $.ajax({
            url: url,
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,

            xhr: function () {
                let xhr = new window.XMLHttpRequest();
                console.group("file upload");
                xhr.upload.addEventListener(
                    "progress",
                    function (event) {
                        if (event.lengthComputable) {
                            let percent = Math.round(
                                (event.loaded / event.total) * 100
                            );
                            console.log(event.loaded, event.total);
                            // Smooth animation
                            requestAnimationFrame(() => {
                                progressBar.css({
                                    width: percent + "%",
                                    transition: "width 0.3s ease-in-out",
                                });
                            });
                            $("#uploadStatus").empty();

                            $("#uploadStatus")
                                .text(`Uploading... ${percent}%`)
                                .removeClass("text-danger");

                            if (percent >= 99) {
                                console.groupEnd("file upload");
                            }
                        }
                    },
                    false
                );
                return xhr;
            },
            beforeSend: function () {
                $("#uploadStatus").text("Uploading...");
            },
            success: function (response) {
                if (response.success) {
                    $("#uploadStatus").text("Upload Successful!");
                    $(".upload_video .video_uplod").css(
                        "border",
                        "1px solid #6F6C90"
                    );
                    $("#video_source").val("");
                    // Update input fields with video info
                    $("#instructor_video_url_s3").val(response.video_url);
                    $("#videoname").val(response.videoname);
                    $("#recordVideo").addClass("disabled_video_upload");
                    $(".upload_video .video_uplod").first().empty();
                    $(".upload_video .video_uplod").first().html(`
                        <span id="video_name_with_remove">${response.videoname}</span>
                        <span class="ms-2" title="Remove" id="remove-video">
                            <svg style="cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                                <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                            </svg>
                        </span>
                    `);

                    console.log("Video URL:", response.video_url);
                    console.log("Video Name:", response.videoname);
                } else {
                    $("#uploadStatus").text(response.error || "Upload failed!");
                    console.log("Error:", response.error);
                }
            },
            error: function (xhr, status, error) {
                let errorMessage = "An error occurred while uploading.";
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                $("#uploadStatus").text(errorMessage);
                console.log("Upload Error:", xhr);
            },
        });
    }
});

$("body").on("change", ".videoInput", function (e) {
    let fileInput = e.target;

    // Get the selected file from that input
    let file = fileInput.files[0];

    // Validate file format and size
    if (file) {
        let validFormats = [
            "video/mp4",
            "video/avi",
            "video/mov",
            "video/quicktime",
            "video/x-matroska",
            ".mkv",
        ]; // Add other valid formats as needed
        let maxSize = 21 * 1024 * 1024; // 21 MB

        if (!validFormats.includes(file.type)) {
            $("#progressContainer").hide();
            $("#uploadStatus").show();
            $(fileInput).val("");
            $("#uploadStatus")
                .text(
                    "The file format is invalid. Please upload a file in one of the supported formats."
                )
                .addClass("text-danger");
            return; // Stop the upload process
        }

        if (file.size > maxSize) {
            $("#progressContainer").hide();
            $("#uploadStatus").show();
            $(fileInput).val("");

            $("#uploadStatus")
                .text("File size exceeds the maximum limit.")
                .addClass("text-danger");
            return; // Stop the upload process
        }
        // Set up the CSRF token for the request
        $.ajaxSetup({
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
        });

        // Prevent default form submission behavior
        e.preventDefault();

        const url = APP_URL + "/k12connections/videouploads3";

        let formData = new FormData();
        formData.append("video", this.files[0]);

        let progressBar = $("#progressBar");
        let progressContainer = $("#progressContainer");
        progressBar.show();
        $("#uploadStatus").show();

        // Reset progress bar immediately to 0% when a new file is selected
        progressBar.css({ width: "0%", transition: "none" });

        // Re-enable the transition for smooth animation
        setTimeout(() => {
            progressBar.css({ transition: "width 0.3s ease-in-out" });
        }, 50); // A small timeout to ensure the reset happens smoothly

        progressContainer.show(); // Ensure the progress bar container is visible

        // Perform the AJAX request to upload the file
        $.ajax({
            url: url,
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,

            xhr: function () {
                let xhr = new window.XMLHttpRequest();
                console.group("file upload");
                xhr.upload.addEventListener(
                    "progress",
                    function (event) {
                        if (event.lengthComputable) {
                            let percent = Math.round(
                                (event.loaded / event.total) * 100
                            );
                            console.log(event.loaded, event.total);
                            // Smooth animation
                            requestAnimationFrame(() => {
                                progressBar.css({
                                    width: percent + "%",
                                    transition: "width 0.3s ease-in-out",
                                });
                            });
                            $("#uploadStatus").empty();

                            $("#uploadStatus")
                                .text(`Uploading... ${percent}%`)
                                .removeClass("text-danger");

                            if (percent >= 99) {
                                console.groupEnd("file upload");
                            }
                        }
                    },
                    false
                );
                return xhr;
            },
            beforeSend: function () {
                $("#uploadStatus").text("Uploading...");
            },
            success: function (response) {
                if (response.success) {
                    $("#uploadStatus").text("Upload Successful!");
                    $(".upload_video .video_uplod").css(
                        "border",
                        "1px solid #6F6C90"
                    );
                    $("#video_source").val("");
                    // Update input fields with video info
                    $("#instructor_video_url_s3").val(response.video_url);
                    $("#videoname").val(response.videoname);
                    $("#recordVideo").addClass("disabled_video_upload");
                    $(".upload_video .video_uplod").first().empty();
                    $(".upload_video .video_uplod").first().html(`
                        <span id="video_name_with_remove">${response.videoname}</span>
                        <span class="ms-2" title="Remove" id="remove-video">
                            <svg style="cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                                <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"/>
                            </svg>
                        </span>
                    `);

                    console.log("Video URL:", response.video_url);
                    console.log("Video Name:", response.videoname);
                } else {
                    $("#uploadStatus").text(response.error || "Upload failed!");
                    console.log("Error:", response.error);
                }
            },
            error: function (xhr, status, error) {
                let errorMessage = "An error occurred while uploading.";
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                $("#uploadStatus").text(errorMessage);
                console.log("Upload Error:", xhr);
            },
        });
    }
});

$("body").on("click", "#remove-video", function (event) {
    event.stopPropagation();
    const uploadContainer = $(this).closest(".upload_video");
    $("#progressBar").hide();
    $("#progressContainer").hide();
    $("#uploadStatus").hide();
    $("#instructor_video_url_s3").val("");
    $("#videoname").hide("");
    $(".video_record").removeClass("disabled_video_upload");
    uploadContainer.find("#videoInputnew").val(""); // Clear file input
    uploadContainer.find(".video_uplod").html(`
         <input type="file" id="videoInputnew"  class="form-control videoInput myFile second" name="video" class="myFile"
                    accept="video/mp4,video/x-m4v,video/quicktime,video/x-matroska,.mkv,video/x-msvideo">
        Upload Video
        <svg  width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>`);
    $("#video_error").text("");
    $(".video_uplod").css("border-color", "#ced4da");

    const url = APP_URL + "/k12connections/remove-profile-video";
    const userId = $("#instructorId").val();
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $.ajax({
        url: url,
        method: "POST",
        dataType: "json",
        data: {
            userId: userId,
        },
        success: function (response) {
            if (response.success) {
            }
        },
        error: function (xhr, status, error) {
            console.log(error);
        },
    });
});

$(document).on("change", "#certification_dropdown", function () {
    if ($("#certification_dropdown").val() !== "License/Certification") {
        $(".secondno").show();
        $(".appendCertificate ").hide();
        $(".addMoreCertification").hide();
    } else {
        $(".secondno").hide();
        $(".appendCertificate ").show();
        $(".addMoreCertification").show();
    }
});
// $("#additional_certificates").empty();
$(document).on("click", ".add_additional_certificate_button", function () {
    var loading = "<i class='fa fa-spinner fa-spin'></i>Loading";

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    $.ajax({
        url: APP_URL + "/additional_certificate_all_category",
        method: "post",
        beforeSend: function () {
            $(".add_additional_certificate_button").html(loading);
        },
        success: function (response) {
            $(".add_additional_certificate_button").html(
                `<span>+</span> Add More`
            );

            $all_category = response.all_categories;

            const container = $("#main_certificate_div");
            const count = container.find(".additional_certificate").length;
            console.log(count);

            // Determine margin class based on how many already exist
            const marginClass = count > 0 ? "my-5" : "my-2";
            // $("#additional_certificates").show(300);

            ///this is for the select options
            let selectOptions = "";
            for (let i = 0; i < $all_category.length; i++) {
                let c = $all_category[i];
                selectOptions += `<option value="${c.id}">${c.name}</option>`;
            }
            selectOptions += `<option value="Other">Other</option>`;

            container.find(".add_additional_certificate_button").before(`
           <div class="d-flex additional_certificate ${marginClass}" style="align-items: stretch;gap:2.6rem;">
                        <div style="width:91%!important">
                            <div class="row mt-3 category_and_subcategory_div">
                                <div class="col-md-6 ">
                                    <div class="login__form select_category_div">
                                        <select class="common__login__input form-select clas add_select_category additional_certificate_error"  data-allow-clear="1" type="text" name="additional_category[]" id="credentialing_agency_1745826268966">
                                            <option value="" hidden=""> Select Certificate/License type*</option>
                                             ${selectOptions}

                                        </select>


                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="login__form other_select_subcategory_select_div">
                                        <select  class="common__login__input form-select add_sub_category additional_certificate_error" placeholder="Credentialing Agency*" data-allow-clear="1" type="text" name="additional_sub_category[]" id="credentialing_agency_1745826268966">
                                            <option value="" hidden=""> Select Certificate/License*</option>
                                        </select>
                                    </div>
                                </div>

                            </div>
                            <div class="row my-1">
                                <div class="col-md-6">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="login__form select sel" style="position: relative;">
                                                <input class="common__login__input teaching_certification_year additional_certificate_error employee-dates issue_date" type="text" placeholder="Issue date*" name="additional_issue_date[]" id="additional_issue_date" value="">
                                                <svg onclick="issueDateSvg(this)" style="position: absolute; top: 32%;right: 30px; outline: none;" width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#000000" stroke-width="0.00024000000000000003">
                                                    <g id="SVGRepo_bgCarrier_1745827748577" stroke-width="0"></g>
                                                    <g id="SVGRepo_tracerCarrier_1745827748577" stroke-linecap="round" stroke-linejoin="round"></g>
                                                    <g id="SVGRepo_iconCarrier_1745827748577">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6 2C6 1.44772 6.44772 1 7 1C7.55228 1 8 1.44772 8 2V3H16V2C16 1.44772 16.4477 1 17 1C17.5523 1 18 1.44772 18 2V3H19C20.6569 3 22 4.34315 22 6V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V6C2 4.34315 3.34315 3 5 3H6V2ZM16 5V6C16 6.55228 16.4477 7 17 7C17.5523 7 18 6.55228 18 6V5H19C19.5523 5 20 5.44772 20 6V9H4V6C4 5.44772 4.44772 5 5 5H6V6C6 6.55228 6.44772 7 7 7C7.55228 7 8 6.55228 8 6V5H16ZM4 11V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V11H4Z" fill="#aeaeae"></path>
                                                    </g>
                                                </svg>

                                            </div>
                                        </div>
                                        <div class="col-md-6 add_cert_valid_till_div">
                                            <div class="login__form select sel " style="position: relative;">
                                                 <input class="common__login__input add_cert_valid_till_input  additional_certificate_error employee-dates validTill_date" type="text" placeholder="Valid till" name="additional_till_date[]" id="additional_till_date" value="">
                                                <svg onclick="validTillDateSvg(this)" style="position: absolute; top: 32%;right: 30px; outline: none;" width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#000000" stroke-width="0.00024000000000000003">
                                                    <g id="SVGRepo_bgCarrier_1745827748577" stroke-width="0"></g>
                                                    <g id="SVGRepo_tracerCarrier_1745827748577" stroke-linecap="round" stroke-linejoin="round"></g>
                                                    <g id="SVGRepo_iconCarrier_1745827748577">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6 2C6 1.44772 6.44772 1 7 1C7.55228 1 8 1.44772 8 2V3H16V2C16 1.44772 16.4477 1 17 1C17.5523 1 18 1.44772 18 2V3H19C20.6569 3 22 4.34315 22 6V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V6C2 4.34315 3.34315 3 5 3H6V2ZM16 5V6C16 6.55228 16.4477 7 17 7C17.5523 7 18 6.55228 18 6V5H19C19.5523 5 20 5.44772 20 6V9H4V6C4 5.44772 4.44772 5 5 5H6V6C6 6.55228 6.44772 7 7 7C7.55228 7 8 6.55228 8 6V5H16ZM4 11V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V11H4Z" fill="#aeaeae"></path>
                                                    </g>
                                                </svg>

                                            </div>
                                            <div class="mt-3 ms-1" style="display: flex; align-items: center; gap: 6px;">
                                                <input type="checkbox" class="valid_till_check_box"  name="additional_valid_till_check_box[]"  style="width: 18px; height: 14px;">

                                                <label>Does not expire</label>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div class="col-md-6 upload_add_cert_div  ">
                                    <div class="login__form position-relative">
                                       <label class="custom-file-input additional_certificate_error common__login__input">
                                      Certificates/License
                                        <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M16 11V14.3333C16 14.7754 15.8244 15.1993 15.5118 15.5118C15.1993 15.8244 14.7754 16 14.3333 16H2.66667C2.22464 16 1.80072 15.8244 1.48816 15.5118C1.17559 15.1993 1 14.7754 1 14.3333V11" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                       </path>
                                       <path d="M13.3333 5.16667L9.16667 1L5 5.16667" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       <path d="M9 2V12" stroke="#A7A9B7" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                       </svg>
                                       <div class="additional_certificate_upload_parent_div">
                                         <input style=""  class="add_cert_upload_input" type="file" id="additional_certificate_upload" name="additional_certificate_upload[]">
                                       </div>
                                       </label>
                                       <span class="text-danger add_error_file_and_size" style="display:none;font-size:13px"></span>
                                       <small style="font-size: 0.8rem; column-gap: 5px" class="flex-wrap d-flex py-3 text-muted justify-content-center d-block">
                                            <i>Accepted file formats include *.pdf, *.doc, *.docx, *.png, *.jpg, and *.jpeg up to 1 MB</i>
                                        </small>
                                       <span class="uploaded-documents mt-3 w-100" style="display:none;margin-top:2% width:fit-content"><span class="add_cert_upload_text_of_file" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;"></span>
                                       <span style="z-index:5;cursor:pointer; width:fit-content;" class="ms-2 remove-certificate remove_certificate_new" title="Remove">
                                       <svg class="cros_icon_add_cert" style="position:relative;z-index:5;cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x-lg" viewBox="0 0 16 16">
                                       <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"></path>
                                       </svg>
                                     </span>
                                     </span>
                                       <div class="mt-3 ms-1" style="display: flex; align-items: center; gap: 6px;">
                                           <input type="checkbox" class="upload_certification_checkbox"  style="width: 18px; height: 14px;">
                                           <label>Make this document visible to the schools</label>
                                          </div>
                                       </div>

                                </div>

                            </div>
                        </div>

                        <div class="d-flex  ps-3" style="border-left:1px solid #d8dadc; align-items: center;">
                        <svg style="cursor:pointer" class="delete_additional_certificate" width="30px" height="30px" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                                <path fill="#a7a9b7" d="M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z"></path>
                            </svg>
                        </div>


                    </div>
        `);
            console.log(container);

            initializeOnboardingBootstrapDatepicker(container);
        },
    });
});

//this is wehn we fill the empty input and border red got removed
$(document).on("change", ".additional_certificate_error", function () {
    const $el = $(this);
    const type = $el.attr("type");
    const tag = $el.prop("tagName").toLowerCase();
    const $label = $el.closest("label.additional_certificate_error");

    let isValid = true;

    if (type === "file") {
        // For file input, check if file selected
        isValid = $el[0].files && $el[0].files.length > 0;

        if (isValid && $label.length > 0) {
            // ✅ Clear border on label only (not input)
            $label.css("border", "");
        }
    } else {
        // For other input/select
        const value = $el.val();
        isValid = value && value.trim() !== "";

        if (isValid) {
            if ($label.length > 0) {
                // ✅ Clear border on label if it's styled
                $label.css("border", "");
            } else {
                // ✅ Otherwise, remove from input/select
                $el.css("border", "");
            }
        }
    }
});

$(document).on("click", ".delete_additional_certificate", function (e) {
    e.preventDefault();

    var $parent = $(this).closest(".additional_certificate");
    //    $(this).closest('.add_additional_certificate_button').find().html('<span>+</span> Add additional certificate/license')

    $parent.remove();
    if ($(".additional_certificate").length == 0) {
        $(".add_additional_certificate_button").html(
            "<span>+</span> Add additional certificate/license"
        );
    }
});
//this is when we check the input of valid_till_check_b
$(document).on("change", ".valid_till_check_box", function () {
    var parentDiv = $(this).closest(".add_cert_valid_till_div");
    var inputField = parentDiv.find(".add_cert_valid_till_input");

    if ($(this).is(":checked")) {
        inputField.val("").prop("disabled", true).css("background", "#D8DADC");
    } else {
        inputField.prop("disabled", false).css("background", "white");
    }
});
//this is for the update one
function toggleValidTillInputs() {
    $(".valid_till_check_box").each(function () {
        var parentDiv = $(this).closest(".add_cert_valid_till_div");
        var inputField = parentDiv.find(".add_cert_valid_till_input");

        if ($(this).is(":checked")) {
            inputField.prop("disabled", true).css("background", "#D8DADC");
        } else {
            inputField.prop("disabled", false).css("background", "white");
        }
    });
}

// Run on page load

$(document).on("change", ".add_select_category", function () {
    const $value = $(this).find("option:selected").val();
    const $wrapper = $(this).closest(".category_and_subcategory_div"); // Scope to nearest container

    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $.ajax({
        url: APP_URL + "/get_all_sub_category",
        method: "post",
        data: {
            id: $value,
        },
        success: function (response) {
            const $subCategorySelect = $wrapper.find(".add_sub_category");

            $subCategorySelect.css("border", "");

            $subCategorySelect.empty();

            if ($value == "Other") {
                $subCategorySelect.append(
                    `<option value="Other"selected>Other</option>`
                );

                console.log("inside the category div");
                var categorydiv = $wrapper.find(".select_category_div");
                var subcategorydiv = $wrapper.find(
                    ".other_select_subcategory_select_div"
                );
                categorydiv.find("input").remove();
                subcategorydiv.find("input").remove();

                categorydiv.append(
                    `<input  class="common__login__input  other_category_input_field other_input   my-3 " placeholder="Enter other Select certificate/license type*">`
                );
                subcategorydiv.append(
                    `<input class="other_subcategory_input_field common__login__input my-3 other_input" placeholder="Enter other certificate/license*">`
                );
                // categorydiv.find(".other_category_input_field").show();
                // subcategorydiv.find(".other_subcategory_input_field").show();
            } else {
                var categorydiv = $wrapper.find(".select_category_div");
                var subcategorydiv = $wrapper.find(
                    ".other_select_subcategory_select_div"
                );
                categorydiv.find("input").remove();
                subcategorydiv.find("input").remove();
                $subCategorySelect.append(
                    `<option value="">Select certificate/license*</option>`
                );
                response.forEach(function (category) {
                    $subCategorySelect.append(
                        `<option value='${category.id}'>${category.name}</option>`
                    );
                });

                // Append "Other" after the loop
                $subCategorySelect.append(
                    `<option value="Other">Other</option>`
                );
            }
        },
    });
});

$(document).on("change", ".add_sub_category", function () {
    const selectedText = $(this).find("option:selected").text();

    if (selectedText === "Other") {
        console.log("Other option selected");
        var closest_subcategorydiv = $(this).closest(
            ".category_and_subcategory_div"
        );
        closest_subcategorydiv.find(".other_select_subcategory_select_div")
            .append(`
        <input placeholder="Enter other certificate/license *"
               class="other_subcategory_input_field common__login__input my-3 other_input">
    `);
    } else {
        var closest_subcategorydiv = $(this).closest(
            ".category_and_subcategory_div"
        );
        closest_subcategorydiv
            .find(".other_select_subcategory_select_div")
            .find("input")
            .remove();
    }
});

let lastFile = null;
$(document).on("focus", ".add_cert_upload_input", function () {
    lastFile = this.files[0] || null; // Save the previously selected file
});
$(document).on("change", ".add_cert_upload_input", function (event) {
    const target = event.target;
    const file = event.target.files[0];

    // If user clicked cancel (no file selected), restore last file
    if (!file && lastFile) {
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(lastFile);
        target.files = dataTransfer.files;
        console.log("Cancel clicked, restoring previous file.");
    }

    const validTypes = ["pdf", "doc", "docx", "png", "jpg", "jpeg"];
    const ext = file.name.split(".").pop().toLowerCase();
    const maxSize = 1024 * 1024;

    if (file.size > maxSize) {
        var parent = $(this).closest(".upload_add_cert_div");
        //this is the label inside it input is placed
        var custom_input = parent.find(".custom-file-input");
        var custom_label_input = custom_input.find(".add_cert_upload_input");

        if (
            custom_label_input.length &&
            custom_label_input[0].files.length > 0
        ) {
            // Clear the selected file
            custom_label_input.val("");

            // Remove the data-bs-file attribute
            custom_label_input.removeAttr("data-bs-file");
        }
        var errorSpan = parent.find(".add_error_file_and_size");
        console.log("this exceed maximum size");
        errorSpan.show();
        errorSpan.text("File size exceeds the 1MB limit.");
        custom_input.css({ border: "1px solid red" });
        custom_input.val("");
        return;
    } else if (!validTypes.includes(ext)) {
        var parent = $(this).closest(".upload_add_cert_div");
        var custom_input = parent.find(".custom-file-input");
        var errorSpan = parent.find(".add_error_file_and_size");
        errorSpan.show();
        errorSpan.text(
            "Invalid file type. Only PDF, DOC, DOCX, PNG, JPG, and JPEG are allowed."
        );
        custom_input.css({ border: "1px solid red" });
        custom_input.val("");
        return;
    } else {
        var parent = $(this).closest(".upload_add_cert_div");
        var custom_input = parent.find(".custom-file-input");
        var errorSpan = parent.find(".add_error_file_and_size");

        errorSpan.hide();
        errorSpan.text(""); // optional: clear previous message
        custom_input.css({ border: "" });
    }

    if (target.tagName === "INPUT" && target.type === "file") {
        if (!target.files || target.files.length === 0) {
            // Prevent all other listeners from firing
            event.stopImmediatePropagation();
            event.preventDefault();
            console.log("File selection cancelled — change event suppressed.");
        } else {
            var parentforinputs = $(this).closest(
                ".additional_certificate_upload_parent_div"
            );
            var hidden_input = parentforinputs.find(
                ".exisiting_additional_certificates"
            );
            hidden_input.val("");

            var labelinput = parentforinputs.find(".add_cert_upload_input");
            labelinput.attr("data-bs-file", $(this).val());

            // Get the file name
            var parent = $(this).closest(".upload_add_cert_div");
            var text_span_div = parent.find(".uploaded-documents");
            text_span_div.addClass("d-flex justify-content-between");
            text_span_div.show();

            //for making already inserted text  empty
            var text_span = parent.find(".add_cert_upload_text_of_file");
            text_span.text("");
            console.log("we are inside of the input");
            var fileName = $(this).val().split("\\").pop();
            // Find the closest .add_cert_upload_span and prepend the file name
            $(this)
                .closest(".upload_add_cert_div")
                .find(".add_cert_upload_text_of_file")
                .first()
                .prepend(fileName + " ");
        }
    }

    //for removing the  existing hidden input if we select the anothere file in update time
});
$(document).on("click", ".cros_icon_add_cert", function () {
    console.log("Sdfsdfsdfg");
    var parent = $(this).closest(".upload_add_cert_div");
    var input = parent.find(".add_cert_upload_input");
    input.val("");
    if (input.attr("data-bs-file")) {
        input.removeAttr("data-bs-file");
    }

    var document_div = parent.find(".uploaded-documents");
    document_div.removeClass("d-flex justify-content-between");
    document_div.css("display", "none");
});

// fot  the change  in the input of the other category input
$(document).on("input", ".other_category_input_field", function () {
    let inputVal = $(this).val();

    // Find the closest parent with class "select_category_div"
    let parentDiv = $(this).closest(".select_category_div");

    // Find the <select> inside that parent with class "add_select_category"
    let select = parentDiv.find("select.add_select_category");

    // Find the option whose visible text is "Other"
    let otherOption = select.find("option").filter(function () {
        return $(this).text().trim() === "Other";
    });

    // Update only the value, keep the text as "Other"
    otherOption.attr("value", inputVal || "Other");

    // Optional: Select the updated option
    select.val(inputVal || "Other");

    // Log the currently selected value
    console.log(select.val());
});

//this is for the change on the subcategroy input
$(document).on("input", ".other_subcategory_input_field", function () {
    let inputVal = $(this).val();
    // Find the closest parent with class "select_category_div"
    let parentDiv = $(this).closest(".other_select_subcategory_select_div");

    // Find the <select> inside that parent with class "add_select_category"
    let select = parentDiv.find("select.add_sub_category");

    // Find the option whose visible text is "Other"
    let otherOption = select.find("option").filter(function () {
        return $(this).text().trim() === "Other";
    });
    otherOption.attr("value", inputVal || "Other");

    // Optional: Select the updated option
    select.val(inputVal || "Other");
});

///for the sub category change

function showHideVideoDemo() {
    console.log("here");
    var videoContainer = $("#video_demo");
    var video = videoContainer.find(".video-player")[0];

    if (videoContainer.hasClass("hide")) {
        video.currentTime = 0;
    } else {
        video.pause();
        video.currentTime = 0;
    }

    videoContainer.toggleClass("hide");
}

//for the sample lesson btn
$(document).on("click", "#AddLessonBtn", function () {
    $(".sample_lesson_error").empty();

    var $this = $(this);
    $this.html("<span>+ Add More</span>"); // or any other label
    $this.prop("disabled", false);

    let options = '<option value="" hidden>Select Subjects</option>';
    subjects.forEach(function (subjectArea) {
        options += `<optgroup label="${subjectArea.subject_area}" data-id="${subjectArea.id}">`;
        subjectArea.subjects.forEach(function (item) {
            if (item.title) {
                // Skip if title is empty/null/undefined
                options += `<option value="${item.id}">${item.title}</option>`;
            }
        });
        options += "</optgroup>";
    });

    $(".main_div_subject_lesson").append(`
        <div class="ParentLessonDiv my-5">

            <div class="row">
                <div class="col-md-3 d-flex align-items-center login__form">
                    <select class=" common__login__input  form-select select_subject #004CBD required-field" aria-label="Select Subjects" name="Lessonsubject[]" id="LessonSubject" data-lesson="">
                        ${options}
                    </select>
                </div>

            <div class="col-md-3 d-flex align-items-center login__form">
                    <label class="custom-file-input common__login__input">
                        Upload a file
                        <svg width="17" height="21" viewBox="0 0 17 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.07143 15.6785H10.9286C11.5964 15.6785 12.1429 15.1361 12.1429 14.4732V8.44632H14.0736C15.1543 8.44632 15.7007 7.14452 14.9357 6.38514L9.36214 0.852503C9.24981 0.740761 9.11637 0.652109 8.96947 0.591622C8.82258 0.531135 8.6651 0.5 8.50607 0.5C8.34704 0.5 8.18957 0.531135 8.04267 0.591622C7.89577 0.652109 7.76234 0.740761 7.65 0.852503L2.07643 6.38514C1.31143 7.14452 1.84571 8.44632 2.92643 8.44632H4.85714V14.4732C4.85714 15.1361 5.40357 15.6785 6.07143 15.6785ZM1.21429 18.0893H15.7857C16.4536 18.0893 17 18.6317 17 19.2946C17 19.9576 16.4536 20.5 15.7857 20.5H1.21429C0.546429 20.5 0 19.9576 0 19.2946C0 18.6317 0.546429 18.0893 1.21429 18.0893Z" fill="#6F6C8F" />
                        </svg>

                        <div class="lesson_upload_div">
                            <input type="file" class="lesson_upload required-field" name="lesson_upload[]">
                        </div>
                    </label>
                </div>

                <div class="col-md-4 d-flex align-items-center login__form">
            <textarea
                class="w-100 py-2 lesson_text_area required-field common__login__input"
                name="lessondescription[]"
                placeholder="Enter description or notes for review purposes"
                style="border-radius: 18px; font-size: 15px; border: 1px solid #ccc; outline: none; line-height: 1.5; padding-left: 0.75rem; height: 88px; text-align: left; direction: ltr;"></textarea>

                </div>

                <div class="col-md-1 d-flex justify-content-center align-items-center">
                    <div style="height: 93px; width: 2px; background-color: #ccc;"></div>
                </div>

                <div class="col-md-1 d-flex align-items-center">
                    <svg style="cursor:pointer" class="remove_lesson_subject" width="30px" height="30px" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                        <path fill="#a7a9b7" d="M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64h256zm64 0h192v-64H416v64zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32H192zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32zm192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32z"></path>
                    </svg>
                </div>
            </div>
            <div class="error_div">
                    <span class="file_size_error text-danger ps-2"></span>
                    <span class="file_format_error text-danger ps-2"></span>
                </div>

            <div class="lesson_subject_text_div"></div>

        </div>
    `);
});

$(document).on("change", ".lesson_upload", function () {
    if (this.files.length === 0) {
        return;
    }
    console.log("zdfdfsdsdf");

    const $t = $(this),
        files = this.files;
    const allowed = ["jpg", "jpeg", "png", "pdf", "docx"];
    const $parent = $t.closest(".ParentLessonDiv");

    $parent.find(".file_size_error, .file_format_error").text("");

    for (let f of files) {
        const ext = f.name.split(".").pop().toLowerCase();
        if (f.size > 2 * 1024 * 1024) {
            $parent
                .find(".file_size_error")
                .text("File size exceeds the maximum limit.");
            $parent.find(".lesson_subject_text_div").empty();

            $t.val("");
            return;
        }
        if (!allowed.includes(ext)) {
            $parent
                .find(".file_format_error")
                .text(
                    "The file format is invalid. Please upload a file in one of the supported formats."
                );
            $parent.find(".lesson_subject_text_div").empty();

            $t.val("");
            return;
        }
    }

    // Exit early if no file is selected
    if (!this.files || this.files.length === 0) return;

    const fileName = this.files[0].name;

    // Get the parent element with class ParentLessonDiv
    const parentDiv = $(this).closest(".ParentLessonDiv");

    // Find the child element lesson_subject_text_div within the parent
    const targetDiv = parentDiv.find(".lesson_subject_text_div");
    targetDiv.empty();

    // Define the HTML to append
    const fileSpan = `
        <span class="uploaded-documents lesson_subject_remove_div mt-3 w-100" style="margin-top:2%;width:43%;display:flex;justify-content:space-between; width:fit-content">
            <span class="lesson_file_text" style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap; flex-grow: 1; max-width: 76%; display: inline-block; margin-right: 8px; vertical-align: middle;">
                ${fileName}
            </span>
            <span style="z-index:5;cursor:pointer;" class="ms-2 remove_subject_lesson_cros_icon" title="Remove">
                <svg class="cros_icon_add_cert" style="position:relative;z-index:5;cursor:pointer" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"></path>
                </svg>
            </span>
        </span>`;

    // Append the span to the target div
    targetDiv.append(fileSpan);
});

$(document).on("click", ".remove_subject_lesson_cros_icon", function () {
    const mainparentDiv = $(this).closest(".ParentLessonDiv");
    const fileInput = mainparentDiv.find(".lesson_upload");
    const existinginput = mainparentDiv.find(".existing_input");
    existinginput.val("");
    fileInput.val("");

    //this is for removing the input
    const parentDiv = $(this).closest(".lesson_subject_remove_div");
    parentDiv.remove();
});
$(document).on("click", ".remove_lesson_subject", function () {
    const parentDiv = $(this).closest(".ParentLessonDiv");
    const fileInput = parentDiv.find(".lesson_upload");

    // Log the input element to the console
    console.log(fileInput);

    // Clear the file input
    fileInput.val("");

    // Remove the parent div
    parentDiv.remove();
    if ($(".main_div_subject_lesson").children().length == 0) {
        $("#AddLessonBtn").html("<span>+</span> Add sample lesson");
    }
});

//this is for the truate of the selecct fro sub_subject
function truncateOptions(select) {
    for (let option of select.options) {
        let fullText = option.getAttribute("data-fulltext") || option.text;
        option.setAttribute("data-fulltext", fullText); // Store original
        let words = fullText.trim().split(/\s+/); // Split on whitespace
        option.text =
            words.length > 10 ? words.slice(0, 10).join(" ") + "..." : fullText;
        option.title = fullText; // Show full text on hover
    }
}

$(".subsubject_slect").on("change", function () {
    let selectId = $(this).attr("id");
    if (selectId) {
        truncateOptionsById(selectId);
    }
});

// Run on page load
window.addEventListener("DOMContentLoaded", () => {
    document.querySelectorAll(".subsubject_slect").forEach((select) => {
        truncateOptions(select);

        // Also run on change of each select
        select.addEventListener("change", () => truncateOptions(select));
    });
});

$(document).on("keyup", ".lesson_text_area", function () {
    var $this = $(this);
    var text = $this.val();
    console.log("dfsdfsfsdf");

    // Split by whitespace, filtering out empty strings
    var words = text
        .trim()
        .split(/\s+/)
        .filter(function (word) {
            return word.length > 0;
        });

    if (words.length > 100) {
        // Join first 100 words and preserve trailing space if user is typing
        var trimmedText = words.slice(0, 100).join(" ");
        $this.val(trimmedText);
    }
});

$(document).on("change", "#curriculumOne", function () {
    console.log("sdgsdgsdgsdg");

    $(this).removeClass("error-border");
});

function sanitizeZipCode() {
    let $input = $("#zip_code");
    if($input) {
        let value = $input.val().replace(/\D/g, "").slice(0, 5);
        $input.val(value);
    }
}

// Run once on load (handles pre-filled value)
$(document).ready(function () {
    sanitizeZipCode();
});

// Run on user input
$(document).on("input", "#zip_code", function () {
    sanitizeZipCode();
});

$("body").on("click", "#platformschoolresetpasswordformbtn", function () {
    var loading = $(this).attr("data-loading-text");

    var new_password = $("#newPassword").val();
    var confirm_password = $("#confirmPassword").val();

    // Reset border colors
    $("#tempPassword, #newPassword, #confirmPassword").css("border-color", "#d8dadc");

    var isValid = true;

    if (new_password == "") {
        $("#newPassword").css("border-color", "red");
        isValid = false;
    }
    if (confirm_password == "") {
        $("#confirmPassword").css("border-color", "red");
        isValid = false;
    }

    if (!isValid) return false;
    if (new_password !== confirm_password) {
        $("#confirmPassword").css("border-color", "red");
        alertify.error("New password and confirm password do not match.");
        return false;
    }

    var termsChecked = $("#terms").is(":checked");
    if (!termsChecked) {
        $(".terms_error").text("You must agree to the Terms and Conditions.");
        return false;
    } else {
        $(".terms_error").text("");
    }

    var $this = $(this);
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    var url = APP_URL + "/reset-platform-school-password-frm";
    var formData = $("#platformschoolresetpasswordform").serialize();

    $.ajax({
        type: "POST",
        url: url,
        dataType: "json",
        data: formData,
        beforeSend: function () {
            $this.html(loading);
            $this.prop("disabled", true);
        },
        success: function (data) {
            $(".loader").addClass("d-none");
            if (data.success == true) {
                alertify.success(data.message);
                window.location.href = data.redirect;
                $("#platformschoolresetpasswordform")[0].reset();
            } else {
                alertify.error(data.message);
            }
            $this.html("Change Password");
            $this.prop("disabled", false);
        },
        error: function (xhr) {
            $(".loader").addClass("d-none");
            $this.html("Change Password");
            $this.prop("disabled", false);

            if (xhr.status === 401) {
                alertify.error("Invalid temporary password.");
                $("#tempPassword").css("border-color", "red");
            } else if (xhr.status === 422) {
                const errors = xhr.responseJSON.errors;
                if (errors.temp_password) {
                    $("#tempPassword").css("border-color", "red");
                }
                if (errors.password) {
                    $("#newPassword").css("border-color", "red");
                }
                if (errors.confirm_password) {
                    $("#confirmPassword").css("border-color", "red");
                }
                alertify.error("Validation failed. Please check the fields.");
            } else {
                alertify.error("Something went wrong. Please try again.");
            }
        },
    }).done(function () {
        setTimeout(function () {
            $this.html("Change Password");
            $this.prop("disabled", false);
        }, 500);
    });
});

document.addEventListener('DOMContentLoaded', function () {
    document.body.addEventListener('change', handleSubjectChange);
});

function handleSubjectChange(event) {
    const target = event.target;
    if (target.tagName !== 'SELECT' || !target.id.startsWith('subject_')) return;
    const selectedText = target.options[target.selectedIndex]?.text || '';
    const indexMatch = target.id.match(/subject_(\d+)/);
    if (!indexMatch) return;

    const index = indexMatch[1];
    const hiddenInput = document.getElementById(`subject_area_id_${index}`);
    if (hiddenInput) {
        hiddenInput.value = selectedText;
    }
}

