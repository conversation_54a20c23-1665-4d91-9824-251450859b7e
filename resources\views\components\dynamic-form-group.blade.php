<style>
  body{
    font-family: sans-serif !important;
  }
  input.common__input.w-100.availability {
      padding: 15px;
      border: 2px solid black;
  }
  input.common__input.w-100.availability::placeholder {
      color: #000; /* true black */
      opacity: 1;
  }
  select.form-select.w-auto.contact_phone_country_code {
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      background-image: none !important;
      background: none;
      padding: 10px 20px;
      border: 2px solid black;
      border-radius: 50px;
      margin-right: 15px;
  }
  input.common__input.flex-grow-1.w-100.contact_phone_number {
      padding: 10px 20px;
      border: 2px solid black;
      border-radius: 50px;
  }
</style>
@csrf

@php
  $indexName = isset($index) ? "[{$index}]" : "";
@endphp

<div class="formGroupsContainer">
  <div class="position-relative">
    @foreach ($fields as $field)
      @if ($field['type'] === 'description')
        <div class="mb-3 fs-5 @if ($field['options']['bold'] ?? false) fw-bold @endif">
          <span @if(($field['options']['color'] ?? '') === 'primary') class="text-primary" @endif>
            {{ $field['label'] }}
          </span>
          @if($field['required'])
            <span class="text-danger">*</span>
          @endif
        </div>
        <div class="mb-3 small">{!! $field['description'] !!}</div>

      @elseif ($field['type'] === 'separator')
        <hr class="my-4" />

      @elseif ($field['type'] === 'view')
        @include($field['view'], $field['data'] ?? [])

      @else
        <div class="mb-3">
          @if (!empty($field['label']))
            <label class="form-label">{{ $field['label'] }}</label>
          @endif

          @switch($field['type'])

            @case('text')
              <input type="text"
                name="{{ $field['name'] }}{{ $indexName }}"
                value="{{ old($field['name'], $field['default'] ?? '') }}"
                class="common__input w-100 {{ $field['name'] }}" placeholder="{{ $field['placeholder'] ?? '' }}"
                data-required="{{ $field['required'] ? 'true' : 'false' }}"
                data-rules="{{ implode('|', $field['rules'] ?? []) }}">
              @break

            @case('textarea')
              <textarea name="{{ $field['name'] }}{{ $indexName }}"
                class="common__input w-100"
                data-required="{{ $field['required'] ? 'true' : 'false' }}"
                data-rules="{{ implode('|', $field['rules'] ?? []) }}">{{ old($field['name'], $field['default'] ?? '') }}</textarea>
              @break

            @case('date')
              <input type="text"
                name="{{ $field['name'] }}{{ $indexName }}"
                value="{{ old($field['name'], $field['default'] ?? '') }}"
                class="common__input datepicker w-100"
                data-required="{{ $field['required'] ? 'true' : 'false' }}"
                data-rules="{{ implode('|', $field['rules'] ?? []) }}">
              @break

            @case('time')
              <input type="text"
                name="{{ $field['name'] }}{{ $indexName }}"
                value="{{ old($field['name'], $field['default'] ?? '') }}"
                class="common__input timepicker w-100"
                data-required="{{ $field['required'] ? 'true' : 'false' }}"
                data-rules="{{ implode('|', $field['rules'] ?? []) }}">
              @break

            @case('select')
              <select name="{{ $field['name'] }}{{ $indexName }}"
                class="form-select w-100 select2"
                data-required="{{ $field['required'] ? 'true' : 'false' }}"
                data-rules="{{ implode('|', $field['rules'] ?? []) }}">
                @foreach ($field['options'] ?? [] as $key => $value)
                  <option value="{{ $key }}" @if(old($field['name'], $field['default']) == $key) selected @endif>
                    {{ $value }}
                  </option>
                @endforeach
              </select>
              @break

            @case('checkbox')
              @foreach ($field['options'] ?? [] as $key => $label)
                <div class="form-check">
                  <input type="checkbox"
                    name="{{ $field['name'] }}[{{ $index ?? 0 }}][]"
                    value="{{ $key }}"
                    class="form-check-input"
                    data-required="{{ $field['required'] ? 'true' : 'false' }}"
                    data-rules="{{ implode('|', $field['rules'] ?? []) }}">
                  <label class="form-check-label">{{ $label }}</label>
                </div>
              @endforeach
              @break

            @case('radio')
              <div class="d-flex gap-3 flex-wrap">
                @foreach ($field['options'] ?? [] as $key => $label)
                  <div class="form-check">
                    <input type="radio"
                      name="{{ $field['name'] }}@if($index)[{{ $index ?? 0 }}]@endif"
                      value="{{ $key }}"
                      class="form-check-input"
                      @if(old($field['name'], $field['default'][0]) == $key) checked @endif
                      data-required="{{ $field['required'] ? 'true' : 'false' }}"
                      data-rules="{{ implode('|', $field['rules'] ?? []) }}">
                    <label class="form-check-label">{{ $label }}</label>
                  </div>
                @endforeach
              </div>
              @break

            @case('phone')
              @php
                $indexSuffix = isset($index) ? "[{$index}]" : "";
              @endphp
              <div class="d-flex align-items-center">
                <select name="{{ $field['name'] }}_country_code{{ $indexSuffix }}"
                  class="form-select w-auto {{ $field['name'] }}_country_code"
                  data-required="{{ $field['required'] ? 'true' : 'false' }}"
                  data-rules="required">
                  @foreach ($field['country_codes'] as $code => $label)
                    <option value="{{ $code }}" @if(old($field['name'].'.country_code', $field['default_country_code']) == $code) selected @endif>
                      {{ $label }} {{ $code }}
                    </option>
                  @endforeach
                </select>

                <input type="tel"
                  name="{{ $field['name'] }}_number{{ $indexSuffix }}"
                  value="{{ old($field['name'].'.number', $field['default'] ?? '') }}"
                  class="common__input flex-grow-1 w-100 {{ $field['name'] }}_number"
                  placeholder="Phone number"
                  data-required="{{ $field['required'] ? 'true' : 'false' }}"
                  data-rules="{{ implode('|', $field['rules'] ?? []) }}">
              </div>
              @break

          @endswitch
        </div>
      @endif
    @endforeach
  </div>
</div>
