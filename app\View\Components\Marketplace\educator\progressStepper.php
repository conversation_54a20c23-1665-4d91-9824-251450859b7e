<?php

namespace App\View\Components\Marketplace\Educator;

use Illuminate\View\Component;
use Illuminate\Support\Collection;

class ProgressStepper extends Component
{
    public array $steps;
    public string $modalId;
    public string $modalTitle;
    public string $triggerButtonLabel;
    public bool $useModal;
    public int $progress;
    public int $requiredCount = 0;
    public int $requiredCompletedCount = 0;

    public function __construct(
        array $steps = [],
        string $modalId = 'overviewModal',
        string $modalTitle = 'Setup Steps Overview',
        string $triggerButtonLabel = 'Show Steps',
        bool $useModal = true,
    ) {
        $this->steps = $steps;
        $this->modalId = $modalId;
        $this->modalTitle = $modalTitle;
        $this->triggerButtonLabel = $triggerButtonLabel;
        $this->useModal = $useModal;

        $this->progress = $this->calculateProgress();

    }

    protected function calculateProgress(): int
    {
        $stepsCollection = collect($this->steps);
        $this->requiredCount = $stepsCollection->where('required', true)->count();
        $this->requiredCompletedCount = $stepsCollection->where('required', true)->where('completed', true)->count();

        return $this->requiredCount > 0
            ? (int) round(($this->requiredCompletedCount / $this->requiredCount) * 100)
            : 100;
    }

    public function render()
    {
        return view('components.marketplace.educator.progress-stepper', [
            'steps' => $this->steps,
            'modalId' => $this->modalId,
            'modalTitle' => $this->modalTitle,
            'triggerButtonLabel' => $this->triggerButtonLabel,
            'useModal' => $this->useModal,
            'progress' => $this->progress,
            'requiredCompletedCount' => $this->requiredCompletedCount,
            'requiredCount' => $this->requiredCount,
        ]);
    }
}
