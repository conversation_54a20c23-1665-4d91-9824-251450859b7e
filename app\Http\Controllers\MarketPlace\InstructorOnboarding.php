<?php

namespace App\Http\Controllers\MarketPlace;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use Auth;

use App\Models\v1\SubjectArea;
use App\Models\V1\UserAvailability;
use App\Models\V1\UserAdditionalSubject;
use App\Models\V1\UserNotificationPreference;
use App\Models\V1\UserOnboardingFinalization;

class InstructorOnboarding extends Controller
{

     protected $allowedSteps = [
        'Add Availability',
        'Add More Subjects',
        'Available for substitute requirements',
        'Add Phone Number',
        'Allow Notifications'
    ];
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $userId = Auth::guard('instructor')->user()->id;
        $userOnboardingFinalization = UserOnboardingFinalization::where('user_id', $userId)->first();
        $config = [
            'availability' => [
                'fields' => [
                    [
                        'type' => 'description',
                        'label' => 'Update availability Schedule',
                        'required' => false,
                        'options' => [
                            'color' => 'primary',
                            'bold' => true,
                        ],
                        'description' => 'Update days of week and times you are available during the week',
                    ],
                    [
                        'type' => 'separator',
                    ],
                    [
                        'type' => 'view',
                        'view' => 'marketplace.instructor_onboarding.availability_step',
                        'data' => [
                            'availability' => UserAvailability::where('user_id', $userId)->get(),
                        ],
                        'arrayFields' => [
                            [
                                'name' => 'day_of_week',
                                'type' => 'text',
                                'required' => true,
                            ],
                            [
                                'name' => 'start_time',
                                'type' => 'time',
                                'required' => true,
                            ],
                            [
                                'name' => 'end_time',
                                'type' => 'time',
                                'required' => true,
                            ]
                        ],
                    ],
                    [
                        'type' => 'separator',
                    ],
                    [
                        'type' => 'description',
                        'label' => 'Hours available per week',
                        'required' => true,
                        'options' => [
                            'color' => 'secondary',
                            'bold' => true,
                        ],
                        'description' => 'How many hours are you available to work per week',
                    ],
                    [
                        'name' => 'hours_per_week',
                        'label' => '',
                        'type' => 'text',
                        'required' => true,
                        'default' => $userOnboardingFinalization ? $userOnboardingFinalization->hours_per_week : '',
                        'placeholder' => 'Enter numbers of hours',
                        'rules' => [],
                    ],
                    [
                        'type' => 'separator',
                    ],
                ],
            ],
            'subjects' => [
                'fields' => [
                    [
                        'type' => 'description',
                        'label' => 'Add subjects',
                        'required' => false,
                        'options' => [
                            'color' => 'primary',
                            'bold' => true,
                        ],
                        'description' => 'Add additional subjects you would like to teach. You can add a maximum of 7 subjects at a time.',
                    ],
                    [
                        'name' => 'add_more_subjects',
                        'label' => '',
                        'type' => 'radio',
                        'required' => true,
                        'default' => [$userOnboardingFinalization && $userOnboardingFinalization->add_more_subjects == 1 ? 'yes' : 'no'],
                        'rules' => [],
                        'options' => [
                            'yes' => 'Yes',
                            'no' => 'No',
                        ],
                    ],
                    [
                        'type' => 'view',
                        'view' => 'marketplace.instructor_onboarding.subjects_step',
                        'data' => [
                            'subjectAreas' => SubjectArea::get(),
                            'userSubjects' => UserAdditionalSubject::where('user_id', $userId)->get(),
                        ],
                        'arrayFields' => [
                            [
                                'name' => 'subject_code',
                                'type' => 'text',
                                'required' => true,
                            ],
                            [
                                'name' => 'proficiency',
                                'type' => 'text',
                                'required' => true,
                            ],
                            [
                                'name' => 'lesson_plan_note',
                                'type' => 'text',
                                'required' => false,
                            ],
                            [
                                'name' => 'file_name',
                                'type' => 'text',
                                'required' => false,
                            ],
                            [
                                'name' => 'file_url',
                                'type' => 'text',
                                'required' => false,
                            ],
                        ],
                    ],
                    [
                        'type' => 'description',
                        'label' => '',
                        'required' => false,
                        'description' => "Note:<ul><li>• Each newly added subject will undergo admin review.</li><li>• You will receive a notification once your submission is approved along with pay rates for the subjects.</li></ul>",
                    ],
                    [
                        'type' => 'separator',
                    ]
                ],
            ],
            'substitutes' => [
                'fields' => [
                    [
                        'type' => 'description',
                        'label' => 'Substitute Opportunities',
                        'required' => false,
                        'options' => [
                            'color' => 'primary',
                            'bold' => true,
                        ],
                        'description' => "Let us know if you're open to stepping in as a substitute teacher as and when the need arises.<br><br>This could include: <ul><li>• Short-term classroom assignments</li><li>• Emergency coverage</li><li>• Flexible teaching across different grades/subjects (as per your expertise)</li></ul>",
                    ],
                    [
                        'type' => 'description',
                        'label' => 'Are you open to substitute teaching opportunities?',
                        'required' => false,
                        'options' => [
                            'color' => 'primary',
                            'bold' => false,
                        ],
                        'description' => "",
                    ],
                    [
                        'name' => 'open_to_substitute_opportunity',
                        'label' => '',
                        'type' => 'radio',
                        'required' => false,
                        'default' => [$userOnboardingFinalization && $userOnboardingFinalization->open_to_substitute_opportunity ? 'yes' : 'no'],
                        'rules' => [],
                        'options' => [
                            'yes' => 'Yes',
                            'no' => 'No',
                        ],
                    ],
                    [
                        'type' => 'separator',
                    ]
                ],
            ],
            'phoneNumber' => [
                'fields' => [
                    [
                        'type' => 'description',
                        'label' => 'Add phone number',
                        'required' => false,
                        'options' => [
                            'color' => 'primary',
                            'bold' => true,
                        ],
                        'description' => "Please share the best phone number for communication about ongoing sessions, new opportunities and attendance checks",
                    ],
                    [
                        'type' => 'phone',
                        'name' => 'contact_phone',
                        'label' => "Enter Phone Number*",
                        'required' => true,
                        'rules' => ['required', 'numeric'],
                        'country_codes' => [
                            '+1' => '',
                            // '+91' => 'IN',
                            // '+44' => 'UK',
                        ],
                        'default_country_code' => '+1',
                        'default' => $userOnboardingFinalization ? $userOnboardingFinalization->phone_number : '',
                    ],
                    [
                        'type' => 'separator',
                    ]
                ],
            ],
            'notifications' => [
                'fields' => [
                    [
                        'type' => 'description',
                        'label' => 'Manage notifications',
                        'required' => false,
                        'options' => [
                            'color' => 'primary',
                            'bold' => true,
                        ],
                        'description' => "By enabling notifications, you’ll receive timely updates about your sessions, opportunities, and important actions.",
                    ],
                    [
                        'type' => 'view',
                        'view' => 'marketplace.instructor_onboarding.notifications_step',
                        'data' => [
                            'notificationPreferences' => UserNotificationPreference::where('user_id', $userId)->get(),
                        ],
                        'arrayFields' => [
                            [
                                'name' => 'notification_type',
                                'type' => 'text',
                                'required' => true,
                            ],
                            [
                                'name' => 'in_app_notification',
                                'type' => 'checkbox',
                                'required' => true,
                            ],
                            [
                                'name' => 'email_notification',
                                'type' => 'checkbox',
                                'required' => true,
                            ],
                            [
                                'name' => 'real_time_email_notification',
                                'type' => 'checkbox',
                                'required' => true,
                            ],
                            [
                                'name' => 'daily_summary_email',
                                'type' => 'checkbox',
                                'required' => true,
                            ]
                        ],
                    ],
                    [
                        'type' => 'description',
                        'label' => '',
                        'required' => false,
                        'description' => 'Notes: Notifications for ongoing sessions are essential to ensure quality and uninterrupted service. You can customize your notification preferences later through your settings.',
                    ],
                    [
                        'type' => 'separator',
                    ]
                ],
            ],
        ];

        return view('marketplace.instructor_onboarding.steps', compact('config'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $step = $request->query('step');
        $userId = Auth::guard('instructor')->user()->id;

        if (!$step || !in_array($step, $this->allowedSteps)) {
            throw ValidationException::withMessages([
                'step' => "Invalid or missing onboarding step: '{$step}'"
            ]);
        }

        $data = null;

        DB::transaction(function () use ($request, $step, $userId, &$data) {

            switch ($step) {

                case 'Add Availability':
                    $validated = $request->validate([
                        'availability' => 'required|array',
                        'hours_per_week' => 'required|integer|min:0',
                        'availability.*.id' => 'nullable|integer|exists:user_availability_v1,id',
                        'availability.*.day_of_week' => 'required|in:sunday,monday,tuesday,wednesday,thursday,friday,saturday',
                        'availability.*.start_time' => 'required|date_format:H:i:s',
                        'availability.*.end_time' => 'required|date_format:H:i:s|after:availability.*.start_time',
                    ]);

                    $submittedIds = [];

                    foreach ($validated['availability'] as $slot) {
                        if (isset($slot['id'])) {
                            $existing = UserAvailability::where('user_id', $userId)
                                ->where('id', $slot['id'])
                                ->first();

                            if ($existing) {
                                $existing->update([
                                    'day_of_week' => $slot['day_of_week'],
                                    'start_time' => $slot['start_time'],
                                    'end_time' => $slot['end_time'],
                                ]);
                                $submittedIds[] = $existing->id;
                            }
                        } else {
                            $new = UserAvailability::create([
                                'user_id' => $userId,
                                'day_of_week' => $slot['day_of_week'],
                                'start_time' => $slot['start_time'],
                                'end_time' => $slot['end_time'],
                            ]);
                            $submittedIds[] = $new->id;
                        }
                    }

                    // Remove any slots that weren’t resubmitted (PUT-like)
                    UserAvailability::where('user_id', $userId)
                        ->whereNotIn('id', $submittedIds)
                        ->delete();

                    $finalizationSteps = UserOnboardingFinalization::updateOrCreate(
                        ['user_id' => $userId],
                        [
                            'hours_per_week' => $validated['hours_per_week'],
                        ]
                    );

                    $data = UserAvailability::where('user_id', $userId)->get();
                    break;

                case 'Add More Subjects':

                    $check = $request->validate([
                        'add_more_subjects' => 'required|in:yes,no',
                    ]);
                    $data = UserOnboardingFinalization::updateOrCreate(
                        ['user_id' => $userId],
                        [ 'add_more_subjects' => $check['add_more_subjects'] == 'yes' ? 1 : 0 ]
                    );
                    if($check['add_more_subjects'] === 'yes') {
                        $validated = $request->validate([
                            'subjects' => 'required|array',
                            'subjects.*.subject_code' => 'nullable|string|max:50',
                            'subjects.*.subject_name' => 'nullable|string|max:255',
                            'subjects.*.proficiency' => 'required|integer|between:1,5',
                            'subjects.*.lesson_plan_note' => 'nullable|string',
                            'subjects.*.file_name' => 'nullable|string',
                            'subjects.*.file_url' => 'nullable|string',
                            'subjects.*.status' => 'nullable|in:pending,approved,rejected,accepted,declined',
                        ]);

                        $submittedCodes = [];

                        foreach ($validated['subjects'] as $subject) {
                            UserAdditionalSubject::updateOrCreate(
                                [
                                    'user_id' => $userId,
                                    'subject_code' => $subject['subject_code'],
                                ],
                                [
                                    'subject_name' => $subject['subject_name'],
                                    'proficiency' => $subject['proficiency'],
                                    'lesson_plan_note' => $subject['lesson_plan_note'] ?? null,
                                    'file_name' => $subject['file_name'] ?? null,
                                    'file_url' => $subject['file_url'] ?? null,
                                    'status' => $subject['status'] ?? 'pending',
                                ]
                            );

                            $submittedCodes[] = $subject['subject_code'];
                        }

                        // Remove any subjects not resubmitted
                        UserAdditionalSubject::where('user_id', $userId)
                            ->whereNotIn('subject_code', $submittedCodes)
                            ->delete();

                        $data = UserAdditionalSubject::where('user_id', $userId)->get();
                    } else {
                        $data = [];
                        UserAdditionalSubject::where('user_id', $userId)
                            ->delete();
                    }

                    break;

                case 'Available for substitute requirements':
                    $validated = $request->validate([
                        'open_to_substitute_opportunity' => 'required|in:yes,no',
                    ]);
                    $data = UserOnboardingFinalization::updateOrCreate(
                        ['user_id' => $userId],
                        [ 'open_to_substitute_opportunity' => $validated['open_to_substitute_opportunity'] == 'yes' ? 1 : 0 ]
                    );
                    break;
                case 'Add Phone Number':
                    $validated = $request->validate([
                        'contact_phone_number' => 'required|string|max:20',
                        'contact_phone_country_code' => 'required|string|max:5',
                    ]);
                    $data = UserOnboardingFinalization::updateOrCreate(
                        ['user_id' => $userId],
                        [
                            'phone_number' => $validated['contact_phone_number'],
                            'country_code' => $validated['contact_phone_country_code'],
                        ]
                    );
                    break;
                case 'Allow Notifications':
                    $validated = $request->validate([
                        'notifications' => 'required|array',
                        'notifications.*.notification_type' => 'required|string',
                        'notifications.*.in_app_notification' => 'in:on',
                        'notifications.*.email_notification' => 'in:on',
                        'notifications.*.realtime_email_notification' => 'in:on',
                        'notifications.*.daily_summary_email' => 'in:on',
                    ]);

                    foreach ($validated['notifications'] as $notification) {
                        UserNotificationPreference::updateOrCreate(
                            [
                                'user_id' => $userId,
                                'notification_type' => $notification['notification_type']
                            ],
                            [
                                'in_app_notifications' => isset($notification['in_app_notification']) && $notification['in_app_notification'] == 'on' ? 1 : 0,
                                'email_notifications' => isset($notification['email_notification']) && $notification['email_notification'] == 'on' ? 1 : 0,
                                'realtime_email_notifications' => isset($notification['realtime_email_notification']) && $notification['realtime_email_notification'] == 'on' ? 1 : 0,
                                'daily_summary_emails' => isset($notification['daily_summary_email']) && $notification['daily_summary_email'] == 'on' ? 1 : 0,
                            ]
                        );
                    }

                    $data = UserNotificationPreference::where('user_id', $userId)->get();
                    break;

                case 'onboarding-finalization':
                    $validated = $request->validate([
                        'hours_per_week' => 'required|integer|min:0',
                        'add_more_subjects' => 'required|boolean',
                        'open_to_substitute_opportunity' => 'required|boolean',
                        'phone_number' => 'required|string|max:20',
                        'country_code' => 'required|string|max:5',
                        'progress' => 'required|string|max:255',
                        'status' => 'required|in:incomplete,completed',
                    ]);

                    $data = UserOnboardingFinalization::updateOrCreate(
                        ['user_id' => $userId],
                        $validated
                    );
                    break;

                default:
                    throw ValidationException::withMessages([
                        'step' => "Invalid onboarding step: '{$step}'"
                    ]);
            }

        });

        return response()->json([
            'message' => ucfirst($step) . ' saved successfully.',
            'data' => $data,
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
