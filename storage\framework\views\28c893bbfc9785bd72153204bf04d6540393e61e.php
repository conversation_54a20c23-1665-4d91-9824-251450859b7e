<style>
  .notification-table {
  width: 100%;
  border-collapse: collapse;
  text-align: center;
}

.notification-table th,
.notification-table td {
  padding: 12px 8px;
  vertical-align: middle;
}

.notification-table th {
  font-weight: 600;
}

.notification-table td i {
  margin-left: 8px;
  color: #000000;
}

/* Center the checkbox and checkmark */
.custom-checkbox {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.custom-checkbox input[type="checkbox"] {
  margin: 0;
}

/* Optional: Style checkmark span if you have custom styling */
.checkmark {
  margin-left: 5px;
}
.styled-checkbox {
  appearance: none;
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  border: 2px solid #ccc;
  background-color: #fff;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s, border-color 0.2s;
}

/* Checked State */
.styled-checkbox:checked {
  background-color: #122a81;
  border-color: #122a81;
}

/* Optional checkmark (✓) */
.styled-checkbox:checked::after {
  content: '✓';
  color: #fff;
  font-size: 14px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
<?php
$notificationTypes = [
    'New opportunities',
    'Application Related',
    'Ongoing Sessions',
    'Messages'
];
?>
<table class="notification-table">
  <thead>
    <tr>
      <th></th>
      <th>In-App<br>Notifications</th>
      <th>Email<br>Notifications</th>
      <th>Real Time<br>Email Notifications</th>
      <th>Daily<br>Summary Emails</th>
    </tr>
  </thead>
  <tbody>
    <?php $__currentLoopData = $notificationTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php
    $notification = $notificationPreferences->firstWhere('notification_type', $type);
    ?>
    <tr>
      <td>
        <?php echo e($type); ?>

        <i class="fa fa-info-circle ms-2"></i>
        <input type="hidden" value="<?php echo e($type); ?>"  name="notifications[<?php echo e($key); ?>][notification_type]" class="styled-checkbox">
      </td>
      <td>
        <label class="custom-checkbox">
          <input type="checkbox" <?php if(!$notification || $notification['in_app_notifications'] == 1): ?> checked <?php endif; ?> name="notifications[<?php echo e($key); ?>][in_app_notification]" class="styled-checkbox">
          <span class="checkmark"></span>
        </label>
      </td>
      <td>
        <label class="custom-checkbox">
          <input type="checkbox" checked name="notifications[<?php echo e($key); ?>][email_notification]" class="styled-checkbox">
          <span class="checkmark"></span>
        </label>
      </td>
      <td>
        <label class="custom-checkbox">
          <input type="checkbox" checked name="notifications[<?php echo e($key); ?>][real_time_email_notification]" class="styled-checkbox">
          <span class="checkmark"></span>
        </label>
      </td>
      <td>
        <label class="custom-checkbox">
          <input type="checkbox" checked name="notifications[<?php echo e($key); ?>][daily_summary_email]" class="styled-checkbox">
          <span class="checkmark"></span>
        </label>
      </td>
    </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
  </tbody>
</table>
<?php /**PATH D:\whizara\whizara\resources\views/marketplace/instructor_onboarding/notifications_step.blade.php ENDPATH**/ ?>