<!-- Progress Bar -->
<div class="mb-3">
  <div class="progress" style="height: 8px;">
    <div class="progress-bar bg-primary" style="width: {{ isset($progress) ? $progress : 0 }}%;"></div>
  </div>
  <small class="text-muted d-block mt-1">
    {{ $requiredCompletedCount }} of {{ $requiredCount }} required steps completed
  </small>
</div>

<!-- Steps List -->
<ul class="list-group list-group-flush">
  @foreach($steps as $step)
  <li class="list-group-item d-flex align-items-center" data-step="{{ $step['title'] }}" data-next-button="{{ $step['nextButtonText'] ?? '' }}" data-prev-button="{{ isset($step['hidePrevButton']) ? $step['hidePrevButton'] : 'false' }}">
    @if(isset($step['icon']))
    <i class="{{ $step['icon'] }} fs-4 me-3 text-primary"></i>
    @endif
    <div class="flex-grow-1">
      <div class="fw-medium" data-required="{{$step['required']}}">{{ $step['title'] }}</div>
    </div>
    @if(isset($step['action_link']) || isset($step['btn_click']))
      @if($step['completed'])
        <span class="badge bg-success"> right </span> 
      @else 
        @if(isset($step['action_link']))
        <a href="{{ $step['action_link'] }}" class="btn btn-sm btn-outline-primary ms-3 hover:text-white">
          {{ $step['action_label'] }}
        </a>
        @elseif (isset($step['btn_click']))
        <button class="btn btn-outline-primary ms-3 hover:text-white" data-step="{{ $step['title'] }}" onclick="{{ $step['btn_click'] }}">
          {{ $step['action_label'] }}
        </button>
        @endif
      @endif
    @endif
  </li>
  @endforeach
</ul>
